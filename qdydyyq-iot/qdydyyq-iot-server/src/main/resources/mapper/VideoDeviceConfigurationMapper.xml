<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.thingshive.iot.mapper.VideoDeviceConfigurationMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.thingshive.iot.entity.VideoDeviceConfiguration" id="videoDeviceConfigurationResult">
        <result property="id" column="id"/> <!-- id -->
        <result property="tenantId" column="tenant_id"/> <!-- 租户ID -->
        <result property="hostId" column="host_id"/> <!-- 主机ID -->
        <result property="driveId" column="drive_id"/> <!-- 驱动ID -->
        <result property="deviceId" column="device_id"/> <!-- 设备ID -->
        <result property="protocolTypeCode" column="protocol_type_code"/> <!-- 协议编码 -->
        <result property="protocolTypeName" column="protocol_type_name"/> <!-- 协议名称 -->
        <result property="ip" column="ip"/> <!-- IP -->
        <result property="port" column="port"/> <!-- 端口号 -->
        <result property="userName" column="user_name"/> <!-- 用户名 -->
        <result property="password" column="password"/> <!-- 密码 -->
        <result property="pullUrl" column="pull_url"/> <!-- 取流地址 -->
        <result property="serviceId" column="service_id"/> <!-- 流媒体服务器ID -->
        <result property="serviceName" column="service_name"/> <!-- 流媒体服务器名称 -->
        <result property="pushUrl" column="push_url"/> <!-- 播放地址 -->
        <result property="channelId" column="channel_id"/> <!-- 流通道 -->
        <result property="channelCode" column="channel_code"/> <!-- 流通道 -->
        <result property="nationalStandardId" column="national_standard_id"/> <!-- 国标ID -->
        <result property="nationalStandardName" column="national_standard_name"/> <!-- 国标名称 -->
        <result property="createTime" column="create_time"/> <!-- 创建时间 -->
        <result property="createBy" column="create_by"/> <!-- 创建人 -->
        <result property="createName" column="create_name"/> <!-- 创建人名称 -->
        <result property="updateTime" column="update_time"/> <!-- 修改时间 -->
        <result property="updateBy" column="update_by"/> <!-- 修改人 -->
        <result property="updateName" column="update_name"/> <!-- 修改人名称 -->
        <result property="delFlag" column="del_flag"/> <!-- 删除标识 1-删除，0-正常 -->
        <result property="remark" column="remark"/> <!-- 备注 -->

        <result property="type" column="type"/> <!--设备类型(0:摄像头；1:门禁；2:磁盘主机) -->

        <result property="hostName" column="hostName"/> <!-- 主机名称 -->
        <result property="driveName" column="driveName"/> <!-- 驱动名称 -->
        <result property="deviceName" column="device_name"/> <!-- 设备名称 -->
        <result property="deviceCode" column="device_code"/> <!-- 设备编码 -->
        <result property="productId" column="product_id"/> <!-- 产品ID -->
        <result property="productName" column="product_name"/> <!-- 产品名称 -->
        <result property="productKey" column="product_key"/> <!-- 产品KEY -->
        <result property="relationId" column="relation_id"/> <!--关系ID -->
        <result property="nvrId" column="nvr_id"/> <!--NVRID -->
    </resultMap>

    <sql id="selectVideoDeviceConfigurationVo">
        SELECT
        a.id,
        a.tenant_id,
        a.device_id,
        b.code device_code,
        b.NAME device_name,
        c.id product_id,
        c.`name` product_name,
        c.product_key,
        a.protocol_type_code,
        a.protocol_type_name,
        a.ip,
        a.PORT,
        a.user_name,
        a.PASSWORD,
        a.pull_url,
        a.service_id,
        a.service_name,
        a.push_url,
        a.channel_id,
        a.channel_code,
        a.national_standard_id,
        a.national_standard_name,
        a.create_time,
        a.create_by,
        a.create_name,
        a.update_time,
        a.update_by,
        a.update_name,
        a.del_flag,
        a.host_id,
        a.type,
        tnh.name as hostName,
        <if test="nvrId != null">
            d.id relation_id,
        </if>
        a.remark
        FROM
        th_video_device_configuration a
        INNER JOIN th_iot_device b on a.device_id = b.id
        INNER JOIN th_product c on c.id = b.product_id
        left JOIN th_node_host tnh on a.host_id = tnh.id
        <if test="nvrId != null">
            INNER JOIN th_nvr_device_relation d on d.video_id = a.id AND d.del_flag = '0' AND d.nvr_id = #{nvrId}
        </if>

    </sql>

    <sql id="selectVo">
        SELECT
        a.id,
        a.tenant_id,
        a.device_id,
        b.code device_code,
        b.NAME device_name,
        c.id product_id,
        c.`name` product_name,
        a.protocol_type_code,
        a.protocol_type_name,
        a.ip,
        a.PORT,
        a.user_name,
        a.PASSWORD,
        a.pull_url,
        a.service_id,
        a.service_name,
        a.push_url,
        a.channel_id,
        a.channel_code,
        a.national_standard_id,
        a.national_standard_name,
        a.create_time,
        a.create_by,
        a.create_name,
        a.update_time,
        a.update_by,
        a.update_name,
        a.del_flag,
        a.host_id,
        a.type,
        tnh.name as hostName,
        a.remark
        FROM
        th_video_device_configuration a
        INNER JOIN th_iot_device b on a.device_id = b.id
        INNER JOIN th_product c on c.id = b.product_id
        left JOIN th_node_host tnh on a.host_id = tnh.id

    </sql>
    <!-- 查询对象List -->
    <select id="selectVideoDeviceConfigurationList" parameterType="VideoDeviceConfiguration"
            resultMap="videoDeviceConfigurationResult">
        <include refid="selectVideoDeviceConfigurationVo"/>
        <where>
            <include refid="equal"/>
        </where>
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectVideoDeviceConfigurationListByLike" parameterType="VideoDeviceConfiguration"
            resultMap="videoDeviceConfigurationResult">
        <include refid="selectVideoDeviceConfigurationVo"/>
        <where>
            <include refid="like"/>
        </where>
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectVideoDeviceConfigurationById" parameterType="Integer" resultMap="videoDeviceConfigurationResult">
        <include refid="selectVo"/>
        where a.id = #{id}
    </select>

    <update id="updateVideoDeviceConfiguration" parameterType="VideoDeviceConfiguration">
        update th_video_device_configuration
        <trim prefix="SET" suffixOverrides=",">
            <if test="tenantId != null  ">
                tenant_id = #{tenantId},
            </if>
            <if test="deviceId != null  ">
                device_id = #{deviceId},
            </if>
            <if test="hostId != null">
                host_id = #{hostId},
            </if>
            <if test="driveId != null">
                drive_id = #{driveId},
            </if>
            <if test="protocolTypeCode != null  ">
                protocol_type_code = #{protocolTypeCode},
            </if>
            <if test="protocolTypeName != null  ">
                protocol_type_name = #{protocolTypeName},
            </if>
            <if test="ip != null  and ip != ''  ">
                ip = #{ip},
            </if>
            <if test="port != null  ">
                port = #{port},
            </if>
            <if test="userName != null  and userName != ''  ">
                user_name = #{userName},
            </if>
            <if test="password != null  and password != ''  ">
                password = #{password},
            </if>
            <if test="pullUrl != null  and pullUrl != ''  ">
                pull_url = #{pullUrl},
            </if>
            <if test="serviceId != null  ">
                service_id = #{serviceId},
            </if>
            <if test="serviceName != null  and serviceName != ''  ">
                service_name = #{serviceName},
            </if>
            <if test="pushUrl != null  and pushUrl != ''  ">
                push_url = #{pushUrl},
            </if>
            <if test="channelId != null  and channelId != ''  ">
                channel_id = #{channelId},
            </if>
            <if test="channelCode != null  and channelCode != ''  ">
                channel_code = #{channelCode},
            </if>
            <if test="nationalStandardId != null  ">
                national_standard_id = #{nationalStandardId},
            </if>
            <if test="nationalStandardName != null  and nationalStandardName != ''  ">
                national_standard_name = #{nationalStandardName},
            </if>
            <if test="createTime != null  ">
                create_time = #{createTime},
            </if>
            <if test="createBy != null  ">
                create_by = #{createBy},
            </if>
            <if test="createName != null  and createName != ''  ">
                create_name = #{createName},
            </if>
            <if test="updateTime != null  ">
                update_time = #{updateTime},
            </if>
            <if test="updateBy != null  ">
                update_by = #{updateBy},
            </if>
            <if test="updateName != null  and updateName != ''  ">
                update_name = #{updateName},
            </if>
            <if test="delFlag != null  and delFlag != ''  ">
                del_flag = #{delFlag},
            </if>
            <if test="type != null  and type != '' ">
                type = #{type},
            </if>
            remark = #{remark},
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVideoDeviceConfigurationById" parameterType="Integer">
        update th_video_device_configuration set del_flag = '1' where id = #{id}
    </delete>

    <!-- 新增对象 -->
    <insert id="insertVideoDeviceConfiguration" parameterType="VideoDeviceConfiguration" useGeneratedKeys="true"
            keyProperty="id">
        insert into th_video_device_configuration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tenantId != null  ">tenant_id,
            </if>
            <if test="deviceId != null  ">device_id,
            </if>
            <if test="hostId != null">host_id,
            </if>
            <if test="driveId != null">drive_id,
            </if>
            <if test="protocolTypeCode != null  ">protocol_type_code,
            </if>
            <if test="protocolTypeName != null  ">protocol_type_name,
            </if>
            <if test="ip != null  and ip != ''  ">ip,
            </if>
            <if test="port != null  ">port,
            </if>
            <if test="userName != null  and userName != ''  ">user_name,
            </if>
            <if test="password != null  and password != ''  ">password,
            </if>
            <if test="pullUrl != null  and pullUrl != ''  ">pull_url,
            </if>
            <if test="serviceId != null  ">service_id,
            </if>
            <if test="serviceName != null  and serviceName != ''  ">service_name,
            </if>
            <if test="pushUrl != null  and pushUrl != ''  ">push_url,
            </if>
            <if test="channelId != null  and channelId != ''  ">
                channel_id ,
            </if>
            <if test="channelCode != null  and channelCode != ''  ">
                channel_code ,
            </if>
            <if test="nationalStandardId != null  ">national_standard_id,
            </if>
            <if test="nationalStandardName != null  and nationalStandardName != ''  ">national_standard_name,
            </if>
            <if test="createTime != null  ">create_time,
            </if>
            <if test="createBy != null  ">create_by,
            </if>
            <if test="createName != null  and createName != ''  ">create_name,
            </if>
            <if test="updateTime != null  ">update_time,
            </if>
            <if test="updateBy != null  ">update_by,
            </if>
            <if test="updateName != null  and updateName != ''  ">update_name,
            </if>
            <if test="delFlag != null  and delFlag != ''  ">del_flag,
            </if>
            <if test="remark != null  and remark != ''  ">remark,
            </if>
            <if test="type != null  and type != '' ">type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tenantId != null  ">#{tenantId},
            </if>
            <if test="deviceId != null  ">#{deviceId},
            </if>
            <if test="hostId != null">#{hostId},
            </if>
            <if test="driveId != null">#{driveId},
            </if>
            <if test="protocolTypeCode != null  ">#{protocolTypeCode},
            </if>
            <if test="protocolTypeName != null  ">#{protocolTypeName},
            </if>
            <if test="ip != null  and ip != ''  ">#{ip},
            </if>
            <if test="port != null  ">#{port},
            </if>
            <if test="userName != null  and userName != ''  ">#{userName},
            </if>
            <if test="password != null  and password != ''  ">#{password},
            </if>
            <if test="pullUrl != null  and pullUrl != ''  ">#{pullUrl},
            </if>
            <if test="serviceId != null  ">#{serviceId},
            </if>
            <if test="serviceName != null  and serviceName != ''  ">#{serviceName},
            </if>
            <if test="pushUrl != null  and pushUrl != ''  ">#{pushUrl},
            </if>
            <if test="channelId != null  and channelId != ''  ">
                 #{channelId},
            </if>
            <if test="channelCode != null  and channelCode != ''  ">
                 #{channelCode},
            </if>
            <if test="nationalStandardId != null  ">#{nationalStandardId},
            </if>
            <if test="nationalStandardName != null  and nationalStandardName != ''  ">#{nationalStandardName},
            </if>
            <if test="createTime != null  ">#{createTime},
            </if>
            <if test="createBy != null  ">#{createBy},
            </if>
            <if test="createName != null  and createName != ''  ">#{createName},
            </if>
            <if test="updateTime != null  ">#{updateTime},
            </if>
            <if test="updateBy != null  ">#{updateBy},
            </if>
            <if test="updateName != null  and updateName != ''  ">#{updateName},
            </if>
            <if test="delFlag != null  and delFlag != ''  ">#{delFlag},
            </if>
            <if test="remark != null  and remark != ''  ">#{remark},
            </if>
            <if test="type != null  and type != '' ">#{type},
            </if>
        </trim>
    </insert>

    <!-- 表字段 -->
    <sql id="column">
        id, <!-- id -->
        tenant_id, <!-- 租户ID -->
        device_id, <!-- 设备ID -->
        protocol_type_code, <!-- 协议编码 -->
        protocol_type_name, <!-- 协议名称 -->
        ip, <!-- IP -->
        port, <!-- 端口号 -->
        user_name, <!-- 用户名 -->
        password, <!-- 密码 -->
        pull_url, <!-- 取流地址 -->
        service_id, <!-- 流媒体服务器ID -->
        service_name, <!-- 流媒体服务器名称 -->
        push_url, <!-- 播放地址 -->
        channel_id, <!-- 流通道 -->
        channel_code, <!-- 流通道 -->
        national_standard_id, <!-- 国标ID -->
        national_standard_name, <!-- 国标名称 -->
        create_time, <!-- 创建时间 -->
        create_by, <!-- 创建人 -->
        create_name, <!-- 创建人名称 -->
        update_time, <!-- 修改时间 -->
        update_by, <!-- 修改人 -->
        update_name, <!-- 修改人名称 -->
        del_flag, <!-- 删除标识 1-删除，0-正常 -->
        remark, <!-- 备注 -->
        type<!-- 设备类型(0:摄像头；1:门禁；2:磁盘主机) -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="id != null ">
            AND a.id = #{id}  <!-- id -->
        </if>
        <if test="tenantId != null ">
            AND a.tenant_id = #{tenantId}  <!-- 租户ID -->
        </if>
        <if test="hostId != null ">
            AND a.host_id = #{hostId}  <!-- 主机ID -->
        </if>
        <if test="driveId != null ">
            AND a.drive_id = #{driveId}  <!-- 驱动ID -->
        </if>
        <if test="deviceId != null ">
            AND a.device_id = #{deviceId}  <!-- 设备ID -->
        </if>
        <if test="protocolTypeCode != null ">
            AND a.protocol_type_code = #{protocolTypeCode}  <!-- 协议编码 -->
        </if>
        <if test="protocolTypeName != null ">
            AND a.protocol_type_name = #{protocolTypeName}  <!-- 协议名称 -->
        </if>
        <if test="ip != null and ip != ''">
            AND a.ip = #{ip}  <!-- IP -->
        </if>
        <if test="port != null ">
            AND a.port = #{port}  <!-- 端口号 -->
        </if>
        <if test="userName != null and userName != ''">
            AND a.user_name = #{userName}  <!-- 用户名 -->
        </if>
        <if test="password != null and password != ''">
            AND a.password = #{password}  <!-- 密码 -->
        </if>
        <if test="pullUrl != null and pullUrl != ''">
            AND a.pull_url = #{pullUrl}  <!-- 取流地址 -->
        </if>
        <if test="serviceId != null ">
            AND a.service_id = #{serviceId}  <!-- 流媒体服务器ID -->
        </if>
        <if test="serviceName != null and serviceName != ''">
            AND a.service_name = #{serviceName}  <!-- 流媒体服务器名称 -->
        </if>
        <if test="pushUrl != null and pushUrl != ''">
            AND a.push_url = #{pushUrl}  <!-- 播放地址 -->
        </if>
        <if test="channelId != null  and channelId != ''  ">
            and a.channel_id = #{channelId},
        </if>
        <if test="channelCode != null  and channelCode != ''  ">
            and a.channel_code = #{channelCode},
        </if>
        <if test="nationalStandardId != null ">
            AND a.national_standard_id = #{nationalStandardId}  <!-- 国标ID -->
        </if>
        <if test="nationalStandardName != null and nationalStandardName != ''">
            AND a.national_standard_name = #{nationalStandardName}  <!-- 国标名称 -->
        </if>
        <if test="createTime != null ">
            AND a.create_time = #{createTime}  <!-- 创建时间 -->
        </if>
        <if test="createBy != null ">
            AND a.create_by = #{createBy}  <!-- 创建人 -->
        </if>
        <if test="createName != null and createName != ''">
            AND a.create_name = #{createName}  <!-- 创建人名称 -->
        </if>
        <if test="updateTime != null ">
            AND a.update_time = #{updateTime}  <!-- 修改时间 -->
        </if>
        <if test="updateBy != null ">
            AND a.update_by = #{updateBy}  <!-- 修改人 -->
        </if>
        <if test="updateName != null and updateName != ''">
            AND a.update_name = #{updateName}  <!-- 修改人名称 -->
        </if>
        <if test="remark != null and remark != ''">
            AND a.remark = #{remark}  <!-- 备注 -->
        </if>
        <if test="productId != null ">
            AND c.id = #{productId}  <!-- 产品ID -->
        </if>
        <if test="productName != null and productName != '' ">
            AND c.name = #{productName}    <!-- 产品名称 -->
        </if>
        <if test="deviceName != null and deviceName != '' ">
            AND b.name = #{deviceName}    <!-- 设备名称 -->
        </if>
        <if test="deviceCode != null and deviceCode != '' ">
            AND b.code = #{deviceCode}    <!-- 设备编码 -->
        </if>
        <if test="type != null  and type != '' ">
            and a.type = #{type}
        </if>
        <if test="notNvrId != null">
            AND NOT EXISTS (
            SELECT
            1
            FROM
            th_nvr_device_relation d
            WHERE
            d.video_id = a.id
            AND d.nvr_id = #{notNvrId}
            AND d.del_flag = '0'
            )
        </if>
        AND a.del_flag = '0'
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <if test="id != null ">
            AND a.id = #{id}  <!-- id -->
        </if>
        <if test="tenantId != null ">
            AND a.tenant_id = #{tenantId}  <!-- 租户ID -->
        </if>
        <if test="deviceId != null ">
            AND a.device_id = #{deviceId}  <!-- 设备ID -->
        </if>
        <if test="protocolTypeCode != null ">
            AND a.protocol_type_code = #{protocolTypeCode}  <!-- 协议编码 -->
        </if>
        <if test="protocolTypeName != null ">
            AND a.protocol_type_name = #{protocolTypeName}  <!-- 协议名称 -->
        </if>
        <if test="ip != null and ip != ''">
            AND a.ip like '%${ip}%'  <!-- IP -->
        </if>
        <if test="port != null ">
            AND a.port = #{port}  <!-- 端口号 -->
        </if>
        <if test="userName != null and userName != ''">
            AND a.user_name like '%${userName}%'  <!-- 用户名 -->
        </if>
        <if test="password != null and password != ''">
            AND a.password like '%${password}%'  <!-- 密码 -->
        </if>
        <if test="pullUrl != null and pullUrl != ''">
            AND a.pull_url like '%${pullUrl}%'  <!-- 取流地址 -->
        </if>
        <if test="serviceId != null ">
            AND a.service_id = #{serviceId}  <!-- 流媒体服务器ID -->
        </if>
        <if test="serviceName != null and serviceName != ''">
            AND a.service_name like '%${serviceName}%'  <!-- 流媒体服务器名称 -->
        </if>
        <if test="pushUrl != null and pushUrl != ''">
            AND a.push_url like '%${pushUrl}%'  <!-- 播放地址 -->
        </if>
        <if test="channelId != null  and channelId != ''  ">
            and a.channel_id = #{channelId},
        </if>
        <if test="channelCode != null  and channelCode != ''  ">
            and a.channel_code = #{channelCode},
        </if>
        <if test="nationalStandardId != null ">
            AND a.national_standard_id = #{nationalStandardId}  <!-- 国标ID -->
        </if>
        <if test="nationalStandardName != null and nationalStandardName != ''">
            AND a.national_standard_name like '%${nationalStandardName}%'  <!-- 国标名称 -->
        </if>
        <if test="createTime != null ">
            AND a.create_time = #{createTime}  <!-- 创建时间 -->
        </if>
        <if test="createBy != null ">
            AND a.create_by = #{createBy}  <!-- 创建人 -->
        </if>
        <if test="createName != null and createName != ''">
            AND a.create_name like '%${createName}%'  <!-- 创建人名称 -->
        </if>
        <if test="updateTime != null ">
            AND a.update_time = #{updateTime}  <!-- 修改时间 -->
        </if>
        <if test="updateBy != null ">
            AND a.update_by = #{updateBy}  <!-- 修改人 -->
        </if>
        <if test="updateName != null and updateName != ''">
            AND a.update_name like '%${updateName}%'  <!-- 修改人名称 -->
        </if>
        <if test="remark != null and remark != ''">
            AND a.remark like '%${remark}%'  <!-- 备注 -->
        </if>

        <if test="productId != null ">
            AND c.id = #{productId}  <!-- 产品ID -->
        </if>
        <if test="productName != null and productName != '' ">
            AND c.name like '%${productName}%'   <!-- 产品名称 -->
        </if>
        <if test="deviceName != null and deviceName != '' ">
            AND b.name like '%${deviceName}%'    <!-- 设备名称 -->
        </if>
        <if test="deviceCode != null and deviceCode != '' ">
            AND b.code like '%${deviceCode}%'    <!-- 设备编码 -->
        </if>
        <if test="type != null  and type != '' ">
            and a.type = #{type}
        </if>
        <if test="notNvrId != null">
            AND NOT EXISTS (
            SELECT
            1
            FROM
            th_nvr_device_relation d
            WHERE
            d.video_id = a.id
            AND d.nvr_id = #{notNvrId}
            AND d.del_flag = '0'
            )
        </if>
        AND a.del_flag ='0'
        ORDER BY a.update_time DESC
    </sql>
</mapper>

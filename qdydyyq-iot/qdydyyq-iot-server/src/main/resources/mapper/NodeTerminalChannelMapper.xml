<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.thingshive.iot.mapper.NodeTerminalChannelMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.thingshive.iot.entity.NodeTerminalChannel" id="nodeTerminalChannelResult">
        <result property="id" column="id"/> <!--  -->
        <result property="tenantId" column="tenant_id"/> <!-- 租户id -->
        <result property="terminalId" column="terminal_id"/> <!-- 终端ID -->
        <result property="name" column="name"/> <!-- 渠道名称 -->
        <result property="mode" column="mode"/> <!-- 传输模式(RTU;TCP:HTTP) -->
        <result property="modeName" column="mode_name"/> <!-- 传输模式 -->
        <result property="ipAddress" column="ip_address"/> <!-- IP地址 -->
        <result property="port" column="port"/> <!-- 端口 -->
        <result property="serialPort" column="serial_port"/> <!-- 串口 -->
        <result property="baudRate" column="baud_rate"/> <!-- 波特率 -->
        <result property="dataBits" column="data_bits"/> <!-- 数据位 -->
        <result property="checkBit" column="check_bit"/> <!-- 校验位 -->
        <result property="stopBit" column="stop_bit"/> <!-- 停止位 -->
        <result property="methodName" column="method_name"/> <!-- 解析方法名 -->
        <result property="createTime" column="create_time"/> <!-- 创建时间 -->
        <result property="createName" column="create_name"/> <!-- 创建人 -->
        <result property="createBy" column="create_by"/> <!-- 创建人 -->
        <result property="updateTime" column="update_time"/> <!-- 修改时间 -->
        <result property="updateBy" column="update_by"/> <!-- 更新人 -->
        <result property="delFlag" column="del_flag"/> <!-- 删除标识 0-正常，1-删除 -->
        <result property="remark" column="remark"/> <!-- 备注 -->
    </resultMap>

    <sql id="selectNodeTerminalChannelVo">
        select id, tenant_id, terminal_id, name, mode, mode_name, ip_address, port, serial_port, baud_rate, data_bits,
        check_bit, stop_bit, method_name, create_time, create_name, create_by, update_time, update_by, del_flag, remark
        from th_node_terminal_channel
    </sql>
    <!-- 查询对象List -->
    <select id="selectNodeTerminalChannelList" parameterType="NodeTerminalChannel"
            resultMap="nodeTerminalChannelResult">
        <include refid="selectNodeTerminalChannelVo"/>
        <where>
            <include refid="equal"/>
        </where>
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectNodeTerminalChannelListByLike" parameterType="NodeTerminalChannel"
            resultMap="nodeTerminalChannelResult">
        <include refid="selectNodeTerminalChannelVo"/>
        <where>
            <include refid="like"/>
        </where>
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectNodeTerminalChannelById" parameterType="Integer" resultMap="nodeTerminalChannelResult">
        <include refid="selectNodeTerminalChannelVo"/>
        where id = #{id}
    </select>

    <update id="updateNodeTerminalChannel" parameterType="NodeTerminalChannel">
        update th_node_terminal_channel
        <trim prefix="SET" suffixOverrides=",">
            <if test="tenantId != null  ">
                tenant_id = #{tenantId},
            </if>
            <if test="terminalId != null  ">
                terminal_id = #{terminalId},
            </if>
            <if test="name != null  and name != ''  ">
                name = #{name},
            </if>
            <if test="mode != null  and mode != ''  ">
                mode = #{mode},
            </if>
            <if test="modeName != null  and modeName != ''  ">
                mode_name = #{modeName},
            </if>
            <if test="ipAddress != null  and ipAddress != ''  ">
                ip_address = #{ipAddress},
            </if>
            <if test="port != null  and port != ''  ">
                port = #{port},
            </if>
            <if test="serialPort != null  and serialPort != ''  ">
                serial_port = #{serialPort},
            </if>
            <if test="baudRate != null  ">
                baud_rate = #{baudRate},
            </if>
            <if test="dataBits != null  ">
                data_bits = #{dataBits},
            </if>
            <if test="checkBit != null  ">
                check_bit = #{checkBit},
            </if>
            <if test="stopBit != null  ">
                stop_bit = #{stopBit},
            </if>
            <if test="methodName != null and methodName != '' ">
                method_name = #{methodName},
            </if>
            <if test="createTime != null  ">
                create_time = #{createTime},
            </if>
            <if test="createName != null  and createName != ''  ">
                create_name = #{createName},
            </if>
            <if test="createBy != null  ">
                create_by = #{createBy},
            </if>
            <if test="updateTime != null  ">
                update_time = #{updateTime},
            </if>
            <if test="updateBy != null  ">
                update_by = #{updateBy},
            </if>
            <if test="delFlag != null  and delFlag != ''  ">
                del_flag = #{delFlag},
            </if>
            remark = #{remark},
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNodeTerminalChannelById" parameterType="Integer">
        update th_node_terminal_channel set del_flag = '1' where id = #{id}
    </delete>

    <!-- 新增对象 -->
    <insert id="insertNodeTerminalChannel" parameterType="NodeTerminalChannel" useGeneratedKeys="true" keyProperty="id">
        insert into th_node_terminal_channel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tenantId != null  ">tenant_id,
            </if>
            <if test="terminalId != null  ">terminal_id,
            </if>
            <if test="name != null  and name != ''  ">name,
            </if>
            <if test="mode != null  and mode != ''  ">mode,
            </if>
            <if test="modeName != null  and modeName != ''  ">mode_name,
            </if>
            <if test="ipAddress != null  and ipAddress != ''  ">ip_address,
            </if>
            <if test="port != null  and port != ''  ">port,
            </if>
            <if test="serialPort != null  and serialPort != ''  ">serial_port,
            </if>
            <if test="baudRate != null  ">baud_rate,
            </if>
            <if test="dataBits != null  ">data_bits,
            </if>
            <if test="checkBit != null  ">check_bit,
            </if>
            <if test="stopBit != null  ">stop_bit,
            </if>
            <if test="methodName != null and methodName != '' ">method_name,
            </if>
            <if test="createTime != null  ">create_time,
            </if>
            <if test="createName != null  and createName != ''  ">create_name,
            </if>
            <if test="createBy != null  ">create_by,
            </if>
            <if test="updateTime != null  ">update_time,
            </if>
            <if test="updateBy != null  ">update_by,
            </if>
            <if test="delFlag != null  and delFlag != ''  ">del_flag,
            </if>
            <if test="remark != null  and remark != ''  ">remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tenantId != null  ">#{tenantId},
            </if>
            <if test="terminalId != null  ">#{terminalId},
            </if>
            <if test="name != null  and name != ''  ">#{name},
            </if>
            <if test="mode != null  and mode != ''  ">#{mode},
            </if>
            <if test="modeName != null  and modeName != ''  ">#{modeName},
            </if>
            <if test="ipAddress != null  and ipAddress != ''  ">#{ipAddress},
            </if>
            <if test="port != null  and port != ''  ">#{port},
            </if>
            <if test="serialPort != null  and serialPort != ''  ">#{serialPort},
            </if>
            <if test="baudRate != null  ">#{baudRate},
            </if>
            <if test="dataBits != null  ">#{dataBits},
            </if>
            <if test="checkBit != null  ">#{checkBit},
            </if>
            <if test="stopBit != null  ">#{stopBit},
            </if>
            <if test="methodName != null and methodName != '' ">#{methodName},
            </if>
            <if test="createTime != null  ">#{createTime},
            </if>
            <if test="createName != null  and createName != ''  ">#{createName},
            </if>
            <if test="createBy != null  ">#{createBy},
            </if>
            <if test="updateTime != null  ">#{updateTime},
            </if>
            <if test="updateBy != null  ">#{updateBy},
            </if>
            <if test="delFlag != null  and delFlag != ''  ">#{delFlag},
            </if>
            <if test="remark != null  and remark != ''  ">#{remark},
            </if>
        </trim>
    </insert>

    <!-- 表字段 -->
    <sql id="column">
        id, <!--  -->
        tenant_id, <!-- 租户id -->
        terminal_id, <!-- 终端ID -->
        name, <!-- 渠道名称 -->
        mode, <!-- 传输模式(RTU;TCP) -->
        mode_name, <!-- 传输模式 -->
        ip_address, <!-- IP地址 -->
        port, <!-- 端口 -->
        serial_port, <!-- 串口 -->
        baud_rate, <!-- 波特率 -->
        data_bits, <!--  -->
        check_bit, <!-- 校验位 -->
        stop_bit, <!-- 停止位 -->
        create_time, <!-- 创建时间 -->
        create_name, <!-- 创建人 -->
        create_by, <!-- 创建人 -->
        update_time, <!-- 修改时间 -->
        update_by, <!-- 更新人 -->
        del_flag, <!-- 删除标识 0-正常，1-删除 -->
        remark <!-- 备注 -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="id != null ">
            AND id = #{id}  <!--  -->
        </if>
        <if test="tenantId != null ">
            AND tenant_id = #{tenantId}  <!-- 租户id -->
        </if>
        <if test="terminalId != null ">
            AND terminal_id = #{terminalId}  <!-- 终端ID -->
        </if>
        <if test="name != null and name != ''">
            AND name = #{name}  <!-- 渠道名称 -->
        </if>
        <if test="mode != null and mode != ''">
            AND mode = #{mode}  <!-- 传输模式(RTU;TCP;HTTP) -->
        </if>
        <if test="modeName != null and modeName != ''">
            AND mode_name = #{modeName}  <!-- 传输模式 -->
        </if>
        <if test="ipAddress != null and ipAddress != ''">
            AND ip_address = #{ipAddress}  <!-- IP地址 -->
        </if>
        <if test="port != null and port != ''">
            AND port = #{port}  <!-- 端口 -->
        </if>
        <if test="serialPort != null and serialPort != ''">
            AND serial_port = #{serialPort}  <!-- 串口 -->
        </if>
        <if test="baudRate != null ">
            AND baud_rate = #{baudRate}  <!-- 波特率 -->
        </if>
        <if test="dataBits != null ">
            AND data_bits = #{dataBits}  <!--  -->
        </if>
        <if test="checkBit != null ">
            AND check_bit = #{checkBit}  <!-- 校验位 -->
        </if>
        <if test="stopBit != null ">
            AND stop_bit = #{stopBit}  <!-- 停止位 -->
        </if>
        <if test="methodName != null and methodName != '' ">
            AND method_name = #{methodName}
        </if>
        <if test="createTime != null ">
            AND create_time = #{createTime}  <!-- 创建时间 -->
        </if>
        <if test="createName != null and createName != ''">
            AND create_name = #{createName}  <!-- 创建人 -->
        </if>
        <if test="createBy != null ">
            AND create_by = #{createBy}  <!-- 创建人 -->
        </if>
        <if test="updateTime != null ">
            AND update_time = #{updateTime}  <!-- 修改时间 -->
        </if>
        <if test="updateBy != null ">
            AND update_by = #{updateBy}  <!-- 更新人 -->
        </if>
        <if test="remark != null and remark != ''">
            AND remark = #{remark}  <!-- 备注 -->
        </if>
        AND del_flag = '0'
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <if test="id != null ">
            AND id = #{id}  <!--  -->
        </if>
        <if test="tenantId != null ">
            AND tenant_id = #{tenantId}  <!-- 租户id -->
        </if>
        <if test="terminalId != null ">
            AND terminal_id = #{terminalId}  <!-- 终端ID -->
        </if>
        <if test="name != null and name != ''">
            AND name like '%${name}%'  <!-- 渠道名称 -->
        </if>
        <if test="mode != null and mode != ''">
            AND mode like '%${mode}%'  <!-- 传输模式(RTU;TCP;HTTP) -->
        </if>
        <if test="modeName != null and modeName != ''">
            AND mode_name like '%${modeName}%'  <!-- 传输模式 -->
        </if>
        <if test="ipAddress != null and ipAddress != ''">
            AND ip_address like '%${ipAddress}%'  <!-- IP地址 -->
        </if>
        <if test="port != null and port != ''">
            AND port like '%${port}%'  <!-- 端口 -->
        </if>
        <if test="serialPort != null and serialPort != ''">
            AND serial_port like '%${serialPort}%'  <!-- 串口 -->
        </if>
        <if test="baudRate != null ">
            AND baud_rate = #{baudRate}  <!-- 波特率 -->
        </if>
        <if test="dataBits != null ">
            AND data_bits = #{dataBits}  <!--  -->
        </if>
        <if test="checkBit != null ">
            AND check_bit = #{checkBit}  <!-- 校验位 -->
        </if>
        <if test="stopBit != null ">
            AND stop_bit = #{stopBit}  <!-- 停止位 -->
        </if>
        <if test="methodName != null and methodName != '' ">
            AND method_name = #{methodName}
        </if>
        <if test="createTime != null ">
            AND create_time = #{createTime}  <!-- 创建时间 -->
        </if>
        <if test="createName != null and createName != ''">
            AND create_name like '%${createName}%'  <!-- 创建人 -->
        </if>
        <if test="createBy != null ">
            AND create_by = #{createBy}  <!-- 创建人 -->
        </if>
        <if test="updateTime != null ">
            AND update_time = #{updateTime}  <!-- 修改时间 -->
        </if>
        <if test="updateBy != null ">
            AND update_by = #{updateBy}  <!-- 更新人 -->
        </if>
        <if test="remark != null and remark != ''">
            AND remark like '%${remark}%'  <!-- 备注 -->
        </if>
        AND del_flag ='0'
        ORDER BY update_time DESC
    </sql>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.thingshive.iot.mapper.GatewayMonitorProductMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.thingshive.iot.entity.GatewayMonitorProduct" id="gatewayMonitorProductResult">
        <result property="id" column="id"/> <!--  -->
        <result property="monitorId" column="monitor_id"/> <!-- 触发器ID -->
        <result property="productId" column="product_id"/> <!-- 产品Id -->
        <result property="productKey" column="product_key"/> <!-- 产品key -->
        <result property="delFlag" column="del_flag"/> <!-- 删除标识 0-正常，1-删除 -->
        <result property="remark" column="remark"/> <!-- 备注 -->
        <result property="createTime" column="create_time"/> <!-- 创建时间 -->
        <result property="createBy" column="create_by"/> <!-- 创建人 -->
        <result property="createName" column="create_name"/> <!-- 创建人 -->
        <result property="updateTime" column="update_time"/> <!-- 修改时间 -->
        <result property="updateBy" column="update_by"/> <!-- 更新人 -->

        <result property="productName" column="productName"/> <!-- 产品名称 -->
        <result property="productModel" column="product_model"/> <!-- 产品型号 -->
        <result property="manufacturer" column="manufacturer"/> <!-- 制造商/厂商 -->
    </resultMap>

    <sql id="selectGatewayMonitorProductVo">
        gmp.id, gmp.monitor_id, gmp.product_id, gmp.product_key, gmp.del_flag, gmp.remark,
        gmp.create_time, gmp.create_by, gmp.create_name, gmp.update_time, gmp.update_by
    </sql>
    <!-- 查询对象List -->
    <select id="selectGatewayMonitorProductList" parameterType="GatewayMonitorProduct"
            resultMap="gatewayMonitorProductResult">
        select <include refid="selectGatewayMonitorProductVo"/> from th_gateway_monitor_product gmp
        <where>
            <include refid="equal"/>
        </where>
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectGatewayMonitorProductListByLike" parameterType="GatewayMonitorProduct"
            resultMap="gatewayMonitorProductResult">
        select <include refid="selectGatewayMonitorProductVo"/>,tp.name productName ,tp.product_model,tp.manufacturer
        from th_gateway_monitor_product gmp
        left join th_product tp on tp.product_key = gmp.product_key
        <where>
            tp.del_flag = '0'
            <if test="productName != null and productName != ''">
                AND tp.name like '%${productName}%'  <!-- 产品key -->
            </if>
            <include refid="like"/>

        </where>
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectGatewayMonitorProductById" parameterType="Integer" resultMap="gatewayMonitorProductResult">
        select <include refid="selectGatewayMonitorProductVo"/> from th_gateway_monitor_product gmp
        where id = #{id}
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectProductIds" parameterType="GatewayMonitorProduct" resultType="Integer">
        select gmp.product_id from th_gateway_monitor_product gmp
        where gmp.del_flag = '0' AND gmp.monitor_id = #{monitorId}  <!-- 触发器ID -->
        group by gmp.product_id
    </select>

    <update id="updateGatewayMonitorProduct" parameterType="GatewayMonitorProduct">
        update th_gateway_monitor_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="monitorId != null  ">
                monitor_id = #{monitorId},
            </if>
            <if test="productId != null  ">
                product_id = #{productId},
            </if>
            <if test="productKey != null  and productKey != ''  ">
                product_key = #{productKey},
            </if>
            <if test="delFlag != null  and delFlag != ''  ">
                del_flag = #{delFlag},
            </if>
            remark = #{remark},
            <if test="createTime != null  ">
                create_time = #{createTime},
            </if>
            <if test="createBy != null  ">
                create_by = #{createBy},
            </if>
            <if test="createName != null  and createName != ''  ">
                create_name = #{createName},
            </if>
            <if test="updateTime != null  ">
                update_time = #{updateTime},
            </if>
            <if test="updateBy != null  ">
                update_by = #{updateBy},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGatewayMonitorProductById" parameterType="Integer">
        update th_gateway_monitor_product set del_flag = '1' where id = #{id}
    </delete>

    <!-- 新增对象 -->
    <insert id="insertGatewayMonitorProduct" parameterType="GatewayMonitorProduct">
        insert into th_gateway_monitor_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null  ">id,
            </if>
            <if test="monitorId != null  ">monitor_id,
            </if>
            <if test="productId != null  ">product_id,
            </if>
            <if test="productKey != null  and productKey != ''  ">product_key,
            </if>
            <if test="delFlag != null  and delFlag != ''  ">del_flag,
            </if>
            <if test="remark != null  and remark != ''  ">remark,
            </if>
            <if test="createTime != null  ">create_time,
            </if>
            <if test="createBy != null  ">create_by,
            </if>
            <if test="createName != null  and createName != ''  ">create_name,
            </if>
            <if test="updateTime != null  ">update_time,
            </if>
            <if test="updateBy != null  ">update_by,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null  ">#{id},
            </if>
            <if test="monitorId != null  ">#{monitorId},
            </if>
            <if test="productId != null  ">#{productId},
            </if>
            <if test="productKey != null  and productKey != ''  ">#{productKey},
            </if>
            <if test="delFlag != null  and delFlag != ''  ">#{delFlag},
            </if>
            <if test="remark != null  and remark != ''  ">#{remark},
            </if>
            <if test="createTime != null  ">#{createTime},
            </if>
            <if test="createBy != null  ">#{createBy},
            </if>
            <if test="createName != null  and createName != ''  ">#{createName},
            </if>
            <if test="updateTime != null  ">#{updateTime},
            </if>
            <if test="updateBy != null  ">#{updateBy},
            </if>
        </trim>
    </insert>

    <!-- 表字段 -->
    <sql id="column">
        id, <!--  -->
        monitor_id, <!-- 触发器ID -->
        product_id, <!-- 产品Id -->
        product_key, <!-- 产品key -->
        del_flag, <!-- 删除标识 0-正常，1-删除 -->
        remark, <!-- 备注 -->
        create_time, <!-- 创建时间 -->
        create_by, <!-- 创建人 -->
        create_name, <!-- 创建人 -->
        update_time, <!-- 修改时间 -->
        update_by <!-- 更新人 -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="id != null ">
            AND gmp.id = #{id}  <!--  -->
        </if>
        <if test="monitorId != null ">
            AND gmp.monitor_id = #{monitorId}  <!-- 触发器ID -->
        </if>
        <if test="productId != null ">
            AND gmp.product_id = #{productId}  <!-- 产品Id -->
        </if>
        <if test="productKey != null and productKey != ''">
            AND gmp.product_key = #{productKey}  <!-- 产品key -->
        </if>
        <if test="remark != null and remark != ''">
            AND gmp.remark = #{remark}  <!-- 备注 -->
        </if>
        <if test="createTime != null ">
            AND gmp.create_time = #{createTime}  <!-- 创建时间 -->
        </if>
        <if test="createBy != null ">
            AND gmp.create_by = #{createBy}  <!-- 创建人 -->
        </if>
        <if test="createName != null and createName != ''">
            AND gmp.create_name = #{createName}  <!-- 创建人 -->
        </if>
        <if test="updateTime != null ">
            AND gmp.update_time = #{updateTime}  <!-- 修改时间 -->
        </if>
        <if test="updateBy != null ">
            AND gmp.update_by = #{updateBy}  <!-- 更新人 -->
        </if>
        AND del_flag = '0'
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <if test="id != null ">
            AND gmp.id = #{id}  <!--  -->
        </if>
        <if test="monitorId != null ">
            AND gmp.monitor_id = #{monitorId}  <!-- 触发器ID -->
        </if>
        <if test="productId != null ">
            AND gmp.product_id = #{productId}  <!-- 产品Id -->
        </if>
        <if test="productKey != null and productKey != ''">
            AND gmp.product_key like '%${productKey}%'  <!-- 产品key -->
        </if>
        <if test="remark != null and remark != ''">
            AND gmp.remark like '%${remark}%'  <!-- 备注 -->
        </if>
        <if test="createTime != null ">
            AND gmp.create_time = #{createTime}  <!-- 创建时间 -->
        </if>
        <if test="createBy != null ">
            AND gmp.create_by = #{createBy}  <!-- 创建人 -->
        </if>
        <if test="createName != null and createName != ''">
            AND gmp.create_name like '%${createName}%'  <!-- 创建人 -->
        </if>
        <if test="updateTime != null ">
            AND gmp.update_time = #{updateTime}  <!-- 修改时间 -->
        </if>
        <if test="updateBy != null ">
            AND gmp.update_by = #{updateBy}  <!-- 更新人 -->
        </if>
        AND gmp.del_flag ='0'
        ORDER BY gmp.update_time DESC
    </sql>
</mapper>

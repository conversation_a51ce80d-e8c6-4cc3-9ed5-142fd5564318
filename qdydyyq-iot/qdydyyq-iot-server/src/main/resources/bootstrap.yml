server:
  port: 9038

spring:
  application:
    name: qdydyyq-iot
  # dev环境
  profiles:
    active: dev
  #注册与配置中心地址,密码认证当前nacos1.2版本暂未支持配置
  cloud:
    nacos:
      username: nacos
      password: Tunny_huazheng@2024
      discovery:
        server-addr: server0:1005
        namespace: edenSmartPark
      config:
        server-addr: server0:1005
        #默认为application.name的值
        #prefix:
        #配置语法格式
        file-extension: yaml
        #Tunny微服务的通用配置，按实际需要配置即可，若存在多个用，隔开
        shared-dataids: qdydyyq-dev.yaml
        #当以下dataids发生变化时，应用中动态刷新
        refreshable-dataids: qdydyyq-dev.yaml
        #若不配置，则为public(保留空间)
        namespace: edenSmartPark
        #若不配置，则为DEFAULT_GROUP，可以用group来区分不同的项目或环境
        #group:

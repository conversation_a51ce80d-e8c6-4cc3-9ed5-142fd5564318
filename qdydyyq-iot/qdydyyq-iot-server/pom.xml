<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.huazheng</groupId>
        <artifactId>qdydyyq-iot</artifactId>
        <version>1.3.2</version>
    </parent>

    <artifactId>qdydyyq-iot-server</artifactId>
    <version>1.3.2</version>
    <packaging>jar</packaging>

    <name>qdydyyq-iot-server</name>
    <description>qdydyyq 物联接入业务处理模块</description>

    <dependencies>

        <!--basic api、model 模块-->
        <dependency>
            <groupId>com.huazheng</groupId>
            <artifactId>qdydyyq-iot-client</artifactId>
            <version>1.3.2</version>
        </dependency>
        <!--mybatis-->
        <!--mybatis-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mbp.boot.version}</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>org.xerial</groupId>
            <artifactId>sqlite-jdbc</artifactId>
            <version>3.31.1</version>
        </dependency>
        <!--配置druid连接池-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>1.1.10</version>
        </dependency>
        <!--common-->
        <dependency>
            <groupId>com.huazheng</groupId>
            <artifactId>qdydyyq-common-thingshive</artifactId>
			<version>1.3.2</version>
        </dependency>
        <!--swagger 内置安全模块-->
        <dependency>
            <groupId>com.huazheng</groupId>
            <artifactId>qdydyyq-common-swagger</artifactId>
            <version>1.3.2</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.6.6</version>
        </dependency>

        <dependency>
            <groupId>com.huazheng</groupId>
            <artifactId>qdydyyq-data-client</artifactId>
            <version>1.3.2</version>
        </dependency>

        <!-- MQTT服务 start -->
        <dependency>
            <groupId>org.springframework.integration</groupId>
            <artifactId>spring-integration-stream</artifactId>
            <version>5.0.6.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.integration</groupId>
            <artifactId>spring-integration-mqtt</artifactId>
            <version>5.0.6.RELEASE</version>
        </dependency>
        <!-- MQTT服务 end -->

        <!--web 模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <!--排除tomcat依赖-->
                <exclusion>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>

        <!--引入日志组件-->
        <dependency>
            <groupId>com.plumelog</groupId>
            <artifactId>plumelog-logback</artifactId>
            <version>3.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--引入日志组件结束-->

        <!-- 阿里短信服务 -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
            <version>4.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-ecs</artifactId>
            <version>4.11.0</version>
        </dependency>
        <!-- 阿里短信服务 end -->

        <!-- 邮件服务 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
            <version>2.1.9.RELEASE</version>
        </dependency>

        <!--注意spring-kafka有版本问题，我使用的springboot是2.1.1的-->
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>

    </dependencies>



    <profiles>
        <!--开发环境 -->
        <profile>
            <id>dev</id>
            <properties>
                <profiles.active>dev</profiles.active>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <!--测试环境 -->
        <profile>
            <id>test</id>
            <properties>
                <profiles.active>test</profiles.active>
            </properties>
        </profile>
        <!--生产环境 -->
        <profile>
            <id>prod</id>
            <properties>
                <profiles.active>prod</profiles.active>
            </properties>
        </profile>
    </profiles>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <!-- <finalName>${project.artifactId}-1.0.0</finalName> -->
    </build>

</project>

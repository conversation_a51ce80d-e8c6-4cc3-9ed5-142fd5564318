package com.huazheng.tunny.admin.api.entity;

import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableLogic;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 部门管理
 *
 * <AUTHOR> code generator
 * @date 2019-01-21 13:53:35
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_dept")
public class SysDept extends Model<SysDept> {

    private static final long serialVersionUID = 1L;

    @TableId
    private Long deptId;
    /**
     * 部门名称
     */
    private String name;
    /**
     * 排序
     */
    private Integer orderNum;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    private LocalDateTime updateTime;
    /**
     * 是否删除  -1：已删除  0：正常
     */
    @TableLogic
    private String delFlag;

    private Long parentId;
    /**
     * 组织简称
     */
    private String deptSimplify;
    /**
     * 组织负责人
     */
    private String deptLeader;
    /**
     * 组织类别
     */
    private String deptType;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 修改人
     */
    private String updateBy;
    /**
     * 备注
     */
    private String remark;
    /**
     * 状态 1-正常 0-停用
     */
    private String deptFlag;


    @Override
    protected Serializable pkVal() {
        return this.deptId;
    }
}

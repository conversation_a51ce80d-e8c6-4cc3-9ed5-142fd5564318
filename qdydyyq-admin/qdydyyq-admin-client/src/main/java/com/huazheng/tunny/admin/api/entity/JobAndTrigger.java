package com.huazheng.tunny.admin.api.entity;


import lombok.Data;

import java.math.BigInteger;

/**
 * 任务与触发器
 */
@Data
public class JobAndTrigger {
	//任务名称
	private String jobName;
	//任务分组
	private String jobGroup;
	//任务执行类名
	private String jobClassName;
	//任务名称
	private String description;
	// 触发器名称
	private String triggerName;
	//触发器分组
	private String triggerGroup;
	private BigInteger  repeatInterval;
	private BigInteger timesTriggered;
	//任务执行表达式
	private String cronExpression;
	//时区
	private String timeZoneId;
	//任务开始时间
	private long startTime ;
	//任务结束时间
	private long  endTime;
	//上次执行时间
	private long prevFireTime;
	//下次执行时间
	private long nextFireTime;
	//当前任务状态
	private String triggerState;
	//告警配置主键
	private int id;
	//是否失败告警
	private int isAlarm;
	// 告警电话
	private String alarmPhones;
	//告警邮箱
	private String alarmMails;
}

package com.huazheng.tunny.admin.api.feign;

import com.huazheng.tunny.admin.api.entity.SysLog;
import com.huazheng.tunny.admin.api.feign.fallback.RemoteLogServiceFallbackImpl;
import com.huazheng.tunny.common.core.constant.ServiceNameConstant;
import com.huazheng.tunny.common.core.util.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "qdydyyq-admin", fallback = RemoteLogServiceFallbackImpl.class)
public interface RemoteLogService {
	/**
	 * 保存日志
	 *
	 * @param sysLog 日志实体
	 * @return succes、false
	 */
	@PostMapping("/log")
	R<Boolean> saveLog(@RequestBody SysLog sysLog);
}

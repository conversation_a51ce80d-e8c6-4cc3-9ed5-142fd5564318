package com.huazheng.tunny.admin.api.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 子应用
 *
 * <AUTHOR> code generator
 * @date 2022-06-15 17:20:52
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_micro")
public class SysMicro extends Model<SysMicro> {
    private static final long serialVersionUID = 1L;

    /**
     * 子应用ID
     */
    @TableId(value = "micro_id", type = IdType.AUTO)
    @ExcelIgnore
    private Integer microId;
    /**
     * 子应用中文名称
     */
    @ExcelProperty(value = "子应用中文名称")
    private String microName;
    /**
     * 子应用英文名称（唯一）
     */
    @ExcelProperty(value = "	子应用英文名称（唯一）")
    private String microCode;
    /**
     * 路由前缀
     */
    @ExcelProperty(value = "路由前缀")
    private String microPath;
    /**
     * 子应用图标
     */
    @ExcelProperty(value = "子应用图标")
    private String microIcon;
    /**
     * 子应用描述
     */
    @ExcelProperty(value = "	子应用描述")
    private String microDesc;
    /**
     * 子应用启用状态
     */
    @ExcelProperty(value = "子应用启用状态")
    private Integer microState;
    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelProperty(value = "创建时间")
    private LocalDateTime createTime;
    /**
     * 更新人
     */
    @ExcelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新时间
     */
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelProperty(value = "更新时间")
    private LocalDateTime updateTime;
    /**
     * 1--正常 0--删除
     */
    @ExcelProperty(value = "1--正常 0--删除")
    private String delFlag;

    /**
     * 主键值
     */
    @Override
    protected Serializable pkVal() {
        return this.microId;
    }
}

package com.huazheng.tunny.admin.api.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 子应用菜单
 *
 * <AUTHOR> code generator
 * @date 2022-06-15 17:21:18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_micro_menu")
public class SysMicroMenu extends Model<SysMicroMenu> {
    private static final long serialVersionUID = 1L;

    /**
     * 菜单id
     */
    @TableId(value = "menu_id", type = IdType.AUTO)
    @ExcelIgnore
    private Integer menuId;
    /**
     * 菜单名称
     */
    @ExcelProperty(value = "菜单名称")
    private String name;
    /**
     * 菜单权限标识
     */
    @ExcelProperty(value = "菜单权限标识")
    private String permission;
    /**
     * 前端url
     */
    @ExcelProperty(value = "前端url")
    private String path;
    /**
     * 父菜单id
     */
    @ExcelProperty(value = "父菜单id")
    private Integer parentId;
    /**
     * 图标
     */
    @ExcelProperty(value = "图标")
    private String icons;
    /**
     * vue页面
     */
    @ExcelProperty(value = "vue页面")
    private String component;
    /**
     * 排序值
     */
    @ExcelProperty(value = "排序值")
    private Integer sort;
    /**
     * 菜单类型 （0菜单 1按钮,9目录）
     */
    @ExcelProperty(value = "菜单类型 （0菜单 1按钮,9目录）")
    private String type;
    /**
     * 子应用id
     */
    @ExcelProperty(value = "子应用id")
    private Integer microId;
    /**
     * 是否外链
     */
    @ExcelProperty(value = "是否外链")
    private Integer outside;
    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelProperty(value = "创建时间")
    private LocalDateTime createTime;
    /**
     * 更新人
     */
    @ExcelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新时间
     */
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelProperty(value = "更新时间")
    private LocalDateTime updateTime;
    /**
     * 1--正常 0--删除
     */
    @ExcelProperty(value = "1--正常 0--删除")
    private String delFlag;

    /**
     * 路由名称
     */
    private String routerName;

    /**
     * 是否缓存(0否1是)
     */
    private Integer isCache;

    /**
     * 是否隐藏(0否1是)
     */
    private Integer isHidden;

    /**
     * 主键值
     */
    @Override
    protected Serializable pkVal() {
        return this.menuId;
    }
}

package com.huazheng.tunny.admin.api.feign.fallback;

import com.huazheng.tunny.admin.api.dto.UserInfo;
import com.huazheng.tunny.admin.api.entity.SysPost;
import com.huazheng.tunny.admin.api.entity.SysRole;
import com.huazheng.tunny.admin.api.entity.SysUser;
import com.huazheng.tunny.admin.api.feign.RemoteUserService;
import com.huazheng.tunny.admin.api.vo.UserVO;
import com.huazheng.tunny.common.core.util.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class RemoteUserServiceFallbackImpl implements RemoteUserService {
	/**
	 * 通过用户名查询用户、角色信息
	 *
	 * @param username 用户名
	 * @param from     内外标志
	 * @return R
	 */
	@Override
	public R<UserInfo> info(String username, String from) {
		log.error("feign 查询用户信息失败:{}", username);
		return null;
	}

	@Override
	public R<UserInfo> mobileinfo(String mobile, String from) {
		log.error("feign 通过手机号查询用户信息失败:{}", mobile);
		return null;
	}

	/**
	 * 通过社交账号查询用户、角色信息
	 *
	 * @param inStr appid@code
	 * @return
	 */
	@Override
	public R<UserInfo> social(String inStr) {
		log.error("feign 查询用户信息失败:{}", inStr);
		return null;
	}

	@Override
	public List selectUserList(String searchUser) {
		return null;
	}

	@Override
	public List<SysPost> postList() {
		return null;
	}

	@Override
	public List<SysRole> roleListAll() {
		return null;
	}

	@Override
	public List<SysUser> selectUserByRole(List<Integer> roles) {
		return null;
	}

	@Override
	public List<SysUser> selectUserByPost(String posts) {
		return null;
	}

	@Override
	public UserVO user(Integer id) {
		return null;
	}

	@Override
	public SysUser getUserByCode(String code) {
		return null;
	}

	@Override
	public List<SysUser> getUserListByCode(List<String> codes) {
		return null;
	}
}

package com.huazheng.tunny.admin.api.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.huazheng.tunny.admin.api.util.StudentSexConverter;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.Date;

@Data
public class StudentEntity {
    //@ExcelIgnore可以在读取或写入Excel时忽略该字段
    @ExcelIgnore
    private String id;
    //@ExcelProperty中value为列标题，order为排序
    @ExcelProperty(value = {"计算机系学生考勤表", "班级"}, order = 1)
    private String classNum;
    @ExcelProperty(value = {"计算机系学生考勤表", "学生信息", "学生姓名"}, order = 2)
    private String name;
    @ExcelProperty(value = {"计算机系学生考勤表", "学生信息", "学生性别"}, order = 3, converter = StudentSexConverter.class)
    private Integer sex;
//    通过DateTimeFormat对导入导出时的时间进行格式化
    @DateTimeFormat("yyyy-MM-dd")
    @ExcelProperty(value = {"计算机系学生考勤表","学生信息", "出生日期"}, order = 4)
    private Date birthday;
    @DateTimeFormat("yyyy-MM-dd")
    @ExcelProperty(value = {"计算机系学生考勤表", "进校日期"}, order = 5)
    private Date registrationDate;

    //实体类中如果有有参构造时，必须要有一个无参构造
    public StudentEntity() {
    }

    public StudentEntity(final String id, final String classNum, final String name, final Integer sex, final Date birthday, final Date registrationDate) {
        this.id = id;
        this.classNum = classNum;
        this.name = name;
        this.sex = sex;
        this.birthday = birthday;
        this.registrationDate = registrationDate;
    }

}

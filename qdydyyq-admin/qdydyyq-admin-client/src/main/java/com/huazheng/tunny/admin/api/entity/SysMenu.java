package com.huazheng.tunny.admin.api.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableLogic;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 菜单权限表
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_menu")
public class SysMenu extends Model<SysMenu> {

    private static final long serialVersionUID = 1L;

    /**
     * 菜单ID
     */
    @TableId(value = "menu_id", type = IdType.AUTO)
    private Integer menuId;

    /**
     * 菜单名称
     */
    private String name;
    /**
     * 菜单权限标识
     */
    private String permission;
    /**
     * 父菜单ID
     */
    private Integer parentId;
    /**
     * 图标
     */
    private String icons;
    /**
     * VUE页面
     */
    private String component;
    /**
     * 排序值
     */
    private Integer sort;
    /**
     * 菜单类型 （0菜单 1按钮,9目录）
     */
    private String type;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 1--正常 0--删除
     */
    @TableLogic
    private String delFlag;
    /**
     * 前端URL
     */
    private String path;
    /**
     * 是否外链
     */
    private Integer outside;
    /**
     * 子应用ID
     */
    private Integer microId;
    /**
     * 子应用中文名称
     */
    private String microName;
    /**
     * 子应用英文名称
     */
    private String microCode;
    /**
     * 路由前缀
     */
    private String microPath;
    /**
     * 子应用父级path
     */
    private String microMenuParentPath;
    /**
     * 子应用菜单ID
     */
    private Integer microMenuId;
    /**
     * 子应用菜单父级ID
     */
    private Integer microMenuParentId;
    /**
     * 路由名称
     */
    private String routerName;

    /**
     * 是否缓存(0否1是)
     */
    private Integer isCache;

    /**
     * 是否隐藏(0否1是)
     */
    private Integer isHidden;

    /**
     * 显示位置(0仅菜单 1仅首页 2菜单及首页)
     */
    private String isHomePage;

    /**
     * 首页显示时的图标文件地址
     */
    private String homePageIcon;


    @Override
    protected Serializable pkVal() {
        return this.menuId;
    }

}

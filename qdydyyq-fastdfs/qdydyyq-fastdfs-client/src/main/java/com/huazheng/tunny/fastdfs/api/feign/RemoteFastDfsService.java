package com.huazheng.tunny.fastdfs.api.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

@FeignClient(value = "qdydyyq-fastdfs")
public interface RemoteFastDfsService {

    /**
     * 单文件上传
     */
    @PostMapping(value = "/singleUpload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Object singleUpload(@RequestPart(value = "file", required = true) MultipartFile file);

    /**
     * 多文件上传
     */
//    @PostMapping(value = "/multiUpload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
//    public Object multiUpload(@RequestPart(value = "files", required = true) MultipartFile[] files, String empno);

    /**
     * 文件秒传
     */
    @PostMapping("/secondPass")
    public Object secondPass(@RequestParam(value = "md5", required = true) String md5,
                            @RequestParam(value = "fileName", required = true) String fileName, @RequestParam String empno);

    /**
     * 字节流上传
     */
    @PostMapping("/byteUpload")
    public Object byteUpload(@RequestParam(value = "bytes", required = true) byte[] bytes,
                        @RequestParam(value = "fileName", required = true) String fileName, @RequestParam String empno);
}

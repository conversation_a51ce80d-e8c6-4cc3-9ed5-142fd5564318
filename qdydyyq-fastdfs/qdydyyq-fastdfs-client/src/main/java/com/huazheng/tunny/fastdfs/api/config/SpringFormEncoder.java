//////
////// Source code recreated from a .class file by IntelliJ IDEA
////// (powered by <PERSON>rn<PERSON> decompiler)
//////
////
//package com.huazheng.tunny.fastdfs.api.config;
//
//import feign.RequestTemplate;
//import feign.codec.EncodeException;
//import feign.codec.Encoder;
//import feign.codec.Encoder.Default;
//import feign.form.ContentType;
//import feign.form.FormEncoder;
//import feign.form.MultipartFormContentProcessor;
//import java.lang.reflect.Type;
//import java.util.Collections;
//import java.util.HashMap;
//import java.util.Map;
//
//import feign.form.spring.SpringManyMultipartFilesWriter;
//import feign.form.spring.SpringSingleMultipartFileWriter;
//import lombok.val;
//import org.springframework.web.multipart.MultipartFile;
//
//public class SpringFormEncoder extends FormEncoder {
//    public SpringFormEncoder() {
//        this(new Default());
//    }
//
//    public SpringFormEncoder(Encoder delegate) {
//        super(delegate);
//        MultipartFormContentProcessor processor = (MultipartFormContentProcessor)this.getContentProcessor(ContentType.MULTIPART);
//        processor.addWriter(new SpringSingleMultipartFileWriter());
//        processor.addWriter(new SpringManyMultipartFilesWriter());
//    }
//
//    public void encode(Object object, Type bodyType, RequestTemplate template) throws EncodeException {
///*
//        System.out.println("类型："+bodyType.getTypeName()+"  :"+bodyType.getClass());
//        System.out.println("判断："+bodyType.equals(MultipartFile[].class));
//        System.out.println("判断A："+(!bodyType.equals(MultipartFile.class) && !bodyType.equals(MultipartFile[].class) ));
//        if (!bodyType.equals(MultipartFile.class) && !bodyType.equals(MultipartFile[].class) ) {
//            super.encode(object, bodyType, template);
//        }else if(bodyType!=null && bodyType.equals(MultipartFile[].class)){
//            //重写原方法，在此处处理MultipartFile数组
//            MultipartFile[] file = (MultipartFile[]) object;
//            if(file != null) {
//                Map data = Collections.singletonMap(file.length == 0 ? "" : file[0].getName(), object);
//                super.encode(data, MAP_STRING_WILDCARD, template);
//            }
//        } else {
//            MultipartFile file = (MultipartFile)object;
//            Map<String, Object> data = Collections.singletonMap(file.getName(), object);
//            super.encode(data, MAP_STRING_WILDCARD, template);
//        }
//
//
//*/
///*        if (bodyType.equals(MultipartFile.class)) {
//            MultipartFile file = (MultipartFile) object;
//            Map<String, Object> data = Collections.singletonMap(file.getName(), object);
//            super.encode(data, MAP_STRING_WILDCARD, template);
//            return;
//        } else if (bodyType.equals(MultipartFile[].class)) {
//            MultipartFile[] file = (MultipartFile[]) object;
//            if(file != null) {
//                Map<String, Object> data = Collections.singletonMap(file.length==0?"":file[0].getName(), object);
//                super.encode(data, MAP_STRING_WILDCARD, template);
//                return;
//            }
//        }
//        super.encode(object, bodyType, template);*/
//
//
//
////        if (bodyType.equals(MultipartFile[].class)) {
////            // 主要是改造这里
////            MultipartFile[]  files = (MultipartFile[]) object;
////            if(files != null) {
////                Map<String, Object>  data = Collections.singletonMap(files.length == 0 ? "" : files[0].getName(), object);
////                super.encode(data, MAP_STRING_WILDCARD, template);
////            }
////        } else if (bodyType.equals(MultipartFile.class)) {
////            MultipartFile file = (MultipartFile) object;
////            Map<String, Object>  data = Collections.singletonMap(file.getName(), object);
////            super.encode(data, MAP_STRING_WILDCARD, template);
////        } else if (isMultipartFileCollection(object)) {
////            val iterable = (Iterable<?>) object;
////            Map<String, Object>  data = new HashMap<String, Object>();
////            for (val item : iterable) {
////                val file = (MultipartFile) item;
////                data.put(file.getName(), file);
////            }
////            super.encode(data, MAP_STRING_WILDCARD, template);
////        } else {
////            super.encode(object, bodyType, template);
////        }
//
//
//
//        // 单MultipartFile判断
//        if (bodyType.equals(MultipartFile.class)) {
//            MultipartFile file = (MultipartFile) object;
//            Map data = Collections.singletonMap(file.getName(), object);
//            super.encode(data, MAP_STRING_WILDCARD, template);
//            return;
//        } else if (bodyType.equals(MultipartFile[].class)) {
//            // MultipartFile数组处理
//            MultipartFile[] file = (MultipartFile[]) object;
//            if(file != null) {
//                Map data = Collections.singletonMap(file.length == 0 ? "" : file[0].getName(), object);
//                super.encode(data, MAP_STRING_WILDCARD, template);
//                return;
//            }
//        }
//        // 其他类型调用父类默认处理方法
//        super.encode(object, bodyType, template);
//
//    }
//
//
//    private boolean isMultipartFileCollection(Object object) {
//        if (!(object instanceof Iterable)) {
//            return false;
//        }
//        val iterable = (Iterable<?>) object;
//        val iterator = iterable.iterator();
//        return iterator.hasNext() && iterator.next() instanceof MultipartFile;
//    }
//
//
//}

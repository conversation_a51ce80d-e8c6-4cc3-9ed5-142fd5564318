package com.huazheng.tunny.fastdfs.api.dto;

import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 文件服务器日志表
 *
 * <AUTHOR> code generator
 * @date 2020-04-23 13:54:32
 */
@Data
public class MinioDto {
    /**
     * 原文件名
     * */
    private String originalName;
    /**
     * 文件后缀
     * */
    private String suffix;
    /**
     * 文件名
     * */
    private String fileName;
    /**
     * 预览地址
     * */
    private String previewUrl;
}

package com.huazheng.tunny.fastdfs.api.entity;

import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 文件服务器日志表
 *
 * <AUTHOR> code generator
 * @date 2020-04-23 13:54:32
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_file_server_log")
@AllArgsConstructor
@NoArgsConstructor
public class SysFileServerLog extends Model<SysFileServerLog> {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 操作类型：1上传 2下载
     */
    private Integer operateType;
    /**
     * 操作系统
     */
    private String systemType;
    /**
     * 文件类型
     */
    private String fileType;
    /**
     * 文件大小
     */
    private Long fileSize;
    /**
     * 文件名
     */
    private String fileName;
    /**
     * 存储路径
     */
    private String filePath;
    /**
     * 上传时间
     */
    private LocalDateTime uploadTime;
    /**
     * 上传人
     */
    private String uploadBy;
    /**
     * 是否有效
     */
    private String fileFlag;

    /**
     * 主键值
     */
    @Override
    protected Serializable pkVal() {
        return this.id;
    }
}

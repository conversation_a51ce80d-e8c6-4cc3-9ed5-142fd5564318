//package com.huazheng.tunny.fastdfs.api.config;
//
//import feign.codec.Encoder;
//import org.springframework.beans.factory.ObjectFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
//import org.springframework.cloud.openfeign.support.SpringEncoder;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//@Configuration
//public class FeignMultipartSupportConfig {
//
//
////    @Bean
////    public Encoder feignEncoder(ObjectFactory<HttpMessageConverters> messageConverters) {
////        return new SpringMultipartEncoder(new SpringEncoder(messageConverters));
////    }
//
//    @Autowired
//    private ObjectFactory<HttpMessageConverters> messageConverters;
//
//    @Bean
//    public Encoder feignEncoder() {
//        return new SpringMultipartEncoder(new SpringEncoder(messageConverters));
//    }
//
//
//}

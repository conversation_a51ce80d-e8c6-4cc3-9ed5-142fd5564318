package com.huazheng.tunny.fastdfs.api.feign.fallback;

import com.huazheng.tunny.fastdfs.api.feign.RemoteFastDfsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Slf4j
@Component
public class RemoteFastDfsServiceFallbackImpl implements RemoteFastDfsService {

	@Override
	public Object singleUpload(MultipartFile file) {
		return null;
	}

//	@Override
//	public Object multiUpload(MultipartFile[] files, String empno) {
//		return null;
//	}

	@Override
	public Object secondPass(String md5, String fileName, String empno) {
		return null;
	}

	@Override
	public Object byteUpload(byte[] bytes, String fileName, String empno) {
		return null;
	}
}

<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.huazheng</groupId>
		<artifactId>qdydyyq-fastdfs</artifactId>
		<version>1.3.2</version>
	</parent>

	<artifactId>qdydyyq-fastdfs-client</artifactId>
	<version>1.3.2</version>
	<packaging>jar</packaging>
	<name>qdydyyq-fastdfs-client</name>
	<description>tunny fastdfs公共api模块</description>


	<dependencies>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-openfeign-core</artifactId>
            <version>2.1.0.RELEASE</version>
        </dependency>
		<!--mybatis-->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
			<version>2.3</version>
		</dependency>

<!--		<dependency>-->
<!--			<groupId>io.github.openfeign.form</groupId>-->
<!--			<artifactId>feign-form</artifactId>-->
<!--			<version>3.3.0</version>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>io.github.openfeign.form</groupId>-->
<!--			<artifactId>feign-form-spring</artifactId>-->
<!--			<version>3.3.0</version>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>commons-fileupload</groupId>-->
<!--			<artifactId>commons-fileupload</artifactId>-->
<!--			<version>1.3.3</version>-->
<!--		</dependency>-->
		<dependency>
			<groupId>io.github.openfeign</groupId>
			<artifactId>feign-core</artifactId>
		</dependency>
    </dependencies>
</project>

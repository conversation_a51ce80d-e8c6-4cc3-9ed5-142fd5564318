<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.huazheng</groupId>
		<artifactId>qdydyyq</artifactId>
		<version>1.3.2</version>
	</parent>

	<artifactId>qdydyyq-fastdfs</artifactId>
	<version>1.3.2</version>
	<name>qdydyyq-fastdfs</name>
	<description>tunny 通用fastdfs聚合模块</description>
	<packaging>pom</packaging>

	<modules>
		<module>qdydyyq-fastdfs-client</module>
		<module>qdydyyq-fastdfs-server</module>
	</modules>


	<repositories>
		<!--配置私服镜像-->
		<repository>
			<id>nexus</id>
			<name>tunny_group</name>
			<url>http://*************:30037/repository/tunny_group/</url>
			<releases>
				<enabled>true</enabled>
				<updatePolicy>always</updatePolicy>
			</releases>
			<snapshots>
				<enabled>true</enabled>
				<updatePolicy>always</updatePolicy>
			</snapshots>
		</repository>

		<repository>
			<id>aliyun</id>
			<name>aliyun</name>
			<url>http://maven.aliyun.com/nexus/content/groups/public/</url>
		</repository>
	</repositories>

	<distributionManagement>
		<snapshotRepository>
			<id>nexus</id>
			<url>http://*************:30037/repository/tunny_snapshots/</url>
		</snapshotRepository>
		<repository>
			<id>nexus</id>
			<url>http://*************:30037/repository/tunny_release/</url>
		</repository>
	</distributionManagement>
</project>

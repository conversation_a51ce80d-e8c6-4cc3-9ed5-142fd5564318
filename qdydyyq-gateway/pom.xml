<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.huazheng</groupId>
		<artifactId>qdydyyq</artifactId>
		<version>1.3.2</version>
	</parent>

	<artifactId>qdydyyq-gateway</artifactId>
	<version>1.3.2</version>
	<packaging>jar</packaging>
	<name>qdydyyq-gateway</name>
	<description>qdydyyq 服务网关，基于 spring cloud gateway</description>

	<dependencies>
		<!--gateway 网关依赖,内置webflux 依赖-->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-gateway</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis-reactive</artifactId>
		</dependency>
		<!--配置中心客户端-->
<!--		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-config</artifactId>
		</dependency>-->
		<!--验证码-->
		<dependency>
			<groupId>com.github.axet</groupId>
			<artifactId>kaptcha</artifactId>
			<version>${kaptcha.version}</version>
		</dependency>
		<!--hutool-->
<!--		<dependency>
			<groupId>com.huazheng</groupId>
			<artifactId>qdydyyq-common-core</artifactId>
			<version>1.3.2</version>
		</dependency>-->

		<!--hutool-->
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>${hutool.version}</version>
		</dependency>
		<!--接口文档-->
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger-ui</artifactId>
			<version>${swagger.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.google.guava</groupId>
					<artifactId>guava</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
			<version>${swagger.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.google.guava</groupId>
					<artifactId>guava</artifactId>
				</exclusion>
			</exclusions>
		</dependency>


		<!--数据库-->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
			<version>2.3.1</version>
		</dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-jdbc</artifactId>
		</dependency>


		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-oauth2-jose</artifactId>
			<version>5.0.5.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-oauth2-resource-server</artifactId>
			<version>5.1.5.RELEASE</version>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>org.springframework.security</groupId>-->
<!--			<artifactId>spring-security-config</artifactId>-->
<!--			<version>5.0.6.RELEASE</version>-->
<!--		</dependency>-->

		<dependency>
			<groupId>org.lionsoul</groupId>
			<artifactId>ip2region</artifactId>
			<version>1.7.2</version>
		</dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-core</artifactId>
			<version>9.5.1</version>
        </dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<version>3.1.0</version>
		</dependency>

		<!--feign接口依赖-->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.google.guava</groupId>
					<artifactId>guava</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- License 校验工具包 -->
		<dependency>
			<groupId>de.schlichtherle.truelicense</groupId>
			<artifactId>truelicense-core</artifactId>
			<version>1.33</version>
		</dependency>

		<dependency>
			<groupId>org.owasp.antisamy</groupId>
			<artifactId>antisamy</artifactId>
			<version>1.6.2</version>
		</dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>1.10.0</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<mainClass>com.huazheng.tunny.gateway.TunnyGatewayApplication</mainClass>
				</configuration>
				</plugin>
		</plugins>
<!--		<finalName>${project.artifactId}-${maven.build.timestamp}</finalName>-->
	</build>
	<properties>
		<!--自定义默认的编码格式-->
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<!--自定义默认的时间格式 年-月-日-时-分-->
		<maven.build.timestamp.format>
			yyyy-MM-dd_HH_mm
		</maven.build.timestamp.format>
	</properties>
</project>

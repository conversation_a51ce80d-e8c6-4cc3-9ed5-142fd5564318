<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <contextName>qdydyyq-smarkpark</contextName>
    <!--输出文件路径-->
    <property name="log.path" value="../logs/qdydyyq-smarkpark.log"/>
    <!--输出到控制台-->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <!--logback默认过滤器，若开启则error以下的日志级别都不会输出-->
        <!-- <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
             <level>ERROR</level>
         </filter>-->
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} %contextName [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!--脱敏后输出到控制台-->
<!--    <appender name="consolesafe" class="pers.liuchengyin.logbackadvice.LcyConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} %contextName [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>-->

    <!--输出到文件-->
    <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}</file>
        <!--日志滚动策略，一天切割一次-->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>../logs/logback.%d{yyyy-MM-dd}.log</fileNamePattern>

            <!--只保留最近15天的日志-->
            <maxHistory>15</maxHistory>
            <!--用来指定日志文件的上限大小，那么到了这个值，就会删除旧的日志-->
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <!-- 日志输出编码格式-->
        <encoder>
            <charset>UTF-8</charset>
            <pattern>%d{HH:mm:ss.SSS} %contextName [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>


    <!--脱敏后输出到文件-->
<!--    <appender name="filesafe" class="pers.liuchengyin.logbackadvice.LcyRollingFileAppender">
        <file>${log.path}</file>
        &lt;!&ndash;日志滚动策略，一天切割一次&ndash;&gt;
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>../logs/logback.%d{yyyy-MM-dd}.log</fileNamePattern>

            &lt;!&ndash;只保留最近15天的日志&ndash;&gt;
            <maxHistory>15</maxHistory>
            &lt;!&ndash;用来指定日志文件的上限大小，那么到了这个值，就会删除旧的日志&ndash;&gt;
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        &lt;!&ndash; 日志输出编码格式&ndash;&gt;
        <encoder>
            <charset>UTF-8</charset>
            <pattern>%d{HH:mm:ss.SSS} %contextName [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>-->



    <!--使用redis启用下面配置-->
    <!-- 字段说明 -->
    <!-- appName:应用名称 -->
    <!-- redisHost：redis地址 -->
    <!-- redisPort：redis端口号 不配置，默认使用6379-->
    <!-- runModel：runModel 1,2  1表示最高性能模式，2表示低性能模式 但是2可以获取更多信息 不配置默认为1- -->
    <!-- expand：整合其他链路插件，启用这个字段 expand=“sleuth” 表示整合springcloud.sleuth- -->
    <appender name="plumelog" class="com.plumelog.logback.appender.RedisAppender">
        <appName>qdydyyq-smarkpark</appName>
        <redisHost>************</redisHost>
        <redisAuth>tunny_huazheng</redisAuth>
        <redisPort>6379</redisPort>
    </appender>


    <root level="info">
        <appender-ref ref="console"/>
        <appender-ref ref="file"/>
    </root>

    <!-- 测试环境+开发环境. 多个使用逗号隔开. -->
    <!-- 测试环境+开发环境. 多个使用逗号隔开. -->
    <springProfile name="test,dev">
        <logger name="com.huazheng" level="debug" additivity="true" >
            <appender-ref ref="console"/>
        </logger>
    </springProfile>

    <!-- 生产环境可设置成info 或error 输出到Plumelog-->
    <springProfile name="prod,pro">
        <root level="info">
           <!-- <appender-ref ref="plumelog"/>-->
            <appender-ref ref="file"/>
        </root>
    </springProfile>


</configuration>
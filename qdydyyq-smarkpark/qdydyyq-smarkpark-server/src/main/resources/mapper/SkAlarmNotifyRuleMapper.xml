<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.smarkpark.mapper.SkAlarmNotifyRuleMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.smarkpark.api.entity.SkAlarmNotifyRule" id="skAlarmNotifyRuleResult">
        <result property="id" column="id"/> <!--  -->
        <result property="alarmType" column="alarm_type"/> <!-- 告警模块编码 SecurityAlarm-安防告警  FireAlarm-消防告警 -->
        <result property="alarmTypeName" column="alarm_type_name"/> <!-- 告警模块名称 SecurityAlarm-安防告警  FireAlarm-消防告警 -->
        <result property="eventType" column="event_type"/> <!-- 告警类别编码 -->
        <result property="eventTypeName" column="event_type_name"/> <!-- 告警类别名称 -->
        <result property="notifyPerson" column="notify_person"/> <!-- 告警通知人 -->
        <result property="notifyPersonName" column="notify_person_name"/> <!-- 告警通知人姓名 -->
        <result property="delFlag" column="del_flag"/> <!-- 0-删除，1-正常 -->
        <result property="createTime" column="create_time"/> <!-- 创建时间 -->
        <result property="createBy" column="create_by"/> <!-- 创建人 -->
        <result property="updateTime" column="update_time"/> <!-- 修改时间 -->
        <result property="updateBy" column="update_by"/> <!-- 修改人 -->
    </resultMap>


    <sql id="selectSkAlarmNotifyRuleVo">
        select id,
               alarm_type,
               alarm_type_name,
               event_type,
               event_type_name,
               notify_person,
               notify_person_name,
               del_flag,
               create_time,
               create_by,
               update_time,
               update_by
        from sk_alarm_notify_rule
    </sql>
    <!-- 查询对象List -->
    <select id="selectSkAlarmNotifyRuleList" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkAlarmNotifyRule" resultMap="skAlarmNotifyRuleResult">
        <include refid="selectSkAlarmNotifyRuleVo"/>
        <where>
            <include refid="equal"/>
            AND del_flag = '1'
        </where>
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectSkAlarmNotifyRuleListByLike" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkAlarmNotifyRule" resultMap="skAlarmNotifyRuleResult">
        <include refid="selectSkAlarmNotifyRuleVo"/>
        <where>
            <include refid="like"/>
            AND del_flag = '1'
        </where>
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectSkAlarmNotifyRuleById" parameterType="Integer" resultMap="skAlarmNotifyRuleResult">
        <include refid="selectSkAlarmNotifyRuleVo"/>
        where id = #{id}
    </select>


    <update id="updateSkAlarmNotifyRule" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkAlarmNotifyRule">
        update sk_alarm_notify_rule
        <trim prefix="SET" suffixOverrides=",">
            <if test="alarmType != null  and alarmType != ''  ">alarm_type = #{alarmType},</if>
            <if test="alarmTypeName != null  and alarmTypeName != ''  ">alarm_type_name = #{alarmTypeName},</if>
            <if test="eventType != null  and eventType != ''  ">event_type = #{eventType},</if>
            <if test="eventTypeName != null  and eventTypeName != ''  ">event_type_name = #{eventTypeName},</if>
            <if test="notifyPerson != null  and notifyPerson != ''  ">notify_person = #{notifyPerson},</if>
            <if test="notifyPersonName != null  and notifyPersonName != ''  ">notify_person_name = #{notifyPersonName},</if>
            <if test="delFlag != null  and delFlag != ''  ">del_flag = #{delFlag},</if>
            <if test="createTime != null  ">create_time = #{createTime},</if>
            <if test="createBy != null  and createBy != ''  ">create_by = #{createBy},</if>
            <if test="updateTime != null  ">update_time = #{updateTime},</if>
            <if test="updateBy != null  and updateBy != ''  ">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSkAlarmNotifyRuleById" parameterType="Integer">
        update sk_alarm_notify_rule set del_flag = '0' where id = #{id}
    </delete>

    <delete id="deleteSkAlarmNotifyRuleByIds" parameterType="Integer">
        delete from sk_alarm_notify_rule where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <!-- 新增对象 -->
    <insert id="insertSkAlarmNotifyRule" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkAlarmNotifyRule">
        insert into sk_alarm_notify_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="alarmType != null  and alarmType != ''  ">alarm_type,</if>
            <if test="alarmTypeName != null  and alarmTypeName != ''  ">alarm_type_name,</if>
            <if test="eventType != null  and eventType != ''  ">event_type,</if>
            <if test="eventTypeName != null  and eventTypeName != ''  ">event_type_name,</if>
            <if test="notifyPerson != null  and notifyPerson != ''  ">notify_person,</if>
            <if test="notifyPersonName != null  and notifyPersonName != ''  ">notify_person_name,</if>
            <if test="delFlag != null  and delFlag != ''  ">del_flag,</if>
            <if test="createTime != null  ">create_time,</if>
            <if test="createBy != null  and createBy != ''  ">create_by,</if>
            <if test="updateTime != null  ">update_time,</if>
            <if test="updateBy != null  and updateBy != ''  ">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="alarmType != null  and alarmType != ''  ">#{alarmType},</if>
            <if test="alarmTypeName != null  and alarmTypeName != ''  ">#{alarmTypeName},</if>
            <if test="eventType != null  and eventType != ''  ">#{eventType},</if>
            <if test="eventTypeName != null  and eventTypeName != ''  ">#{eventTypeName},</if>
            <if test="notifyPerson != null  and notifyPerson != ''  ">#{notifyPerson},</if>
            <if test="notifyPersonName != null  and notifyPersonName != ''  ">#{notifyPersonName},</if>
            <if test="delFlag != null  and delFlag != ''  ">#{delFlag},</if>
            <if test="createTime != null  ">#{createTime},</if>
            <if test="createBy != null  and createBy != ''  ">#{createBy},</if>
            <if test="updateTime != null  ">#{updateTime},</if>
            <if test="updateBy != null  and updateBy != ''  ">#{updateBy},</if>
        </trim>
    </insert>


    <!-- 表字段 -->
    <sql id="column">
        id,  <!--  -->
        alarm_type,  <!-- 告警模块编码 SecurityAlarm-安防告警  FireAlarm-消防告警 -->
        alarm_type_name,  <!-- 告警模块名称 SecurityAlarm-安防告警  FireAlarm-消防告警 -->
        event_type,  <!-- 告警类别编码 -->
        event_type_name,  <!-- 告警类别名称 -->
        notify_person,  <!-- 告警通知人 -->
        notify_person_name,  <!-- 告警通知人姓名 -->
        del_flag,  <!-- 0-删除，1-正常 -->
        create_time,  <!-- 创建时间 -->
        create_by,  <!-- 创建人 -->
        update_time,  <!-- 修改时间 -->
        update_by  <!-- 修改人 -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="id != null ">
            and id = #{id}  <!--  -->
        </if>
        <!-- 告警模块编码 SecurityAlarm-安防告警  FireAlarm-消防告警 -->
        <if test="alarmType != null  and alarmType != ''  ">and alarm_type = #{alarmType}</if>
        <!-- 告警模块名称 SecurityAlarm-安防告警  FireAlarm-消防告警 -->
        <if test="alarmTypeName != null  and alarmTypeName != ''  ">and alarm_type_name = #{alarmTypeName}</if>
        <!-- 告警类别编码 -->
        <if test="eventType != null  and eventType != ''  ">and event_type = #{eventType}</if>
        <!-- 告警类别名称 -->
        <if test="eventTypeName != null  and eventTypeName != ''  ">and event_type_name = #{eventTypeName}</if>
        <!-- 告警通知人 -->
        <if test="notifyPerson != null  and notifyPerson != ''  ">and notify_person = #{notifyPerson}</if>
        <!-- 告警通知人姓名 -->
        <if test="notifyPersonName != null  and notifyPersonName != ''  ">and notify_person_name = #{notifyPersonName}</if>

        <if test="delFlag != null and delFlag != ''">
            and del_flag = #{delFlag}  <!-- 0-删除，1-正常 -->
        </if>
        <if test="createTime != null ">
            and create_time = #{createTime}  <!-- 创建时间 -->
        </if>
        <if test="createBy != null and createBy != ''">
            and create_by = #{createBy}  <!-- 创建人 -->
        </if>
        <if test="updateTime != null ">
            and update_time = #{updateTime}  <!-- 修改时间 -->
        </if>
        <if test="updateBy != null and updateBy != ''">
            and update_by = #{updateBy}  <!-- 修改人 -->
        </if>
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <!-- 告警模块编码 SecurityAlarm-安防告警  FireAlarm-消防告警 -->
        <if test="alarmType != null  and alarmType != ''  ">and alarm_type like concat('%', #{alarmType}, '%')</if>
        <!-- 告警模块名称 SecurityAlarm-安防告警  FireAlarm-消防告警 -->
        <if test="alarmTypeName != null  and alarmTypeName != ''  ">and alarm_type_name like concat('%', #{alarmTypeName}, '%')</if>
        <!-- 告警类别编码 -->
        <if test="eventType != null  and eventType != ''  ">and event_type like concat('%', #{eventType}, '%')</if>
        <!-- 告警类别名称 -->
        <if test="eventTypeName != null  and eventTypeName != ''  ">and event_type_name like concat('%', #{eventTypeName}, '%')</if>
        <!-- 告警通知人 -->
        <if test="notifyPerson != null  and notifyPerson != ''  ">and notify_person like concat('%', #{notifyPerson}, '%')</if>
        <!-- 告警通知人姓名 -->
        <if test="notifyPersonName != null  and notifyPersonName != ''  ">and notify_person_name like concat('%', #{notifyPersonName}, '%')</if>

        <if test="delFlag != null and delFlag != ''">
            and del_flag like concat('%', #{delFlag}, '%')  <!-- 0-删除，1-正常 -->
        </if>
        <if test="createBy != null and createBy != ''">
            and create_by like concat('%', #{createBy}, '%')  <!-- 创建人 -->
        </if>
        <if test="updateBy != null and updateBy != ''">
            and update_by like concat('%', #{updateBy}, '%')  <!-- 修改人 -->
        </if>
    </sql>

    <select id="getAlarmEventTypeList" resultType="java.util.Map">
        SELECT
            dict.`code` as eventType,
            dict.`name` as eventTypeName
        FROM sys_dict dict
        INNER JOIN sys_dict_relevance rel on rel.dict_id = dict.id
        INNER JOIN sys_dict_index i on i.id = rel.index_id
        INNER JOIN sys_dict_type t on t.id = i.dict_type
        WHERE t.remarks = #{alarmTypeCode}
        <if test="eventTypeName != null and eventTypeName != ''">
            AND dict.`name` LIKE CONCAT("%", #{eventTypeName}, "%")
        </if>
    </select>


    <select id="getPersonList" resultType="com.huazheng.tunny.admin.api.entity.SysUser">
        SELECT username, user_realname
        FROM sys_user
        <where>
            <if test="userRealname != null and userRealname != ''">
                AND (user_realname like concat('%', #{userRealname}, '%') OR username like concat('%', #{userRealname}, '%'))
            </if>
        </where>
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.smarkpark.mapper.ParkingSourceMapper">

    <!-- 查询对象List -->
    <select id="selectParkingBarrierGate" resultType="ParkingBarrierGate">
        SELECT spc.id as channelId,  spc.cName as channelName, spc.cIOType as channelType,
               spc.cNumber as deviceSn, spc.cCameraIP as cameraIp,
               spco.device1State as deviceStatus
        FROM Sys_Park_Control spc
        left join Sys_Park_Control_Other spco on spc.id = spco.controlId
    </select>

    <select id="selectVehicleRecord" parameterType="String" resultType="ParkingVehicleRecord">
        SELECT parkInOutOrder as id,
               inWay,
               null           as outWay,
               plateNumber,
               authType,
               plateType,
               plateColor,
               InParkTime as inParkTime,
               InParkControlName as inParkChannelName,
               InParkControlNumber as inParkChannelNumber,
               InParkUserName as inParkChannelUser,
               null           AS outParkTime,
               null           AS outParkChannelName,
               null           as outParkChannelNumber,
               null           as outParkChannelUser,
               NULL           as parkHourStr,
               null           as parkHourMinutes
        FROM Sys_Park_InParkRecords
        WHERE InParkTime > #{startTme}
        UNION
        SELECT parkInOutOrder as id,
               inWay,
               inWay          as outWay,
               plateNumber,
               authType,
               plateType,
               plateColor,
               InParkTime as inParkTime,
               InParkControlName as inParkChannelName,
               InParkControlNumber as inParkChannelNumber,
               InParkUserName as inParkChannelUser,
               OutParkTime as outParkTime,
               OutParkControlName as outParkChannelName,
               OutParkControlNumber as outParkChannelNumber,
               OutParkUserName as outParkChannelUser,
               parkHourStr,
               parkHourMinutes
        FROM Sys_Park_OutParkRecords
        WHERE OutParkTime > #{startTme}
    </select>

    <!-- 查询对象List -->
    <select id="selectParkingSpacesNum" resultType="java.lang.Integer">
        SELECT sum(allTotalCount) total FROM Sys_Park_Area
    </select>

</mapper>

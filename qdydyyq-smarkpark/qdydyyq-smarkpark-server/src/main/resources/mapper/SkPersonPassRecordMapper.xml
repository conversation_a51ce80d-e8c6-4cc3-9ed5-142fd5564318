<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.smarkpark.mapper.SkPersonPassRecordMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.smarkpark.api.entity.SkPersonPassRecord" id="skPersonPassRecordResult">
        <result property="id" column="id"/> <!-- 序列 -->
        <result property="userId" column="user_id"/> <!-- 员工工号，长度16 -->
        <result property="passTime" column="pass_time"/> <!-- 通行时间 -->
        <result property="srcIndex" column="src_index"/> <!-- 出入口标识，最大长度64 -->
    </resultMap>


    <sql id="selectSkPersonPassRecordVo">
        select id, user_id, pass_time, src_index, gate.gate_name, u.user_realname
        from sk_person_pass_record
        left join (select src_index as src_code, `name` as gate_name from sk_gate_list) gate on gate.src_code = src_index
        left join (select empno, user_realname from sys_user) u on u.empno = user_id
    </sql>
    <!-- 查询对象List -->
    <select id="selectSkPersonPassRecordList" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkPersonPassRecord" resultMap="skPersonPassRecordResult">
        <include refid="selectSkPersonPassRecordVo"/>
        <where>
            <include refid="equal"/>
        </where>
        order by pass_time DESC
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectSkPersonPassRecordListByLike" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkPersonPassRecord" resultMap="skPersonPassRecordResult">
        <include refid="selectSkPersonPassRecordVo"/>
        <where>
            <include refid="like"/>
        </where>
        order by pass_time DESC
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectSkPersonPassRecordById" parameterType="Integer" resultMap="skPersonPassRecordResult">
        <include refid="selectSkPersonPassRecordVo"/>
        where id = #{id}
    </select>


    <update id="updateSkPersonPassRecord" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkPersonPassRecord">
        update sk_person_pass_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null  and userId != ''  ">user_id = #{userId},</if>
            <if test="passTime != null  and passTime != ''  ">pass_time = #{passTime},</if>
            <if test="srcIndex != null  and srcIndex != ''  ">src_index = #{srcIndex},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSkPersonPassRecordById" parameterType="Integer">
        delete
        from sk_person_pass_record
        where id = #{id}
    </delete>

    <delete id="deleteSkPersonPassRecordByIds" parameterType="Integer">
        delete from sk_person_pass_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <!-- 新增对象 -->
    <insert id="insertSkPersonPassRecord" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkPersonPassRecord" useGeneratedKeys="true" keyProperty="id">
        insert into sk_person_pass_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null  and userId != ''  ">user_id,</if>
            <if test="passTime != null  and passTime != ''  ">pass_time,</if>
            <if test="srcIndex != null  and srcIndex != ''  ">src_index,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null  and userId != ''  ">#{userId},</if>
            <if test="passTime != null  and passTime != ''  ">#{passTime},</if>
            <if test="srcIndex != null  and srcIndex != ''  ">#{srcIndex},</if>
        </trim>
    </insert>


    <!-- 表字段 -->
    <sql id="column">
        id,  <!-- 序列 -->
        user_id,  <!-- 员工工号，长度16 -->
        pass_time,  <!-- 通行时间 -->
        src_index  <!-- 出入口标识，最大长度64 -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="id != null ">
            and id = #{id}  <!-- 序列 -->
        </if>
        <if test="userId != null and userId != ''">
            and user_id = #{userId}  <!-- 员工工号，长度16 -->
        </if>
        <if test="passTime != null and passTime != ''">
            and pass_time = #{passTime}  <!-- 通行时间 -->
        </if>
        <if test="srcIndex != null and srcIndex != ''">
            and src_index = #{srcIndex}  <!-- 出入口标识，最大长度64 -->
        </if>
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <if test="userId != null and userId != ''">
            and user_id like concat('%', #{userId}, '%')  <!-- 员工工号，长度16 -->
        </if>
        <if test="passTime != null and passTime != ''">
            and pass_time like concat('%', #{passTime}, '%')  <!-- 通行时间 -->
        </if>
        <if test="srcIndex != null and srcIndex != ''">
            and src_index like concat('%', #{srcIndex}, '%')  <!-- 出入口标识，最大长度64 -->
        </if>
        <if test="startTime != null and startTime != ''">
            and pass_time &gt;= #{startTime} <!-- 通行时间 -->
        </if>
        <if test="endTime != null and endTime != ''">
            and pass_time &lt;= #{endTime} <!-- 通行时间 -->
        </if>
        <if test="gateName != null and gateName != ''">
            and gate_name  like concat('%', #{gateName}, '%') <!-- 道闸名称 -->
        </if>
        <if test="userRealname != null and userRealname != ''">
            and user_realname  like concat('%', #{userRealname}, '%') <!-- 道闸名称 -->
        </if>
    </sql>

    <!-- 插入过人记录 -->
    <insert id="syncSkPersonPassRecord">
        INSERT INTO sk_person_pass_record (user_id, pass_time, src_index)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.userId}, #{item.passTime}, #{item.srcIndex})
        </foreach>
    </insert>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.smarkpark.mapper.ZhoujieZoneCamerasMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.smarkpark.api.entity.ZhoujieZoneCameras" id="zhoujieZoneCamerasResult">
        <result property="id" column="id"/> <!--  -->
        <result property="deviceId" column="device_id"/> <!-- 设备id -->
        <result property="zoneId" column="zone_id"/> <!-- 防区id -->
        <result property="cameraCode" column="camera_code"/> <!-- 摄像头编码(通道编码) -->
        <result property="delFlag" column="del_flag"/> <!-- 0-删除，1-正常 -->
        <result property="createTime" column="create_time"/> <!-- 创建时间 -->
        <result property="updateTime" column="update_time"/> <!-- 修改时间 -->
        <result property="createBy" column="create_by"/> <!-- 创建人 -->
        <result property="updateBy" column="update_by"/> <!-- 修改人 -->
        <result property="remark" column="remark"/> <!-- 备注 -->
    </resultMap>


    <sql id="selectZhoujieZoneCamerasVo">
        select id,
               device_id,
               zone_id,
               camera_code,
               del_flag,
               create_time,
               update_time,
               create_by,
               update_by,
               remark
        from sk_zhoujie_zone_cameras
    </sql>
    <!-- 查询对象List -->
    <select id="selectZhoujieZoneCamerasList" parameterType="ZhoujieZoneCameras" resultMap="zhoujieZoneCamerasResult">
        <include refid="selectZhoujieZoneCamerasVo"/>
        <where>
            <include refid="equal"/>
        </where>
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectZhoujieZoneCamerasListByLike" parameterType="com.huazheng.tunny.smarkpark.api.dto.DaHuaCamerasDTO"
            resultType="com.huazheng.tunny.smarkpark.api.dto.DaHuaCamerasDTO">
        select szzc.id, sdc.channel_code,sdc.channel_name,sdc.channel_state,sdc.channel_state,sdc.channel_device_ip from
        sk_dahua_cameras sdc
        left join sk_zhoujie_zone_cameras szzc on sdc.channel_code = szzc.camera_code
        where szzc.del_flag = 1
        <if test="channelCode != null  and channelCode != ''  ">
            and sdc.channel_code like concat('%',#{channelCode},'%')
        </if>
        <if test="channelName != null  and channelName != ''  ">
            and sdc.channel_name like concat('%',#{channelName},'%')
        </if>
        <if test="channelState != null  and channelState != ''  ">
            and sdc.channel_state = #{channelState}
        </if>
        <if test="channelDeviceIp != null  and channelDeviceIp != ''  ">
            and sdc.channel_device_ip = #{channelDeviceIp}
        </if>
        <if test="zoneId != null ">
            and szzc.zone_id = #{zoneId}
        </if>
        <if test="deviceId != null ">
            and szzc.device_id = #{deviceId}
        </if>
        order by sdc.channel_code desc
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectZhoujieZoneCamerasById" parameterType="Long" resultMap="zhoujieZoneCamerasResult">
        <include refid="selectZhoujieZoneCamerasVo"/>
        where id = #{id}
    </select>


    <update id="updateZhoujieZoneCameras" parameterType="ZhoujieZoneCameras">
        update sk_zhoujie_zone_cameras
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceId != null  ">device_id = #{deviceId},</if>
            <if test="zoneId != null  ">zone_id = #{zoneId},</if>
            <if test="cameraCode != null  and cameraCode != ''  ">camera_code = #{cameraCode},</if>
            <if test="delFlag != null  and delFlag != ''  ">del_flag = #{delFlag},</if>
            <if test="createTime != null  ">create_time = #{createTime},</if>
            <if test="updateTime != null  ">update_time = #{updateTime},</if>
            <if test="createBy != null  and createBy != ''  ">create_by = #{createBy},</if>
            <if test="updateBy != null  and updateBy != ''  ">update_by = #{updateBy},</if>
            <if test="remark != null  and remark != ''  ">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteZhoujieZoneCamerasById" parameterType="Long">
        update sk_zhoujie_zone_cameras
        set del_flag = 0
        where id = #{id}
    </delete>

    <delete id="deleteZhoujieZoneCameras" parameterType="ZhoujieZoneCameras">
        update sk_zhoujie_zone_cameras set del_flag = 0
        where device_id = #{deviceId} and zone_id = #{zoneId}
    </delete>

    <delete id="deleteZhoujieZoneCamerasByIds" parameterType="Integer">
        delete from sk_zhoujie_zone_cameras where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <!-- 新增对象 -->
    <insert id="insertZhoujieZoneCameras" parameterType="ZhoujieZoneCameras" useGeneratedKeys="true" keyProperty="id">
        insert into sk_zhoujie_zone_cameras
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceId != null  ">device_id,</if>
            <if test="zoneId != null  ">zone_id,</if>
            <if test="cameraCode != null  and cameraCode != ''  ">camera_code,</if>
            <if test="delFlag != null  and delFlag != ''  ">del_flag,</if>
            <if test="createTime != null  ">create_time,</if>
            <if test="updateTime != null  ">update_time,</if>
            <if test="createBy != null  and createBy != ''  ">create_by,</if>
            <if test="updateBy != null  and updateBy != ''  ">update_by,</if>
            <if test="remark != null  and remark != ''  ">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceId != null  ">#{deviceId},</if>
            <if test="zoneId != null  ">#{zoneId},</if>
            <if test="cameraCode != null  and cameraCode != ''  ">#{cameraCode},</if>
            <if test="delFlag != null  and delFlag != ''  ">#{delFlag},</if>
            <if test="createTime != null  ">#{createTime},</if>
            <if test="updateTime != null  ">#{updateTime},</if>
            <if test="createBy != null  and createBy != ''  ">#{createBy},</if>
            <if test="updateBy != null  and updateBy != ''  ">#{updateBy},</if>
            <if test="remark != null  and remark != ''  ">#{remark},</if>
        </trim>
    </insert>


    <!-- 表字段 -->
    <sql id="column">
        id,  <!--  -->
        device_id,  <!-- 设备id -->
        zone_id,  <!-- 防区id -->
        camera_code,  <!-- 摄像头编码(通道编码) -->
        del_flag,  <!-- 0-删除，1-正常 -->
        create_time,  <!-- 创建时间 -->
        update_time,  <!-- 修改时间 -->
        create_by,  <!-- 创建人 -->
        update_by,  <!-- 修改人 -->
        remark  <!-- 备注 -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        del_flag = 1
        <if test="id != null ">
            and id = #{id}  <!--  -->
        </if>
        <if test="deviceId != null ">
            and device_id = #{deviceId}  <!-- 设备id -->
        </if>
        <if test="zoneId != null ">
            and zone_id = #{zoneId}  <!-- 防区id -->
        </if>
        <if test="cameraCode != null and cameraCode != ''">
            and camera_code = #{cameraCode}  <!-- 摄像头编码(通道编码) -->
        </if>
        <if test="delFlag != null and delFlag != ''">
            and del_flag = #{delFlag}  <!-- 0-删除，1-正常 -->
        </if>
        <if test="createTime != null ">
            and create_time = #{createTime}  <!-- 创建时间 -->
        </if>
        <if test="updateTime != null ">
            and update_time = #{updateTime}  <!-- 修改时间 -->
        </if>
        <if test="createBy != null and createBy != ''">
            and create_by = #{createBy}  <!-- 创建人 -->
        </if>
        <if test="updateBy != null and updateBy != ''">
            and update_by = #{updateBy}  <!-- 修改人 -->
        </if>
        <if test="remark != null and remark != ''">
            and remark = #{remark}  <!-- 备注 -->
        </if>
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        del_flag = 1
        <if test="cameraCode != null and cameraCode != ''">
            and camera_code like concat('%', #{cameraCode}, '%')  <!-- 摄像头编码(通道编码) -->
        </if>
        <if test="delFlag != null and delFlag != ''">
            and del_flag like concat('%', #{delFlag}, '%')  <!-- 0-删除，1-正常 -->
        </if>
        <if test="createBy != null and createBy != ''">
            and create_by like concat('%', #{createBy}, '%')  <!-- 创建人 -->
        </if>
        <if test="updateBy != null and updateBy != ''">
            and update_by like concat('%', #{updateBy}, '%')  <!-- 修改人 -->
        </if>
        <if test="remark != null and remark != ''">
            and remark like concat('%', #{remark}, '%')  <!-- 备注 -->
        </if>
    </sql>
</mapper>

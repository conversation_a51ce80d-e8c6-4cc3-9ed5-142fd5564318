<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.smarkpark.mapper.ZhoujieZoneMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.smarkpark.api.entity.ZhoujieZone" id="zhoujieZoneResult">
        <result property="id" column="id"/> <!--  -->
        <result property="deviceId" column="device_id"/> <!-- 设备id -->
        <result property="zoneId" column="zone_id"/> <!-- 防区 id -->
        <result property="zoneType" column="zone_type"/> <!-- 防区类型 -->
        <result property="zoneTypeName" column="zone_type_name"/> <!-- 防区类型名称 -->
        <result property="zoneName" column="zone_name"/> <!-- 防区名称 -->
        <result property="zoneStatus" column="zone_status"/> <!-- 防区状态(0：未知,1：在线，2：离线,3：报警,4：未准备) -->
        <result property="zoneArmingStatus" column="zone_arming_status"/> <!-- 布防状态(0-未知状态,1-外出布防,2-在家布防,3-撤防状态,4-布防延时) -->
    </resultMap>


    <sql id="selectZhoujieZoneVo">
        select id,
               device_id,
               zone_id,
               zone_type,
               zone_type_name,
               zone_name,
               zone_status,
               zone_arming_status
        from sk_zhoujie_zone
    </sql>
    <!-- 查询对象List -->
    <select id="selectZhoujieZoneList" parameterType="ZhoujieZone" resultMap="zhoujieZoneResult">
        <include refid="selectZhoujieZoneVo"/>
        <where>
            <include refid="equal"/>
        </where>
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectZhoujieZoneListByLike" parameterType="ZhoujieZone" resultMap="zhoujieZoneResult">
        <include refid="selectZhoujieZoneVo"/>
        <where>
            <include refid="like"/>
        </where>
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectZhoujieZoneById" parameterType="Long" resultMap="zhoujieZoneResult">
        <include refid="selectZhoujieZoneVo"/>
        where id = #{id}
    </select>


    <update id="updateZhoujieZone" parameterType="ZhoujieZone">
        update sk_zhoujie_zone
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceId != null  ">device_id = #{deviceId},</if>
            <if test="zoneId != null  ">zone_id = #{zoneId},</if>
            <if test="zoneType != null  ">zone_type = #{zoneType},</if>
            <if test="zoneTypeName != null  and zoneTypeName != ''  ">zone_type_name = #{zoneTypeName},</if>
            <if test="zoneName != null  and zoneName != ''  ">zone_name = #{zoneName},</if>
            <if test="zoneStatus != null  ">zone_status = #{zoneStatus},</if>
            <if test="zoneArmingStatus != null  ">zone_arming_status = #{zoneArmingStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteZhoujieZoneById" parameterType="Long">
        delete
        from sk_zhoujie_zone
        where id = #{id}
    </delete>

    <delete id="deleteZhoujieZoneByIds" parameterType="Integer">
        delete from sk_zhoujie_zone where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <!-- 新增对象 -->
    <insert id="insertZhoujieZone" parameterType="ZhoujieZone" useGeneratedKeys="true" keyProperty="id">
        insert into sk_zhoujie_zone
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceId != null  ">device_id,</if>
            <if test="zoneId != null  ">zone_id,</if>
            <if test="zoneType != null  ">zone_type,</if>
            <if test="zoneTypeName != null  and zoneTypeName != ''  ">zone_type_name,</if>
            <if test="zoneName != null  and zoneName != ''  ">zone_name,</if>
            <if test="zoneStatus != null  ">zone_status,</if>
            <if test="zoneArmingStatus != null  ">zone_arming_status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceId != null  ">#{deviceId},</if>
            <if test="zoneId != null  ">#{zoneId},</if>
            <if test="zoneType != null  ">#{zoneType},</if>
            <if test="zoneTypeName != null  and zoneTypeName != ''  ">#{zoneTypeName},</if>
            <if test="zoneName != null  and zoneName != ''  ">#{zoneName},</if>
            <if test="zoneStatus != null  ">#{zoneStatus},</if>
            <if test="zoneArmingStatus != null  ">#{zoneArmingStatus},</if>
        </trim>
    </insert>


    <!-- 表字段 -->
    <sql id="column">
        id,  <!--  -->
        device_id,  <!-- 设备id -->
        zone_id,  <!-- 防区 id -->
        zone_type,  <!-- 防区类型 -->
        zone_type_name,  <!-- 防区类型名称 -->
        zone_name,  <!-- 防区名称 -->
        zone_status,  <!-- 防区状态(0：未知,1：在线，2：离线,3：报警,4：未准备) -->
        zone_arming_status  <!-- 布防状态(0-未知状态,1-外出布防,2-在家布防,3-撤防状态,4-布防延时) -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="id != null ">
            and id = #{id}  <!--  -->
        </if>
        <if test="deviceId != null ">
            and device_id = #{deviceId}  <!-- 设备id -->
        </if>
        <if test="zoneId != null ">
            and zone_id = #{zoneId}  <!-- 防区 id -->
        </if>
        <if test="zoneType != null ">
            and zone_type = #{zoneType}  <!-- 防区类型 -->
        </if>
        <if test="zoneTypeName != null and zoneTypeName != ''">
            and zone_type_name = #{zoneTypeName}  <!-- 防区类型名称 -->
        </if>
        <if test="zoneName != null and zoneName != ''">
            and zone_name = #{zoneName}  <!-- 防区名称 -->
        </if>
        <if test="zoneStatus != null ">
            and zone_status = #{zoneStatus}  <!-- 防区状态(0：未知,1：在线，2：离线,3：报警,4：未准备) -->
        </if>
        <if test="zoneArmingStatus != null ">
            and zone_arming_status = #{zoneArmingStatus}  <!-- 布防状态(0-未知状态,1-外出布防,2-在家布防,3-撤防状态,4-布防延时) -->
        </if>
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <if test="deviceId != null ">
            and device_id = #{deviceId}  <!-- 设备id -->
        </if>
        <if test="zoneId != null ">
            and zone_id = #{zoneId}  <!-- 防区 id -->
        </if>
        <if test="zoneStatus != null ">
            and zone_status = #{zoneStatus}  <!-- 防区状态(0：未知,1：在线，2：离线,3：报警,4：未准备) -->
        </if>
        <if test="zoneArmingStatus != null ">
            and zone_arming_status = #{zoneArmingStatus}  <!-- 布防状态(0-未知状态,1-外出布防,2-在家布防,3-撤防状态,4-布防延时) -->
        </if>
        <if test="zoneTypeName != null and zoneTypeName != ''">
            and zone_type_name like concat('%', #{zoneTypeName}, '%')  <!-- 防区类型名称 -->
        </if>
        <if test="zoneName != null and zoneName != ''">
            and zone_name like concat('%', #{zoneName}, '%')  <!-- 防区名称 -->
        </if>
    </sql>
</mapper>

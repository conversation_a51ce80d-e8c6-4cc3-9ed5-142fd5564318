<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.smarkpark.mapper.SkBuildControlDeviceAttributeMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.smarkpark.api.entity.SkBuildControlDeviceAttribute"
               id="skBuildControlDeviceAttributeResult">
        <result property="id" column="id"/> <!-- 主键id -->
        <result property="deviceCode" column="device_code"/> <!-- 设备编号 -->
        <result property="attributeCode" column="attribute_code"/> <!-- 属性名称 -->
        <result property="attributeName" column="attribute_name"/> <!-- 属性名称 -->
        <result property="attributeVal" column="attribute_val"/> <!-- 属性值 -->
        <result property="attributeUnit" column="attribute_unit"/> <!-- 属性单位 -->
        <result property="reportTime" column="report_time"/> <!-- 上报时间 -->
    </resultMap>


    <sql id="selectSkBuildControlDeviceAttributeVo">
        select id, device_code, attribute_code, attribute_name, attribute_val, attribute_unit, report_time
        from sk_build_control_device_attribute
    </sql>
    <!-- 查询对象List -->
    <select id="selectSkBuildControlDeviceAttributeList" parameterType="SkBuildControlDeviceAttribute"
            resultMap="skBuildControlDeviceAttributeResult">
        <include refid="selectSkBuildControlDeviceAttributeVo"/>
        <where>
            <include refid="equal"/>
        </where>
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectSkBuildControlDeviceAttributeListByLike" parameterType="SkBuildControlDeviceAttribute"
            resultMap="skBuildControlDeviceAttributeResult">
        <include refid="selectSkBuildControlDeviceAttributeVo"/>
        <where>
            <include refid="like"/>
        </where>
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectSkBuildControlDeviceAttributeById" parameterType="Long"
            resultMap="skBuildControlDeviceAttributeResult">
        <include refid="selectSkBuildControlDeviceAttributeVo"/>
        where id = #{id}
    </select>


    <update id="updateSkBuildControlDeviceAttribute" parameterType="SkBuildControlDeviceAttribute">
        update sk_build_control_device_attribute
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceCode != null  and deviceCode != ''  ">device_code = #{deviceCode},</if>
            <if test="attributeCode != null  and attributeCode != ''  ">attribute_code = #{attributeCode},</if>
            <if test="attributeName != null  and attributeName != ''  ">attribute_name = #{attributeName},</if>
            <if test="attributeVal != null  and attributeVal != ''  ">attribute_val = #{attributeVal},</if>
            <if test="attributeUnit != null  and attributeUnit != ''  ">attribute_unit = #{attributeUnit},</if>
            <if test="reportTime != null  ">report_time = #{reportTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDeviceAttributeByDeviceCode" parameterType="String">
        delete from sk_build_control_device_attribute
        where device_code = #{deviceCode}
    </delete>

    <delete id="deleteDeviceAttributeByLightingDevice" >
        delete from sk_build_control_device_attribute
        where device_code in (select sbcd.device_code from sk_build_control_device sbcd where sbcd.type = 5)
    </delete>


    <delete id="deleteSkBuildControlDeviceAttributeById" parameterType="Long">
        delete
        from sk_build_control_device_attribute
        where id = #{id}
    </delete>

    <delete id="deleteSkBuildControlDeviceAttributeByIds" parameterType="Integer">
        delete from sk_build_control_device_attribute where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertSkBuildControlDeviceAttributeList">
        INSERT INTO sk_build_control_device_attribute (device_code, attribute_code, attribute_name, attribute_val,
                                      attribute_unit,report_time) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.deviceCode}, #{item.attributeCode}, #{item.attributeName}, #{item.attributeVal},
             #{item.attributeUnit}, #{item.reportTime})
        </foreach>
    </insert>

    <!-- 新增对象 -->
    <insert id="insertSkBuildControlDeviceAttribute" parameterType="SkBuildControlDeviceAttribute"
            useGeneratedKeys="true" keyProperty="id">
        insert into sk_build_control_device_attribute
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceCode != null  and deviceCode != ''  ">device_code,</if>
            <if test="attributeCode != null  and attributeCode != ''  ">attribute_code,</if>
            <if test="attributeName != null  and attributeName != ''  ">attribute_name,</if>
            <if test="attributeVal != null  and attributeVal != ''  ">attribute_val,</if>
            <if test="attributeUnit != null  and attributeUnit != ''  ">attribute_unit,</if>
            <if test="reportTime != null  ">report_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceCode != null  and deviceCode != ''  ">#{deviceCode},</if>
            <if test="attributeCode != null  and attributeCode != ''  ">#{attributeCode},</if>
            <if test="attributeName != null  and attributeName != ''  ">#{attributeName},</if>
            <if test="attributeVal != null  and attributeVal != ''  ">#{attributeVal},</if>
            <if test="attributeUnit != null  and attributeUnit != ''  ">#{attributeUnit},</if>
            <if test="reportTime != null  ">#{reportTime},</if>
        </trim>
    </insert>


    <!-- 表字段 -->
    <sql id="column">
        id,  <!-- 主键id -->
        device_code,  <!-- 设备编号 -->
        attribute_code,  <!-- 属性名称 -->
        attribute_name,  <!-- 属性名称 -->
        attribute_val,  <!-- 属性值 -->
        attribute_unit,  <!-- 属性单位 -->
        report_time  <!-- 上报时间 -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="id != null ">
            and id = #{id}  <!-- 主键id -->
        </if>
        <if test="deviceCode != null and deviceCode != ''">
            and device_code = #{deviceCode}  <!-- 设备编号 -->
        </if>
        <if test="attributeCode != null and attributeCode != ''">
            and attribute_code = #{attributeCode}  <!-- 属性名称 -->
        </if>
        <if test="attributeName != null and attributeName != ''">
            and attribute_name = #{attributeName}  <!-- 属性名称 -->
        </if>
        <if test="attributeVal != null and attributeVal != ''">
            and attribute_val = #{attributeVal}  <!-- 属性值 -->
        </if>
        <if test="attributeUnit != null and attributeUnit != ''">
            and attribute_unit = #{attributeUnit}  <!-- 属性单位 -->
        </if>
        <if test="reportTime != null ">
            and report_time = #{reportTime}  <!-- 上报时间 -->
        </if>
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <if test="deviceCode != null and deviceCode != ''">
            and device_code like concat('%', #{deviceCode}, '%')  <!-- 设备编号 -->
        </if>
        <if test="attributeCode != null and attributeCode != ''">
            and attribute_code like concat('%', #{attributeCode}, '%')  <!-- 属性名称 -->
        </if>
        <if test="attributeName != null and attributeName != ''">
            and attribute_name like concat('%', #{attributeName}, '%')  <!-- 属性名称 -->
        </if>
        <if test="attributeVal != null and attributeVal != ''">
            and attribute_val like concat('%', #{attributeVal}, '%')  <!-- 属性值 -->
        </if>
        <if test="attributeUnit != null and attributeUnit != ''">
            and attribute_unit like concat('%', #{attributeUnit}, '%')  <!-- 属性单位 -->
        </if>
    </sql>
</mapper>

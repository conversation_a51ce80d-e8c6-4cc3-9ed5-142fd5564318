<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.smarkpark.mapper.SkCockpitMapPointsMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.smarkpark.api.entity.SkCockpitMapPoints" id="skCockpitMapPointsResult">
        <result property="id" column="id"/> <!-- 唯一ID -->
        <result property="parentId" column="parent_id"/> <!-- 父级ID -->
        <result property="pointType" column="point_type"/> <!-- 点位类型 0-厂区图 1-厂区点位 2-监控点 -->
        <result property="title" column="title"/> <!-- 点位标题 -->
        <result property="abbrTitle" column="abbr_title"/> <!-- 点位标题简称 -->
        <result property="coordinateX" column="coordinate_x"/> <!-- x轴位置 -->
        <result property="coordinateY" column="coordinate_y"/> <!-- y轴位置 -->
        <result property="mapUrl" column="map_url"/> <!-- 厂区剖面图URL -->
        <result property="indexCode" column="index_code"/> <!-- 监控点编号 -->
        <result property="regionName" column="region_name"/> <!-- 监控点所属海康区域 -->
        <result property="monitIconSize" column="monit_icon_size"/> <!-- 监控图标的尺寸 1-normal 2-mini 3-small 4-large -->
        <result property="disabled" column="disabled"/> <!-- 是否可以点击 0-否 1-是 -->
        <result property="online" column="online"/> <!-- 是否在线 0-离线 1-在线 -->
    </resultMap>


    <sql id="selectSkCockpitMapPointsVo">
        select id,
               parent_id,
               point_type,
               title,
               abbr_title,
               coordinate_x,
               coordinate_y,
               map_url,
               index_code,
               region_name,
               monit_icon_size,
               disabled,
               isDanger
        from sk_cockpit_map_points
    </sql>
    <!-- 查询对象List -->
    <select id="selectSkCockpitMapPointsList" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkCockpitMapPoints" resultMap="skCockpitMapPointsResult">
        SELECT
        id,
        parent_id,
        point_type,
        title,
        abbr_title,
        coordinate_x,
        coordinate_y,
        map_url,
        index_code,
        region_name,
        monit_icon_size,
        disabled,
        IFNULL(ds.online,0) online,
        IF(SUM(acl.alarmCount)>0, TRUE, FALSE) AS is_danger
        FROM sk_cockpit_map_points
        LEFT JOIN (
        SELECT IFNULL(log.alarmCount,0) as alarmCount, point_path AS pPth FROM sk_cockpit_map_points
        LEFT JOIN (
        <if test="clientId == null or clientId != 1 ">
            SELECT src_index AS deviceCode, COUNT(1) as alarmCount
            FROM sk_alarm_log
            WHERE handle_status = 0
            GROUP BY src_index
        </if>
        <if test="clientId != null and clientId == 1 ">
            SELECT szzc.camera_code AS deviceCode, COUNT(sal.event_id) as alarmCount
            FROM sk_alarm_log sal
            LEFT JOIN sk_zhoujie_zone_cameras szzc ON szzc.device_id = sal.src_index
            WHERE sal.handle_status = 0 AND szzc.del_flag = 1
            GROUP BY szzc.camera_code
        </if>
        ) log ON log.deviceCode = index_code
        ) AS acl ON acl.pPth LIKE CONCAT(point_path, "%")
        LEFT JOIN (
        <if test="clientId == null or clientId == 1 ">
            SELECT sdc.channel_code AS deviceCode, sdc.channel_state AS `online` from sk_dahua_cameras sdc
        </if>
        <if test="clientId != null and clientId == 2 ">
            select edd.id AS deviceCode,edd.status AS `online`
            from esp_door_device edd
        </if>
        <if test="clientId != null and clientId == 3 ">
            select sbcd.device_code AS deviceCode,sbcd.run_state AS `online`
            from sk_build_control_device sbcd
        </if>
        ) ds ON ds.deviceCode = index_code
        <where>
            <include refid="equal"/>
            and not (point_type>=2 and (coordinate_x is null or coordinate_x = ''))
        </where>
        GROUP BY id
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectSkCockpitMapPointsListByLike" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkCockpitMapPoints" resultMap="skCockpitMapPointsResult">
        <include refid="selectSkCockpitMapPointsVo"/>
        <where>
            <include refid="like"/>
        </where>
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectSkCockpitMapPointsById" parameterType="String" resultMap="skCockpitMapPointsResult">
        <include refid="selectSkCockpitMapPointsVo"/>
        where index_code = #{indexCode}
    </select>


    <update id="updateSkCockpitMapPoints" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkCockpitMapPoints">
        update sk_cockpit_map_points
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null  and parentId != ''  ">parent_id = #{parentId},</if>
            <if test="pointType != null  and pointType != ''  ">point_type = #{pointType},</if>
            <if test="title != null  and title != ''  ">title = #{title},</if>
            <if test="abbrTitle != null  and abbrTitle != ''  ">abbr_title = #{abbrTitle},</if>
            <if test="coordinateX != null  and coordinateX != ''  ">coordinate_x = #{coordinateX},</if>
            <if test="coordinateY != null  and coordinateY != ''  ">coordinate_y = #{coordinateY},</if>
            <if test="mapUrl != null  and mapUrl != ''  ">map_url = #{mapUrl},</if>
            <if test="indexCode != null  and indexCode != ''  ">index_code = #{indexCode},</if>
            <if test="regionName != null  and regionName != ''  ">region_name = #{regionName},</if>
            <if test="monitIconSize != null  and monitIconSize != ''  ">monit_icon_size = #{monitIconSize},</if>
            <if test="disabled != null  and disabled != ''  ">disabled = #{disabled},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSkCockpitMapPointsById" parameterType="String">
        delete
        from sk_cockpit_map_points
        where index_code = #{indexCode}
    </delete>

    <delete id="deleteSkCockpitMapPointsByIds" parameterType="Integer">
        delete from sk_cockpit_map_points where index_code in
        <foreach item="indexCode" collection="array" open="(" separator="," close=")">
            #{indexCode}
        </foreach>
    </delete>


    <!-- 新增对象 -->
    <insert id="insertSkCockpitMapPoints" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkCockpitMapPoints">
        insert into sk_cockpit_map_points
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null  and parentId != ''  ">parent_id,</if>
            <if test="pointType != null  and pointType != ''  ">point_type,</if>
            <if test="title != null  and title != ''  ">title,</if>
            <if test="abbrTitle != null  and abbrTitle != ''  ">abbr_title,</if>
            <if test="coordinateX != null  and coordinateX != ''  ">coordinate_x,</if>
            <if test="coordinateY != null  and coordinateY != ''  ">coordinate_y,</if>
            <if test="mapUrl != null  and mapUrl != ''  ">map_url,</if>
            <if test="indexCode != null  and indexCode != ''  ">index_code,</if>
            <if test="regionName != null  and regionName != ''  ">region_name,</if>
            <if test="monitIconSize != null  and monitIconSize != ''  ">monit_icon_size,</if>
            <if test="disabled != null  and disabled != ''  ">disabled,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null  and parentId != ''  ">#{parentId},</if>
            <if test="pointType != null  and pointType != ''  ">#{pointType},</if>
            <if test="title != null  and title != ''  ">#{title},</if>
            <if test="abbrTitle != null  and abbrTitle != ''  ">#{abbrTitle},</if>
            <if test="coordinateX != null  and coordinateX != ''  ">#{coordinateX},</if>
            <if test="coordinateY != null  and coordinateY != ''  ">#{coordinateY},</if>
            <if test="mapUrl != null  and mapUrl != ''  ">#{mapUrl},</if>
            <if test="indexCode != null  and indexCode != ''  ">#{indexCode},</if>
            <if test="regionName != null  and regionName != ''  ">#{regionName},</if>
            <if test="monitIconSize != null  and monitIconSize != ''  ">#{monitIconSize},</if>
            <if test="disabled != null  and disabled != ''  ">#{disabled},</if>
        </trim>
    </insert>


    <!-- 表字段 -->
    <sql id="column">
        id,  <!-- 唯一ID -->
        parent_id,  <!-- 父级ID -->
        point_type,  <!-- 点位类型 0-厂区图 1-厂区点位 2-监控点 -->
        title,  <!-- 点位标题 -->
        abbr_title,  <!-- 点位标题简称 -->
        coordinate_x,  <!-- x轴位置 -->
        coordinate_y,  <!-- y轴位置 -->
        map_url,  <!-- 厂区剖面图URL -->
        index_code,  <!-- 监控点编号 -->
        region_name,  <!-- 监控点所属海康区域 -->
        monit_icon_size,  <!-- 监控图标的尺寸 1-normal 2-mini 3-small 4-large -->
        disabled  <!-- 是否可以点击 0-否 1-是 -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <!-- 父级ID -->
        <if test="parentId != null  ">and parent_id = #{parentId}</if>
        <!-- 点位类型 0-厂区图 1-厂区点位 2-监控点 -->
        <if test="pointType != null  and pointType != ''  ">and point_type = #{pointType}</if>
        <!-- 点位标题 -->
        <if test="title != null  and title != ''  ">and title = #{title}</if>
        <!-- 点位标题简称 -->
        <if test="abbrTitle != null  and abbrTitle != ''  ">and abbr_title = #{abbrTitle}</if>
        <!-- x轴位置 -->
        <if test="coordinateX != null  and coordinateX != ''  ">and coordinate_x = #{coordinateX}</if>
        <!-- y轴位置 -->
        <if test="coordinateY != null  and coordinateY != ''  ">and coordinate_y = #{coordinateY}</if>
        <!-- 厂区剖面图URL -->
        <if test="mapUrl != null  and mapUrl != ''  ">and map_url = #{mapUrl}</if>
        <!-- 监控点编号 -->
        <if test="indexCode != null  and indexCode != ''  ">and index_code = #{indexCode}</if>
        <!-- 监控点所属海康区域 -->
        <if test="regionName != null  and regionName != ''  ">and region_name = #{regionName}</if>
        <!-- 监控图标的尺寸 1-normal 2-mini 3-small 4-large -->
        <if test="monitIconSize != null  and monitIconSize != ''  ">and monit_icon_size = #{monitIconSize}</if>
        <!-- 是否可以点击 0-否 1-是 -->
        <if test="disabled != null  and disabled != ''  ">and disabled = #{disabled}</if>
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <!-- 父级ID -->
        <if test="parentId != null  and parentId != ''  ">and parent_id like concat('%', #{parentId}, '%')</if>
        <!-- 点位类型 0-厂区图 1-厂区点位 2-监控点 -->
        <if test="pointType != null  and pointType != ''  ">and point_type like concat('%', #{pointType}, '%')</if>
        <!-- 点位标题 -->
        <if test="title != null  and title != ''  ">and title like concat('%', #{title}, '%')</if>
        <!-- 点位标题简称 -->
        <if test="abbrTitle != null  and abbrTitle != ''  ">and abbr_title like concat('%', #{abbrTitle}, '%')</if>
        <!-- x轴位置 -->
        <if test="coordinateX != null  and coordinateX != ''  ">and coordinate_x like concat('%', #{coordinateX}, '%')</if>
        <!-- y轴位置 -->
        <if test="coordinateY != null  and coordinateY != ''  ">and coordinate_y like concat('%', #{coordinateY}, '%')</if>
        <!-- 厂区剖面图URL -->
        <if test="mapUrl != null  and mapUrl != ''  ">and map_url like concat('%', #{mapUrl}, '%')</if>
        <!-- 监控点编号 -->
        <if test="indexCode != null  and indexCode != ''  ">and index_code like concat('%', #{indexCode}, '%')</if>
        <!-- 监控点所属海康区域 -->
        <if test="regionName != null  and regionName != ''  ">and region_name like concat('%', #{regionName}, '%')</if>
        <!-- 监控图标的尺寸 1-normal 2-mini 3-small 4-large -->
        <if test="monitIconSize != null  and monitIconSize != ''  ">and monit_icon_size like concat('%', #{monitIconSize}, '%')</if>
        <!-- 是否可以点击 0-否 1-是 -->
        <if test="disabled != null  and disabled != ''  ">and disabled like concat('%', #{disabled}, '%')</if>
    </sql>


    <select id="camerasStatistics" resultType="java.util.Map">
        SELECT COUNT(c.index_code) as total
             , SUM(IFNULL(ol.`online`, 0)) as onlineCount
             , SUM(IF(ol.`online` IS NULL OR ol.`online` = 0, 1, 0)) as offlineCount
        FROM sk_hdvision_cameras c
        LEFT JOIN (SELECT index_code AS iCode, `online` FROM sk_hdvision_cameras_online) ol ON ol.iCode = index_code
    </select>

    <select id="camerasOnlineRate" resultType="java.util.Map">
        SELECT '在线' as `name`
             , SUM(IFNULL(ol.`online`, 0)) as `value`
        FROM sk_hdvision_cameras c
        LEFT JOIN (SELECT index_code AS iCode, `online` FROM sk_hdvision_cameras_online) ol ON ol.iCode = index_code

        UNION ALL

        SELECT '离线'    as `name`
             , SUM(IF(ol.`online` IS NULL OR ol.`online` = 0, 1, 0)) as `value`
        FROM sk_hdvision_cameras c
        LEFT JOIN (SELECT index_code AS iCode, `online` FROM sk_hdvision_cameras_online) ol ON ol.iCode = index_code
    </select>

    <select id="fireFightEquipmentStatistics" resultType="java.util.Map">
        SELECT COUNT(id) as total
             , SUM(IF(`state` = 'online', 1, 0)) as onlineCount
             , SUM(IF(`state` != 'online', 1, 0)) as offlineCount
        FROM `smarkpark_iot`.th_iot_device
        WHERE del_flag = 0
    </select>

    <select id="fireFightEquipmentOnlineRate" resultType="java.util.Map">
        SELECT '在线' as `name`
             , SUM(IF(`state` = 'online', 1, 0)) as `value`
        FROM `smarkpark_iot`.th_iot_device
        WHERE del_flag = 0

        UNION ALL

        SELECT '离线' as `name`
             , SUM(IF(`state` != 'online', 1, 0)) as `value`
        FROM `smarkpark_iot`.th_iot_device
        WHERE del_flag = 0
    </select>
</mapper>

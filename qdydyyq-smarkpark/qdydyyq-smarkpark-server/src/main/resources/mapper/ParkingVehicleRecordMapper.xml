<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.smarkpark.mapper.ParkingVehicleRecordMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.smarkpark.api.entity.ParkingVehicleRecord" id="parkingVehicleRecordResult">
        <result property="id" column="id"/> <!-- id -->
        <result property="parkId" column="park_id"/> <!-- 车场编号 -->
        <result property="inWay" column="in_way"/> <!-- 入场方式 -->
        <result property="outWay" column="out_way"/> <!-- 出场方式 -->
        <result property="plateNumber" column="plate_number"/> <!-- 车牌号码 -->
        <result property="plateColor" column="plate_color"/> <!-- 车牌颜色 -->
        <result property="plateType" column="plate_type"/> <!-- 车辆类型 -->
        <result property="authType" column="auth_type"/> <!-- 车辆卡类型 -->
        <result property="inParkTime" column="in_park_time"/> <!-- 入场时间 -->
        <result property="inParkChannelNumber" column="in_park_channel_number"/> <!-- 入场通道编号 -->
        <result property="inParkChannelName" column="in_park_channel_name"/> <!-- 入场通道名称 -->
        <result property="inParkChannelUser" column="in_park_channel_user"/> <!-- 入场通道操作员 -->
        <result property="inParkChannelImageUrl" column="in_park_channel_image_url"/> <!-- 入场图片http可访问路径 -->
        <result property="inImageUrl" column="in_image_url"/> <!-- 本地入场图片 -->
        <result property="outParkTime" column="out_park_time"/> <!-- 出场时间 -->
        <result property="outParkChannelNumber" column="out_park_channel_number"/> <!-- 出场通道编号 -->
        <result property="outParkChannelName" column="out_park_channel_name"/> <!-- 出场通道名称 -->
        <result property="outParkChannelUser" column="out_park_channel_user"/> <!-- 出场通道操作员 -->
        <result property="outParkChannelImageUrl" column="out_park_channel_image_url"/> <!-- 出场图片http可访问路径 -->
        <result property="outImageUrl" column="out_image_url"/> <!-- 本地出场图片 -->
        <result property="parkHourMinutes" column="park_hour_minutes"/> <!-- 停车时长(分) -->
        <result property="parkHourStr" column="park_hour_str"/> <!-- 停车时长(xx天xx小时xx分钟) -->
        <result property="updateTime" column="update_time"/> <!-- 修改时间 -->
    </resultMap>


    <sql id="selectParkingVehicleRecordVo">
        select id,
               park_id,
               in_way,
               out_way,
               plate_number,
               plate_color,
               plate_type,
               auth_type,
               in_park_time,
               in_park_channel_number,
               in_park_channel_name,
               in_park_channel_user,
               in_park_channel_image_url,
               in_image_url,
               out_park_time,
               out_park_channel_number,
               out_park_channel_name,
               out_park_channel_user,
               out_park_channel_image_url,
               out_image_url,
               park_hour_minutes,
               park_hour_str
        from sk_parking_vehicle_record
    </sql>
    <!-- 查询对象List -->
    <select id="selectParkingVehicleRecordList" parameterType="ParkingVehicleRecord" resultMap="parkingVehicleRecordResult">
        <include refid="selectParkingVehicleRecordVo"/>
        <where>
            <include refid="equal"/>
        </where>
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectParkingVehicleRecordListByLike" parameterType="ParkingVehicleRecord"
            resultMap="parkingVehicleRecordResult">
        <include refid="selectParkingVehicleRecordVo"/>
        <where>
            <include refid="like"/>
        </where>
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectParkingVehicleRecordById" parameterType="String" resultMap="parkingVehicleRecordResult">
        <include refid="selectParkingVehicleRecordVo"/>
        where id = #{id}
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectInParkingVehicleRecordCount" resultType="java.lang.Integer">
        select count(id) from sk_parking_vehicle_record where in_park_time is not null and out_park_time is null
    </select>



    <update id="updateParkingVehicleRecord" parameterType="ParkingVehicleRecord">
        update sk_parking_vehicle_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="parkId != null  and parkId != ''  ">park_id = #{parkId},</if>
            <if test="inWay != null  ">in_way = #{inWay},</if>
            <if test="outWay != null  ">out_way = #{outWay},</if>
            <if test="plateNumber != null  and plateNumber != ''  ">plate_number = #{plateNumber},</if>
            <if test="plateColor != null  ">plate_color = #{plateColor},</if>
            <if test="plateType != null  ">plate_type = #{plateType},</if>
            <if test="authType != null  ">auth_type = #{authType},</if>
            <if test="inParkTime != null  ">in_park_time = #{inParkTime},</if>
            <if test="inParkChannelNumber != null  ">in_park_channel_number = #{inParkChannelNumber},</if>
            <if test="inParkChannelName != null  and inParkChannelName != ''  ">in_park_channel_name =
                #{inParkChannelName},
            </if>
            <if test="inParkChannelUser != null  and inParkChannelUser != ''  ">in_park_channel_user =
                #{inParkChannelUser},
            </if>
            <if test="inParkChannelImageUrl != null  and inParkChannelImageUrl != ''  ">in_park_channel_image_url =
                #{inParkChannelImageUrl},
            </if>
            <if test="inImageUrl != null  and inImageUrl != ''  ">in_image_url = #{inImageUrl},</if>
            <if test="outParkTime != null  ">out_park_time = #{outParkTime},</if>
            <if test="outParkChannelNumber != null  ">out_park_channel_number = #{outParkChannelNumber},</if>
            <if test="outParkChannelName != null  and outParkChannelName != ''  ">out_park_channel_name =
                #{outParkChannelName},
            </if>
            <if test="outParkChannelUser != null  and outParkChannelUser != ''  ">out_park_channel_user =
                #{outParkChannelUser},
            </if>
            <if test="outParkChannelImageUrl != null  and outParkChannelImageUrl != ''  ">out_park_channel_image_url =
                #{outParkChannelImageUrl},
            </if>
            <if test="outImageUrl != null  and outImageUrl != ''  ">out_image_url = #{outImageUrl},</if>
            <if test="parkHourMinutes != null  ">park_hour_minutes = #{parkHourMinutes},</if>
            <if test="parkHourStr != null  and parkHourStr != ''  ">park_hour_str = #{parkHourStr},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteParkingVehicleRecordById" parameterType="String">
        delete
        from sk_parking_vehicle_record
        where id = #{id}
    </delete>

    <delete id="deleteParkingVehicleRecordByIds" parameterType="Integer">
        delete from sk_parking_vehicle_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <!-- 新增对象 -->
    <insert id="insertParkingVehicleRecord" parameterType="ParkingVehicleRecord">
        insert into sk_parking_vehicle_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null  and id != ''  ">id,</if>
            <if test="parkId != null  and parkId != ''  ">park_id,</if>
            <if test="inWay != null  ">in_way,</if>
            <if test="outWay != null  ">out_way,</if>
            <if test="plateNumber != null  and plateNumber != ''  ">plate_number,</if>
            <if test="plateColor != null  ">plate_color,</if>
            <if test="plateType != null  ">plate_type,</if>
            <if test="authType != null  ">auth_type,</if>
            <if test="inParkTime != null  ">in_park_time,</if>
            <if test="inParkChannelNumber != null  ">in_park_channel_number,</if>
            <if test="inParkChannelName != null  and inParkChannelName != ''  ">in_park_channel_name,</if>
            <if test="inParkChannelUser != null  and inParkChannelUser != ''  ">in_park_channel_user,</if>
            <if test="inParkChannelImageUrl != null  and inParkChannelImageUrl != ''  ">in_park_channel_image_url,</if>
            <if test="inImageUrl != null  and inImageUrl != ''  ">in_image_url,</if>
            <if test="outParkTime != null  ">out_park_time,</if>
            <if test="outParkChannelNumber != null  ">out_park_channel_number,</if>
            <if test="outParkChannelName != null  and outParkChannelName != ''  ">out_park_channel_name,</if>
            <if test="outParkChannelUser != null  and outParkChannelUser != ''  ">out_park_channel_user,</if>
            <if test="outParkChannelImageUrl != null  and outParkChannelImageUrl != ''  ">out_park_channel_image_url,
            </if>
            <if test="outImageUrl != null  and outImageUrl != ''  ">out_image_url,</if>
            <if test="parkHourMinutes != null  ">park_hour_minutes,</if>
            <if test="parkHourStr != null  and parkHourStr != ''  ">park_hour_str,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null  and id != ''  ">#{id},</if>
            <if test="parkId != null  and parkId != ''  ">#{parkId},</if>
            <if test="inWay != null  ">#{inWay},</if>
            <if test="outWay != null  ">#{outWay},</if>
            <if test="plateNumber != null  and plateNumber != ''  ">#{plateNumber},</if>
            <if test="plateColor != null  ">#{plateColor},</if>
            <if test="plateType != null  ">#{plateType},</if>
            <if test="authType != null  ">#{authType},</if>
            <if test="inParkTime != null  ">#{inParkTime},</if>
            <if test="inParkChannelNumber != null  ">#{inParkChannelNumber},</if>
            <if test="inParkChannelName != null  and inParkChannelName != ''  ">#{inParkChannelName},</if>
            <if test="inParkChannelUser != null  and inParkChannelUser != ''  ">#{inParkChannelUser},</if>
            <if test="inParkChannelImageUrl != null  and inParkChannelImageUrl != ''  ">#{inParkChannelImageUrl},</if>
            <if test="inImageUrl != null  and inImageUrl != ''  ">#{inImageUrl},</if>
            <if test="outParkTime != null  ">#{outParkTime},</if>
            <if test="outParkChannelNumber != null  ">#{outParkChannelNumber},</if>
            <if test="outParkChannelName != null  and outParkChannelName != ''  ">#{outParkChannelName},</if>
            <if test="outParkChannelUser != null  and outParkChannelUser != ''  ">#{outParkChannelUser},</if>
            <if test="outParkChannelImageUrl != null  and outParkChannelImageUrl != ''  ">#{outParkChannelImageUrl},
            </if>
            <if test="outImageUrl != null  and outImageUrl != ''  ">#{outImageUrl},</if>
            <if test="parkHourMinutes != null  ">#{parkHourMinutes},</if>
            <if test="parkHourStr != null  and parkHourStr != ''  ">#{parkHourStr},</if>
        </trim>
    </insert>


    <!-- 表字段 -->
    <sql id="column">
        id,  <!-- id -->
        park_id,  <!-- 车场编号 -->
        in_way,  <!-- 入场方式 -->
        out_way,  <!-- 出场方式 -->
        plate_number,  <!-- 车牌号码 -->
        plate_color,  <!-- 车牌颜色 -->
        plate_type,  <!-- 车辆类型 -->
        auth_type,  <!-- 车辆卡类型 -->
        in_park_time,  <!-- 入场时间 -->
        in_park_channel_number,  <!-- 入场通道编号 -->
        in_park_channel_name,  <!-- 入场通道名称 -->
        in_park_channel_user,  <!-- 入场通道操作员 -->
        in_park_channel_image_url,  <!-- 入场图片http可访问路径 -->
        in_image_url,  <!-- 本地入场图片 -->
        out_park_time,  <!-- 出场时间 -->
        out_park_channel_number,  <!-- 出场通道编号 -->
        out_park_channel_name,  <!-- 出场通道名称 -->
        out_park_channel_user,  <!-- 出场通道操作员 -->
        out_park_channel_image_url,  <!-- 出场图片http可访问路径 -->
        out_image_url,  <!-- 本地出场图片 -->
        park_hour_minutes,  <!-- 停车时长(分) -->
        park_hour_str  <!-- 停车时长(xx天xx小时xx分钟) -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="id != null and id != ''">
            and id = #{id}  <!-- id -->
        </if>
        <if test="parkId != null and parkId != ''">
            and park_id = #{parkId}  <!-- 车场编号 -->
        </if>
        <if test="inWay != null ">
            and in_way = #{inWay}  <!-- 入场方式 -->
        </if>
        <if test="outWay != null ">
            and out_way = #{outWay}  <!-- 出场方式 -->
        </if>
        <if test="plateNumber != null and plateNumber != ''">
            and plate_number = #{plateNumber}  <!-- 车牌号码 -->
        </if>
        <if test="plateColor != null ">
            and plate_color = #{plateColor}  <!-- 车牌颜色 -->
        </if>
        <if test="plateType != null ">
            and plate_type = #{plateType}  <!-- 车辆类型 -->
        </if>
        <if test="authType != null ">
            and auth_type = #{authType}  <!-- 车辆卡类型 -->
        </if>
        <if test="inParkTime != null ">
            and in_park_time = #{inParkTime}  <!-- 入场时间 -->
        </if>
        <if test="inParkChannelNumber != null ">
            and in_park_channel_number = #{inParkChannelNumber}  <!-- 入场通道编号 -->
        </if>
        <if test="inParkChannelName != null and inParkChannelName != ''">
            and in_park_channel_name = #{inParkChannelName}  <!-- 入场通道名称 -->
        </if>
        <if test="inParkChannelUser != null and inParkChannelUser != ''">
            and in_park_channel_user = #{inParkChannelUser}  <!-- 入场通道操作员 -->
        </if>
        <if test="inParkChannelImageUrl != null and inParkChannelImageUrl != ''">
            and in_park_channel_image_url = #{inParkChannelImageUrl}  <!-- 入场图片http可访问路径 -->
        </if>
        <if test="inImageUrl != null and inImageUrl != ''">
            and in_image_url = #{inImageUrl}  <!-- 本地入场图片 -->
        </if>
        <if test="outParkTime != null ">
            and out_park_time = #{outParkTime}  <!-- 出场时间 -->
        </if>
        <if test="outParkChannelNumber != null ">
            and out_park_channel_number = #{outParkChannelNumber}  <!-- 出场通道编号 -->
        </if>
        <if test="outParkChannelName != null and outParkChannelName != ''">
            and out_park_channel_name = #{outParkChannelName}  <!-- 出场通道名称 -->
        </if>
        <if test="outParkChannelUser != null and outParkChannelUser != ''">
            and out_park_channel_user = #{outParkChannelUser}  <!-- 出场通道操作员 -->
        </if>
        <if test="outParkChannelImageUrl != null and outParkChannelImageUrl != ''">
            and out_park_channel_image_url = #{outParkChannelImageUrl}  <!-- 出场图片http可访问路径 -->
        </if>
        <if test="outImageUrl != null and outImageUrl != ''">
            and out_image_url = #{outImageUrl}  <!-- 本地出场图片 -->
        </if>
        <if test="parkHourMinutes != null ">
            and park_hour_minutes = #{parkHourMinutes}  <!-- 停车时长(分) -->
        </if>
        <if test="parkHourStr != null and parkHourStr != ''">
            and park_hour_str = #{parkHourStr}  <!-- 停车时长(xx天xx小时xx分钟) -->
        </if>
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <if test="id != null and id != ''">
            and id like concat('%', #{id}, '%')  <!-- id -->
        </if>
        <if test="parkId != null and parkId != ''">
            and park_id like concat('%', #{parkId}, '%')  <!-- 车场编号 -->
        </if>
        <if test="plateNumber != null and plateNumber != ''">
            and plate_number like concat('%', #{plateNumber}, '%')  <!-- 车牌号码 -->
        </if>
        <if test="inoutType != null and inoutType == 'into'">
            and in_park_time is not null and out_park_time is null  <!-- 查询在场车辆 -->
        </if>
        <if test="inoutType != null and inoutType == 'out'">
            and out_park_time is not null  <!-- 查询离场车辆 -->
        </if>
        <if test="inParkChannelName != null and inParkChannelName != ''">
            and in_park_channel_name like concat('%', #{inParkChannelName}, '%')  <!-- 入场通道名称 -->
        </if>
        <if test="inParkChannelUser != null and inParkChannelUser != ''">
            and in_park_channel_user like concat('%', #{inParkChannelUser}, '%')  <!-- 入场通道操作员 -->
        </if>
        <if test="inParkChannelImageUrl != null and inParkChannelImageUrl != ''">
            and in_park_channel_image_url like concat('%', #{inParkChannelImageUrl}, '%')  <!-- 入场图片http可访问路径 -->
        </if>
        <if test="inImageUrl != null and inImageUrl != ''">
            and in_image_url like concat('%', #{inImageUrl}, '%')  <!-- 本地入场图片 -->
        </if>
        <if test="outParkChannelName != null and outParkChannelName != ''">
            and out_park_channel_name like concat('%', #{outParkChannelName}, '%')  <!-- 出场通道名称 -->
        </if>
        <if test="outParkChannelUser != null and outParkChannelUser != ''">
            and out_park_channel_user like concat('%', #{outParkChannelUser}, '%')  <!-- 出场通道操作员 -->
        </if>
        <if test="outParkChannelImageUrl != null and outParkChannelImageUrl != ''">
            and out_park_channel_image_url like concat('%', #{outParkChannelImageUrl}, '%')  <!-- 出场图片http可访问路径 -->
        </if>
        <if test="outImageUrl != null and outImageUrl != ''">
            and out_image_url like concat('%', #{outImageUrl}, '%')  <!-- 本地出场图片 -->
        </if>
        <if test="parkHourStr != null and parkHourStr != ''">
            and park_hour_str like concat('%', #{parkHourStr}, '%')  <!-- 停车时长(xx天xx小时xx分钟) -->
        </if>
    </sql>
</mapper>

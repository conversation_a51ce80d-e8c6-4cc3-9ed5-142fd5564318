<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.smarkpark.mapper.ParkingBarrierGateMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.smarkpark.api.entity.ParkingBarrierGate" id="parkingBarrierGateResult">
        <result property="channelId" column="channel_id"/> <!-- 通道id -->
        <result property="channelName" column="channel_name"/> <!-- 通道名称 -->
        <result property="channelType" column="channel_type"/> <!-- 进出方向 1进 2出 -->
        <result property="cameraIp" column="camera_ip"/> <!-- 相机ip -->
        <result property="deviceSn" column="device_sn"/> <!-- 设备序列号 -->
        <result property="deviceStatus" column="device_status"/> <!-- 道闸状态 0关到位 1开到位 2停止中 仅限网络道闸才有状态 -->
        <result property="onlineStatus" column="online_status"/> <!-- 在线状态 0离线 1在线 -->
    </resultMap>

    <sql id="selectParkingBarrierGateVo">
        select channel_id, channel_name, channel_type, camera_ip, device_sn, device_status, online_status
        from sk_parking_barrier_gate
    </sql>
    <!-- 查询对象List -->
    <select id="selectParkingBarrierGateList" parameterType="ParkingBarrierGate" resultMap="parkingBarrierGateResult">
        <include refid="selectParkingBarrierGateVo"/>
        <where>
            <include refid="equal"/>
        </where>
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectParkingBarrierGateListByLike" parameterType="ParkingBarrierGate"
            resultMap="parkingBarrierGateResult">
        <include refid="selectParkingBarrierGateVo"/>
        <where>
            <include refid="like"/>
        </where>
    </select>

    <select id="statisticsBarrierGateList" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT channel_id deviceCode,channel_name deviceName,online_status deviceState,camera_ip deviceIp
        FROM sk_parking_barrier_gate
        <where>
            <if test="deviceState != null">
                and online_status = #{deviceState}
            </if>
        </where>
    </select>

    <select id="statisticsDeviceNum" resultType="java.util.Map">
        SELECT SUM(CASE WHEN online_status = 0 THEN 1 ELSE 0 END) AS offlineNum,
               SUM(CASE WHEN online_status = 1 THEN 1 ELSE 0 END) AS onlineNum
        FROM sk_parking_barrier_gate
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectParkingBarrierGateById" parameterType="Integer" resultMap="parkingBarrierGateResult">
        <include refid="selectParkingBarrierGateVo"/>
        where channel_id = #{channelId}
    </select>


    <update id="updateParkingBarrierGate" parameterType="ParkingBarrierGate">
        update sk_parking_barrier_gate
        <trim prefix="SET" suffixOverrides=",">
            <if test="channelName != null  and channelName != ''  ">channel_name = #{channelName},</if>
            <if test="channelType != null  ">channel_type = #{channelType},</if>
            <if test="cameraIp != null  and cameraIp != ''  ">camera_ip = #{cameraIp},</if>
            <if test="deviceSn != null  and deviceSn != ''  ">device_sn = #{deviceSn},</if>
            <if test="deviceStatus != null  ">device_status = #{deviceStatus},</if>
            <if test="onlineStatus != null  ">online_status = #{onlineStatus},</if>
        </trim>
        where channel_id = #{channelId}
    </update>

    <delete id="deleteParkingBarrierGateById" parameterType="Integer">
        delete
        from sk_parking_barrier_gate
        where channel_id = #{channelId}
    </delete>

    <delete id="deleteParkingBarrierGateByIds" parameterType="Integer">
        delete from sk_parking_barrier_gate where channel_id in
        <foreach item="channelId" collection="array" open="(" separator="," close=")">
            #{channelId}
        </foreach>
    </delete>

    <update id="truncateParkingBarrierGate">
        TRUNCATE TABLE sk_parking_barrier_gate;
    </update>

    <!-- 新增对象 -->
    <insert id="insertParkingBarrierGate" parameterType="ParkingBarrierGate">
        insert into sk_parking_barrier_gate
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="channelId != null  ">channel_id,</if>
            <if test="channelName != null  and channelName != ''  ">channel_name,</if>
            <if test="channelType != null  ">channel_type,</if>
            <if test="cameraIp != null  and cameraIp != ''  ">camera_ip,</if>
            <if test="deviceSn != null  and deviceSn != ''  ">device_sn,</if>
            <if test="deviceStatus != null  ">device_status,</if>
            <if test="onlineStatus != null  ">online_status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="channelId != null  ">#{channelId},</if>
            <if test="channelName != null  and channelName != ''  ">#{channelName},</if>
            <if test="channelType != null  ">#{channelType},</if>
            <if test="cameraIp != null  and cameraIp != ''  ">#{cameraIp},</if>
            <if test="deviceSn != null  and deviceSn != ''  ">#{deviceSn},</if>
            <if test="deviceStatus != null  ">#{deviceStatus},</if>
            <if test="onlineStatus != null  ">#{onlineStatus},</if>
        </trim>
    </insert>


    <!-- 表字段 -->
    <sql id="column">
        channel_id,  <!-- 通道id -->
        channel_name,  <!-- 通道名称 -->
        channel_type,  <!-- 进出方向 1进 2出 -->
        camera_ip,  <!-- 相机ip -->
        device_sn,  <!-- 设备序列号 -->
        device_status,  <!-- 道闸状态 0关到位 1开到位 2停止中 仅限网络道闸才有状态 -->
        online_status  <!-- 在线状态 0离线 1在线 -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="channelId != null ">
            and channel_id = #{channelId}  <!-- 通道id -->
        </if>
        <if test="channelName != null and channelName != ''">
            and channel_name = #{channelName}  <!-- 通道名称 -->
        </if>
        <if test="channelType != null ">
            and channel_type = #{channelType}  <!-- 进出方向 1进 2出 -->
        </if>
        <if test="cameraIp != null and cameraIp != ''">
            and camera_ip = #{cameraIp}  <!-- 相机ip -->
        </if>
        <if test="deviceSn != null and deviceSn != ''">
            and device_sn = #{deviceSn}  <!-- 设备序列号 -->
        </if>
        <if test="deviceStatus != null ">
            and device_status = #{deviceStatus}  <!-- 道闸状态 0关到位 1开到位 2停止中 仅限网络道闸才有状态 -->
        </if>
        <if test="onlineStatus != null ">
            and online_status = #{onlineStatus}  <!-- 在线状态 0离线 1在线 -->
        </if>
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <if test="channelName != null and channelName != ''">
            and channel_name like concat('%', #{channelName}, '%')  <!-- 通道名称 -->
        </if>
        <if test="cameraIp != null and cameraIp != ''">
            and camera_ip like concat('%', #{cameraIp}, '%')  <!-- 相机ip -->
        </if>
        <if test="deviceSn != null and deviceSn != ''">
            and device_sn like concat('%', #{deviceSn}, '%')  <!-- 设备序列号 -->
        </if>
    </sql>
</mapper>

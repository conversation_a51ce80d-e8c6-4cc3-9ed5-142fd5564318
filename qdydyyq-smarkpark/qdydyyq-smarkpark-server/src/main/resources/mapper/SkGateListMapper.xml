<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.smarkpark.mapper.SkGateListMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.smarkpark.api.entity.SkGateList" id="skGateListResult">
        <result property="id" column="id"/> <!-- 序列 -->
        <result property="srcIndex" column="src_index"/> <!-- 出入口标识，最大长度64 -->
        <result property="name" column="name"/> <!-- 名称   最大长度32 -->
        <result property="type" column="type"/> <!-- 类别  1 人，2车 -->
        <result property="inout" column="inout"/> <!-- 进出  0进，1出，2其他，如测速点 -->
        <result property="onlineStatus" column="online_status"/> <!-- 在线状态   0正常，1离线【每隔5分钟推送一次】 -->
    </resultMap>


    <sql id="selectSkGateListVo">
        select id, src_index, `name`, `type`, `inout`, online_status
        from sk_gate_list
    </sql>
    <!-- 查询对象List -->
    <select id="selectSkGateListList" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkGateList" resultMap="skGateListResult">
        <include refid="selectSkGateListVo"/>
        <where>
            <include refid="equal"/>
        </where>
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectSkGateListListByLike" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkGateList" resultMap="skGateListResult">
        <include refid="selectSkGateListVo"/>
        <where>
            <include refid="like"/>
        </where>
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectSkGateListById" parameterType="Integer" resultMap="skGateListResult">
        <include refid="selectSkGateListVo"/>
        where id = #{id}
    </select>


    <update id="updateSkGateList" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkGateList">
        update sk_gate_list
        <trim prefix="SET" suffixOverrides=",">
            <if test="srcIndex != null  and srcIndex != ''  ">src_index = #{srcIndex},</if>
            <if test="name != null  and name != ''  ">`name` = #{name},</if>
            <if test="type != null  ">`type` = #{type},</if>
            <if test="inout != null  ">`inout` = #{inout},</if>
            <if test="onlineStatus != null  ">online_status = #{onlineStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSkGateListById" parameterType="Integer">
        delete
        from sk_gate_list
        where id = #{id}
    </delete>

    <delete id="deleteSkGateListByIds" parameterType="Integer">
        delete from sk_gate_list where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <!-- 新增对象 -->
    <insert id="insertSkGateList" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkGateList" useGeneratedKeys="true" keyProperty="id">
        insert into sk_gate_list
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="srcIndex != null  and srcIndex != ''  ">src_index,</if>
            <if test="name != null  and name != ''  ">`name`,</if>
            <if test="type != null  ">`type`,</if>
            <if test="inout != null  ">`inout`,</if>
            <if test="onlineStatus != null  ">online_status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="srcIndex != null  and srcIndex != ''  ">#{srcIndex},</if>
            <if test="name != null  and name != ''  ">#{name},</if>
            <if test="type != null  ">#{type},</if>
            <if test="inout != null  ">#{inout},</if>
            <if test="onlineStatus != null  ">#{onlineStatus},</if>
        </trim>
    </insert>


    <!-- 表字段 -->
    <sql id="column">
        id,  <!-- 序列 -->
        src_index,  <!-- 出入口标识，最大长度64 -->
        `name`,  <!-- 名称   最大长度32 -->
        `type`,  <!-- 类别  1 人，2车 -->
        `inout`,  <!-- 进出  0进，1出，2其他，如测速点 -->
        online_status  <!-- 在线状态   0正常，1离线【每隔5分钟推送一次】 -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="id != null ">
            and id = #{id}  <!-- 序列 -->
        </if>
        <if test="srcIndex != null and srcIndex != ''">
            and src_index = #{srcIndex}  <!-- 出入口标识，最大长度64 -->
        </if>
        <if test="name != null and name != ''">
            and `name` = #{name}  <!-- 名称   最大长度32 -->
        </if>
        <if test="type != null ">
            and `type` = #{type}  <!-- 类别  1 人，2车 -->
        </if>
        <if test="inout != null ">
            and `inout` = #{inout}  <!-- 进出  0进，1出，2其他，如测速点 -->
        </if>
        <if test="onlineStatus != null ">
            and online_status = #{onlineStatus}  <!-- 在线状态   0正常，1离线【每隔5分钟推送一次】 -->
        </if>
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <if test="srcIndex != null and srcIndex != ''">
            and src_index like concat('%', #{srcIndex}, '%')  <!-- 出入口标识，最大长度64 -->
        </if>
        <if test="name != null and name != ''">
            and `name` like concat('%', #{name}, '%')  <!-- 名称   最大长度32 -->
        </if>
    </sql>

    <!-- 截断闸机列表：包含人车道闸、测速点 -->
    <update id="truncateSkGateList">
        TRUNCATE TABLE sk_gate_list;
    </update>

    <!-- 插入车辆告警记录 -->
    <insert id="syncSkGateList">
        INSERT INTO sk_gate_list (
        src_index, `name`, `type`, `inout`, online_status
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.srcIndex}, #{item.name}, #{item.type}, #{item.inout}, #{item.onlineStatus}
            )
        </foreach>
    </insert>
</mapper>

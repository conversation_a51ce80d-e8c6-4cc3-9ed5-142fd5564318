<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.smarkpark.mapper.EspDoorDeviceMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.smarkpark.api.entity.EspDoorDevice" id="espDoorDeviceResult">
        <result property="id" column="id"/> <!-- 设备id -->
        <result property="type" column="type"/> <!-- 设备类型(1:门禁；2:车辆道闸) -->
        <result property="brand" column="brand"/> <!-- 设备型号编码 -->
        <result property="brandName" column="brand_name"/> <!-- 设备型号 -->
        <result property="buildingId" column="building_id"/> <!-- 所属楼宇ID -->
        <result property="buildingName" column="building_name"/> <!-- 所属楼宇名称 -->
        <result property="controlStatus" column="control_status"/> <!-- 控制状态 -->
        <result property="createdTime" column="created_time"/> <!-- 创建时间 -->
        <result property="groupDeviceId" column="group_device_id"/> <!-- 设备分组ID -->
        <result property="ip" column="ip"/> <!-- 设备IP -->
        <result property="name" column="name"/> <!-- 设备名称 -->
        <result property="sn" column="sn"/> <!-- 设备SN码 -->
        <result property="status" column="status"/> <!-- 设备状态：0-离线；1-在线 -->
        <result property="subType" column="sub_type"/> <!-- 设备类型：11-门禁控制器；12-人脸一体机 -->
        <result property="subTypeName" column="sub_type_name"/> <!-- 设备类型名称 -->
    </resultMap>


    <sql id="selectEspDoorDeviceVo">
        select id,type,
               brand,
               brand_name,
               building_id,
               building_name,
               control_status,
               created_time,
               group_device_id,
               ip,
               `name`,
               sn,
               status,
               sub_type,
               sub_type_name
        from esp_door_device
    </sql>
    <!-- 查询对象List -->
    <select id="selectEspDoorDeviceList" parameterType="EspDoorDevice" resultMap="espDoorDeviceResult">
        <include refid="selectEspDoorDeviceVo"/>
        <where>
            <include refid="equal"/>
        </where>
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectEspDoorDeviceListByLike" parameterType="EspDoorDevice" resultMap="espDoorDeviceResult">
        <include refid="selectEspDoorDeviceVo"/>
        <where>
            <include refid="like"/>

            <!-- 设备状态：0-离线；1-在线 -->
            <if test="status != null">and status = #{status}</if>
        </where>
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectEspDoorDeviceById" parameterType="String" resultMap="espDoorDeviceResult">
        <include refid="selectEspDoorDeviceVo"/>
        where id = #{id}
    </select>


    <update id="updateEspDoorDevice" parameterType="EspDoorDevice">
        update esp_door_device
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null ">type = #{type},</if>
            <if test="brand != null  and brand != ''  ">brand = #{brand},</if>
            <if test="brandName != null  and brandName != ''  ">brand_name = #{brandName},</if>
            <if test="buildingId != null  and buildingId != ''  ">building_id = #{buildingId},</if>
            <if test="buildingName != null  and buildingName != ''  ">building_name = #{buildingName},</if>
            <if test="controlStatus != null  and controlStatus != ''  ">control_status = #{controlStatus},</if>
            <if test="createdTime != null ">created_time = #{createdTime},</if>
            <if test="groupDeviceId != null  and groupDeviceId != ''  ">group_device_id = #{groupDeviceId},</if>
            <if test="ip != null  and ip != ''  ">ip = #{ip},</if>
            <if test="name != null  and name != ''  ">name = #{name},</if>
            <if test="sn != null  and sn != ''  ">sn = #{sn},</if>
            <if test="status != null ">status = #{status},</if>
            <if test="subType != null ">sub_type = #{subType},</if>
            <if test="subTypeName != null  and subTypeName != ''  ">sub_type_name = #{subTypeName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEspDoorDeviceById" parameterType="String">
        delete
        from esp_door_device
        where id = #{id}
    </delete>

    <delete id="deleteEspDoorDeviceByIds" parameterType="Integer">
        delete from esp_door_device where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <!-- 新增对象 -->
    <insert id="insertEspDoorDevice" parameterType="EspDoorDevice">
        insert into esp_door_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null  and id != ''  ">id,</if>
            <if test="type != null ">type,</if>
            <if test="brand != null  and brand != ''  ">brand,</if>
            <if test="brandName != null  and brandName != ''  ">brand_name,</if>
            <if test="buildingId != null  and buildingId != ''  ">building_id,</if>
            <if test="buildingName != null  and buildingName != ''  ">building_name,</if>
            <if test="controlStatus != null  and controlStatus != ''  ">control_status,</if>
            <if test="createdTime != null ">created_time,</if>
            <if test="groupDeviceId != null  and groupDeviceId != ''  ">group_device_id,</if>
            <if test="ip != null  and ip != ''  ">ip,</if>
            <if test="name != null  and name != ''  ">name,</if>
            <if test="sn != null  and sn != ''  ">sn,</if>
            <if test="status != null ">status,</if>
            <if test="subType != null ">sub_type,</if>
            <if test="subTypeName != null  and subTypeName != ''  ">sub_type_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null  and id != ''  ">#{id},</if>
            <if test="type != null ">#{type},</if>
            <if test="brand != null  and brand != ''  ">#{brand},</if>
            <if test="brandName != null  and brandName != ''  ">#{brandName},</if>
            <if test="buildingId != null  and buildingId != ''  ">#{buildingId},</if>
            <if test="buildingName != null  and buildingName != ''  ">#{buildingName},</if>
            <if test="controlStatus != null  and controlStatus != ''  ">#{controlStatus},</if>
            <if test="createdTime != null ">#{createdTime},</if>
            <if test="groupDeviceId != null  and groupDeviceId != ''  ">#{groupDeviceId},</if>
            <if test="ip != null  and ip != ''  ">#{ip},</if>
            <if test="name != null  and name != ''  ">#{name},</if>
            <if test="sn != null  and sn != ''  ">#{sn},</if>
            <if test="status != null ">#{status},</if>
            <if test="subType != null ">#{subType},</if>
            <if test="subTypeName != null  and subTypeName != ''  ">#{subTypeName},</if>
        </trim>
    </insert>


    <!-- 表字段 -->
    <sql id="column">
        id,  <!-- 设备id -->
        brand,  <!-- 设备型号编码 -->
        brand_name,  <!-- 设备型号 -->
        building_id,  <!-- 所属楼宇ID -->
        building_name,  <!-- 所属楼宇名称 -->
        control_status,  <!-- 控制状态 -->
        created_time,  <!-- 创建时间 -->
        group_device_id,  <!-- 设备分组ID -->
        ip,  <!-- 设备IP -->
        `name`,  <!-- 设备名称 -->
        sn,  <!-- 设备SN码 -->
        status,  <!-- 设备状态：0-离线；1-在线 -->
        sub_type,  <!-- 设备类型：11-门禁控制器；12-人脸一体机 -->
        sub_type_name,  <!-- 设备类型名称 -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="id != null and id != ''">
            and id = #{id}  <!-- 设备id -->
        </if>
        <if test="type != null "> and type = #{type} </if>
        <!-- 设备型号编码 -->
        <if test="brand != null  and brand != ''  ">and brand = #{brand}</if>
        <!-- 设备型号 -->
        <if test="brandName != null  and brandName != ''  ">and brand_name = #{brandName}</if>
        <!-- 所属楼宇ID -->
        <if test="buildingId != null  and buildingId != ''  ">and building_id = #{buildingId}</if>
        <!-- 所属楼宇名称 -->
        <if test="buildingName != null  and buildingName != ''  ">and building_name = #{buildingName}</if>
        <!-- 控制状态 -->
        <if test="controlStatus != null  and controlStatus != ''  ">and control_status = #{controlStatus}</if>
        <!-- 创建时间 -->
        <if test="createdTime != null">and created_time = #{createdTime}</if>
        <!-- 设备分组ID -->
        <if test="groupDeviceId != null  and groupDeviceId != ''  ">and group_device_id = #{groupDeviceId}</if>
        <!-- 设备IP -->
        <if test="ip != null  and ip != ''  ">and ip = #{ip}</if>
        <!-- 设备名称 -->
        <if test="name != null  and name != ''  ">and name = #{name}</if>
        <!-- 设备SN码 -->
        <if test="sn != null  and sn != ''  ">and sn = #{sn}</if>
        <!-- 设备状态：0-离线；1-在线 -->
        <if test="status != null">and status = #{status}</if>
        <!-- 设备类型：11-门禁控制器；12-人脸一体机 -->
        <if test="subType != null">and sub_type = #{subType}</if>
        <!-- 设备类型名称 -->
        <if test="subTypeName != null  and subTypeName != ''  ">and sub_type_name = #{subTypeName}</if>

    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <if test="id != null and id != ''">
            and id like concat('%', #{id}, '%')  <!-- 设备id -->
        </if>
        <if test="type != null "> and type = #{type} </if>
        <!-- 设备型号编码 -->
        <if test="brand != null  and brand != ''  ">and brand like concat('%', #{brand}, '%')</if>
        <!-- 设备型号 -->
        <if test="brandName != null  and brandName != ''  ">and brand_name like concat('%', #{brandName}, '%')</if>
        <!-- 所属楼宇ID -->
        <if test="buildingId != null  and buildingId != ''  ">and building_id like concat('%', #{buildingId}, '%')</if>
        <!-- 所属楼宇名称 -->
        <if test="buildingName != null  and buildingName != ''  ">and building_name like concat('%', #{buildingName}, '%')</if>
        <!-- 控制状态 -->
        <if test="controlStatus != null  and controlStatus != ''  ">and control_status like concat('%', #{controlStatus}, '%')</if>
        <!-- 创建时间 -->
        <if test="createdTime != null ">and created_time like concat('%', #{createdTime}, '%')</if>
        <!-- 设备分组ID -->
        <if test="groupDeviceId != null  and groupDeviceId != ''  ">and group_device_id like concat('%', #{groupDeviceId}, '%')</if>
        <!-- 设备IP -->
        <if test="ip != null  and ip != ''  ">and ip like concat('%', #{ip}, '%')</if>
        <!-- 设备名称 -->
        <if test="name != null  and name != ''  ">and name like concat('%', #{name}, '%')</if>
        <!-- 设备SN码 -->
        <if test="sn != null  and sn != ''  ">and sn like concat('%', #{sn}, '%')</if>
        <!-- 设备状态：0-离线；1-在线 -->
        <if test="status != null  ">and status like concat('%', #{status}, '%')</if>
        <!-- 设备类型：11-门禁控制器；12-人脸一体机 -->
        <if test="subType != null  ">and sub_type like concat('%', #{subType}, '%')</if>
        <!-- 设备类型名称 -->
        <if test="subTypeName != null  and subTypeName != ''  ">and sub_type_name like concat('%', #{subTypeName}, '%')</if>
    </sql>

    <select id="statisticsDeviceNum" resultType="java.util.Map">
        SELECT SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) AS offlineNum,
        SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) AS onlineNum
        FROM esp_door_device where type = 1
    </select>

    <select id="statisticsDeviceList" resultType="java.util.Map">
        SELECT id deviceCode,name deviceName,status deviceState,ip deviceIp
        FROM esp_door_device
        <where>
            type = 1
            <if test="deviceState != null">
                and status = #{deviceState}
            </if>
        </where>
    </select>
</mapper>

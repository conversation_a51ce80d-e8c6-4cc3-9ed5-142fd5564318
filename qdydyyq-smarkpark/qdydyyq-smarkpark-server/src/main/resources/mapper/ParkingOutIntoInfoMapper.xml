<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.smarkpark.mapper.ParkingOutIntoInfoMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.smarkpark.api.entity.ParkingOutIntoInfo" id="parkingOutIntoInfoResult">
        <result property="id" column="id"/> <!-- id -->
        <result property="parkInOutOrder" column="park_in_out_order"/> <!-- 进出唯一编号,必须和入场记录的相同 为空视为无入场记录 -->
        <result property="parkId" column="park_id"/> <!-- 车场编号 -->
        <result property="wayType" column="way_type"/> <!-- 出入场方式 -->
        <result property="plateNumber" column="plate_number"/> <!-- 车牌号码 -->
        <result property="plateColor" column="plate_color"/> <!-- 车牌颜色 -->
        <result property="plateType" column="plate_type"/> <!-- 车辆类型 -->
        <result property="authType" column="auth_type"/> <!-- 车辆卡类型 -->
        <result property="outInTime" column="out_in_time"/> <!-- 出入场时间 -->
        <result property="parkChannelNumber" column="park_channel_number"/> <!-- 出入场通道编号 -->
        <result property="parkChannelName" column="park_channel_name"/> <!-- 出入场通道名称 -->
        <result property="parkChannelUser" column="park_channel_user"/> <!-- 出入场通道操作员 -->
        <result property="parkChannelImageUrl" column="park_channel_image_url"/> <!-- 出入场图片http可访问路径 -->
        <result property="imageUrl" column="image_url"/> <!-- 本地入场图片 -->
    </resultMap>


    <sql id="selectParkingOutIntoInfoVo">
        select id,
               park_in_out_order,
               park_id,
               way_type,
               plate_number,
               plate_color,
               plate_type,
               auth_type,
               out_in_time,
               park_channel_number,
               park_channel_name,
               park_channel_user,
               IFNULL(image_url,park_channel_image_url) as park_channel_image_url,
               image_url
        from sk_parking_out_into_info
    </sql>
    <!-- 查询对象List -->
    <select id="selectParkingOutIntoInfoList" parameterType="ParkingOutIntoInfo" resultMap="parkingOutIntoInfoResult">
        <include refid="selectParkingOutIntoInfoVo"/>
        <where>
            <include refid="equal"/>
        </where>
        order by out_in_time desc
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectParkingOutIntoInfoListByLike" parameterType="ParkingOutIntoInfo"
            resultMap="parkingOutIntoInfoResult">
        <include refid="selectParkingOutIntoInfoVo"/>
        <where>
            <include refid="like"/>
        </where>
        order by out_in_time desc
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectParkingOutIntoInfoById" parameterType="Integer" resultMap="parkingOutIntoInfoResult">
        <include refid="selectParkingOutIntoInfoVo"/>
        where id = #{id}
    </select>


    <update id="updateParkingOutIntoInfo" parameterType="ParkingOutIntoInfo">
        update sk_parking_out_into_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="parkInOutOrder != null  and parkInOutOrder != ''  ">park_in_out_order = #{parkInOutOrder},</if>
            <if test="parkId != null  and parkId != ''  ">park_id = #{parkId},</if>
            <if test="wayType != null  ">way_type = #{wayType},</if>
            <if test="plateNumber != null  and plateNumber != ''  ">plate_number = #{plateNumber},</if>
            <if test="plateColor != null  ">plate_color = #{plateColor},</if>
            <if test="plateType != null  ">plate_type = #{plateType},</if>
            <if test="authType != null  ">auth_type = #{authType},</if>
            <if test="outInTime != null  ">out_in_time = #{outInTime},</if>
            <if test="parkChannelNumber != null  ">park_channel_number = #{parkChannelNumber},</if>
            <if test="parkChannelName != null  and parkChannelName != ''  ">park_channel_name = #{parkChannelName},</if>
            <if test="parkChannelUser != null  and parkChannelUser != ''  ">park_channel_user = #{parkChannelUser},</if>
            <if test="parkChannelImageUrl != null  and parkChannelImageUrl != ''  ">park_channel_image_url =
                #{parkChannelImageUrl},
            </if>
            <if test="imageUrl != null  and imageUrl != ''  ">image_url = #{imageUrl},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteParkingOutIntoInfoById" parameterType="Integer">
        delete
        from sk_parking_out_into_info
        where id = #{id}
    </delete>

    <delete id="deleteParkingOutIntoInfoByIds" parameterType="Integer">
        delete from sk_parking_out_into_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <!-- 新增对象 -->
    <insert id="insertParkingOutIntoInfo" parameterType="ParkingOutIntoInfo">
        insert into sk_parking_out_into_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null  ">id,</if>
            <if test="parkInOutOrder != null  and parkInOutOrder != ''  ">park_in_out_order,</if>
            <if test="parkId != null  and parkId != ''  ">park_id,</if>
            <if test="wayType != null  ">way_type,</if>
            <if test="plateNumber != null  and plateNumber != ''  ">plate_number,</if>
            <if test="plateColor != null  ">plate_color,</if>
            <if test="plateType != null  ">plate_type,</if>
            <if test="authType != null  ">auth_type,</if>
            <if test="outInTime != null  ">out_in_time,</if>
            <if test="parkChannelNumber != null  ">park_channel_number,</if>
            <if test="parkChannelName != null  and parkChannelName != ''  ">park_channel_name,</if>
            <if test="parkChannelUser != null  and parkChannelUser != ''  ">park_channel_user,</if>
            <if test="parkChannelImageUrl != null  and parkChannelImageUrl != ''  ">park_channel_image_url,</if>
            <if test="imageUrl != null  and imageUrl != ''  ">image_url,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null  ">#{id},</if>
            <if test="parkInOutOrder != null  and parkInOutOrder != ''  ">#{parkInOutOrder},</if>
            <if test="parkId != null  and parkId != ''  ">#{parkId},</if>
            <if test="wayType != null  ">#{wayType},</if>
            <if test="plateNumber != null  and plateNumber != ''  ">#{plateNumber},</if>
            <if test="plateColor != null  ">#{plateColor},</if>
            <if test="plateType != null  ">#{plateType},</if>
            <if test="authType != null  ">#{authType},</if>
            <if test="outInTime != null  ">#{outInTime},</if>
            <if test="parkChannelNumber != null  ">#{parkChannelNumber},</if>
            <if test="parkChannelName != null  and parkChannelName != ''  ">#{parkChannelName},</if>
            <if test="parkChannelUser != null  and parkChannelUser != ''  ">#{parkChannelUser},</if>
            <if test="parkChannelImageUrl != null  and parkChannelImageUrl != ''  ">#{parkChannelImageUrl},</if>
            <if test="imageUrl != null  and imageUrl != ''  ">#{imageUrl},</if>
        </trim>
    </insert>


    <!-- 表字段 -->
    <sql id="column">
        id,  <!-- id -->
        park_in_out_order,  <!-- 进出唯一编号,必须和入场记录的相同 为空视为无入场记录 -->
        park_id,  <!-- 车场编号 -->
        way_type,  <!-- 出入场方式 -->
        plate_number,  <!-- 车牌号码 -->
        plate_color,  <!-- 车牌颜色 -->
        plate_type,  <!-- 车辆类型 -->
        auth_type,  <!-- 车辆卡类型 -->
        out_in_time,  <!-- 出入场时间 -->
        park_channel_number,  <!-- 出入场通道编号 -->
        park_channel_name,  <!-- 出入场通道名称 -->
        park_channel_user,  <!-- 出入场通道操作员 -->
        park_channel_image_url,  <!-- 出入场图片http可访问路径 -->
        image_url  <!-- 本地入场图片 -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="id != null ">
            and id = #{id}  <!-- id -->
        </if>
        <if test="parkInOutOrder != null and parkInOutOrder != ''">
            and park_in_out_order = #{parkInOutOrder}  <!-- 进出唯一编号,必须和入场记录的相同 为空视为无入场记录 -->
        </if>
        <if test="parkId != null and parkId != ''">
            and park_id = #{parkId}  <!-- 车场编号 -->
        </if>
        <if test="wayType != null ">
            and way_type = #{wayType}  <!-- 出入场方式 -->
        </if>
        <if test="plateNumber != null and plateNumber != ''">
            and plate_number = #{plateNumber}  <!-- 车牌号码 -->
        </if>
        <if test="plateColor != null ">
            and plate_color = #{plateColor}  <!-- 车牌颜色 -->
        </if>
        <if test="plateType != null ">
            and plate_type = #{plateType}  <!-- 车辆类型 -->
        </if>
        <if test="authType != null ">
            and auth_type = #{authType}  <!-- 车辆卡类型 -->
        </if>
        <if test="outInTime != null ">
            and out_in_time = #{outInTime}  <!-- 出入场时间 -->
        </if>
        <if test="parkChannelNumber != null ">
            and park_channel_number = #{parkChannelNumber}  <!-- 出入场通道编号 -->
        </if>
        <if test="parkChannelName != null and parkChannelName != ''">
            and park_channel_name = #{parkChannelName}  <!-- 出入场通道名称 -->
        </if>
        <if test="parkChannelUser != null and parkChannelUser != ''">
            and park_channel_user = #{parkChannelUser}  <!-- 出入场通道操作员 -->
        </if>
        <if test="parkChannelImageUrl != null and parkChannelImageUrl != ''">
            and park_channel_image_url = #{parkChannelImageUrl}  <!-- 出入场图片http可访问路径 -->
        </if>
        <if test="imageUrl != null and imageUrl != ''">
            and image_url = #{imageUrl}  <!-- 本地入场图片 -->
        </if>
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <if test="parkInOutOrder != null and parkInOutOrder != ''">
            and park_in_out_order like concat('%', #{parkInOutOrder}, '%')  <!-- 进出唯一编号,必须和入场记录的相同 为空视为无入场记录 -->
        </if>
        <if test="parkId != null and parkId != ''">
            and park_id like concat('%', #{parkId}, '%')  <!-- 车场编号 -->
        </if>
        <if test="wayType != null ">
            and way_type = #{wayType}  <!-- 出入场方式 -->
        </if>
        <if test="plateNumber != null and plateNumber != ''">
            and plate_number like concat('%', #{plateNumber}, '%')  <!-- 车牌号码 -->
        </if>
        <if test="parkChannelName != null and parkChannelName != ''">
            and park_channel_name like concat('%', #{parkChannelName}, '%')  <!-- 出入场通道名称 -->
        </if>
        <if test="parkChannelUser != null and parkChannelUser != ''">
            and park_channel_user like concat('%', #{parkChannelUser}, '%')  <!-- 出入场通道操作员 -->
        </if>
        <if test="startTime !=null">
            and out_in_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime !=null">
            and out_in_time <![CDATA[<=]]> #{endTime}
        </if>
    </sql>
</mapper>

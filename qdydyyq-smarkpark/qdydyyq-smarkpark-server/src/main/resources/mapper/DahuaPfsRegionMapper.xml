<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.smarkpark.mapper.DahuaPfsRegionMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.smarkpark.api.entity.DahuaPfsRegion" id="dahuaPfsRegionResult">
        <result property="id" column="id"/> <!-- 主键id -->
        <result property="regionCode" column="region_code"/> <!-- 区域编码 -->
        <result property="regionName" column="region_name"/> <!-- 区域名称 -->
        <result property="regionType" column="region_type"/> <!-- 区域类型 -->
        <result property="devType" column="dev_type"/> <!-- 设备类型：1门禁 5通用客流 -->
        <result property="statisticModel" column="statistic_model"/> <!-- 统计模式：1进出统计 2区域统计 -->
        <result property="enterNumber" column="enter_number"/> <!-- 进入人数 -->
        <result property="outNumber" column="out_number"/> <!-- 出去人数 -->
        <result property="remainder" column="remainder"/> <!-- 剩余人数 -->
        <result property="planPersonCount" column="plan_person_count"/> <!-- 计划人数 -->
        <result property="upPersonCount" column="up_person_count"/> <!-- 上限人数 -->
        <result property="lowerPersonCount" column="lower_person_count"/> <!-- 下限人数 -->
        <result property="alarmChannelCode" column="alarm_channel_code"/> <!-- 报警通道编码 -->
        <result property="exceedUpperTime" column="exceed_upper_time"/> <!-- 超过上限报警时长 -->
        <result property="lessLowerTime" column="less_lower_time"/> <!-- 少于下限报警时长 -->
        <result property="notPlanTime" column="not_plan_time"/> <!-- 计划不符报警时长 -->
        <result property="chainRatio" column="chain_ratio"/> <!-- 环比值 -->
        <result property="createTime" column="create_time"/> <!-- 创建时间 -->
        <result property="updateTime" column="update_time"/> <!-- 变更时间 -->
        <result property="todayCount" column="today_count"/> <!-- 今日客流人数(已弃用) -->
        <result property="yesterdayCount" column="yesterday_count"/> <!-- 昨日客流人数(已弃用) -->
    </resultMap>


    <sql id="selectDahuaPfsRegionVo">
        select id,
               region_code,
               region_name,
               region_type,
               dev_type,
               statistic_model,
               enter_number,
               out_number,
               remainder,
               plan_person_count,
               up_person_count,
               lower_person_count,
               alarm_channel_code,
               exceed_upper_time,
               less_lower_time,
               not_plan_time,
               chain_ratio,
               create_time,
               update_time,
               today_count,
               yesterday_count
        from sk_dahua_pfs_region
    </sql>
    <!-- 查询对象List -->
    <select id="selectDahuaPfsRegionList" parameterType="DahuaPfsRegion" resultMap="dahuaPfsRegionResult">
        <include refid="selectDahuaPfsRegionVo"/>
        <where>
            <include refid="equal"/>
        </where>
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectDahuaPfsRegionListByLike" parameterType="DahuaPfsRegion" resultMap="dahuaPfsRegionResult">
        <include refid="selectDahuaPfsRegionVo"/>
        <where>
            <include refid="like"/>
        </where>
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectDahuaPfsRegionById" parameterType="Long" resultMap="dahuaPfsRegionResult">
        <include refid="selectDahuaPfsRegionVo"/>
        where id = #{id}
    </select>


    <update id="updateDahuaPfsRegion" parameterType="DahuaPfsRegion">
        update sk_dahua_pfs_region
        <trim prefix="SET" suffixOverrides=",">
            <if test="regionCode != null  and regionCode != ''  ">region_code = #{regionCode},</if>
            <if test="regionName != null  and regionName != ''  ">region_name = #{regionName},</if>
            <if test="regionType != null  ">region_type = #{regionType},</if>
            <if test="devType != null  ">dev_type = #{devType},</if>
            <if test="statisticModel != null  ">statistic_model = #{statisticModel},</if>
            <if test="enterNumber != null  ">enter_number = #{enterNumber},</if>
            <if test="outNumber != null  ">out_number = #{outNumber},</if>
            <if test="remainder != null  ">remainder = #{remainder},</if>
            <if test="planPersonCount != null  ">plan_person_count = #{planPersonCount},</if>
            <if test="upPersonCount != null  ">up_person_count = #{upPersonCount},</if>
            <if test="lowerPersonCount != null  ">lower_person_count = #{lowerPersonCount},</if>
            <if test="alarmChannelCode != null  and alarmChannelCode != ''  ">alarm_channel_code =
                #{alarmChannelCode},
            </if>
            <if test="exceedUpperTime != null  and exceedUpperTime != ''  ">exceed_upper_time = #{exceedUpperTime},</if>
            <if test="lessLowerTime != null  and lessLowerTime != ''  ">less_lower_time = #{lessLowerTime},</if>
            <if test="notPlanTime != null  and notPlanTime != ''  ">not_plan_time = #{notPlanTime},</if>
            <if test="chainRatio != null  ">chain_ratio = #{chainRatio},</if>
            <if test="createTime != null  ">create_time = #{createTime},</if>
            <if test="updateTime != null  ">update_time = #{updateTime},</if>
            <if test="todayCount != null  ">today_count = #{todayCount},</if>
            <if test="yesterdayCount != null  ">yesterday_count = #{yesterdayCount},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDahuaPfsRegionById" parameterType="Long">
        delete
        from sk_dahua_pfs_region
        where id = #{id}
    </delete>

    <delete id="deleteDahuaPfsRegionByIds" parameterType="Integer">
        delete from sk_dahua_pfs_region where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <!-- 新增对象 -->
    <insert id="insertDahuaPfsRegion" parameterType="DahuaPfsRegion" useGeneratedKeys="true" keyProperty="id">
        insert into sk_dahua_pfs_region
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="regionCode != null  and regionCode != ''  ">region_code,</if>
            <if test="regionName != null  and regionName != ''  ">region_name,</if>
            <if test="regionType != null  ">region_type,</if>
            <if test="devType != null  ">dev_type,</if>
            <if test="statisticModel != null  ">statistic_model,</if>
            <if test="enterNumber != null  ">enter_number,</if>
            <if test="outNumber != null  ">out_number,</if>
            <if test="remainder != null  ">remainder,</if>
            <if test="planPersonCount != null  ">plan_person_count,</if>
            <if test="upPersonCount != null  ">up_person_count,</if>
            <if test="lowerPersonCount != null  ">lower_person_count,</if>
            <if test="alarmChannelCode != null  and alarmChannelCode != ''  ">alarm_channel_code,</if>
            <if test="exceedUpperTime != null  and exceedUpperTime != ''  ">exceed_upper_time,</if>
            <if test="lessLowerTime != null  and lessLowerTime != ''  ">less_lower_time,</if>
            <if test="notPlanTime != null  and notPlanTime != ''  ">not_plan_time,</if>
            <if test="chainRatio != null  ">chain_ratio,</if>
            <if test="createTime != null  ">create_time,</if>
            <if test="updateTime != null  ">update_time,</if>
            <if test="todayCount != null  ">today_count,</if>
            <if test="yesterdayCount != null  ">yesterday_count,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="regionCode != null  and regionCode != ''  ">#{regionCode},</if>
            <if test="regionName != null  and regionName != ''  ">#{regionName},</if>
            <if test="regionType != null  ">#{regionType},</if>
            <if test="devType != null  ">#{devType},</if>
            <if test="statisticModel != null  ">#{statisticModel},</if>
            <if test="enterNumber != null  ">#{enterNumber},</if>
            <if test="outNumber != null  ">#{outNumber},</if>
            <if test="remainder != null  ">#{remainder},</if>
            <if test="planPersonCount != null  ">#{planPersonCount},</if>
            <if test="upPersonCount != null  ">#{upPersonCount},</if>
            <if test="lowerPersonCount != null  ">#{lowerPersonCount},</if>
            <if test="alarmChannelCode != null  and alarmChannelCode != ''  ">#{alarmChannelCode},</if>
            <if test="exceedUpperTime != null  and exceedUpperTime != ''  ">#{exceedUpperTime},</if>
            <if test="lessLowerTime != null  and lessLowerTime != ''  ">#{lessLowerTime},</if>
            <if test="notPlanTime != null  and notPlanTime != ''  ">#{notPlanTime},</if>
            <if test="chainRatio != null  ">#{chainRatio},</if>
            <if test="createTime != null  ">#{createTime},</if>
            <if test="updateTime != null  ">#{updateTime},</if>
            <if test="todayCount != null  ">#{todayCount},</if>
            <if test="yesterdayCount != null  ">#{yesterdayCount},</if>
        </trim>
    </insert>


    <!-- 清空大华同步-监控设备表 -->
    <update id="truncateDahuaPfsRegion">
        TRUNCATE TABLE sk_dahua_pfs_region;
    </update>

    <!-- 批量新增大华同步-监控设备表 -->
    <insert id="insertDahuaPfsRegionList">
        INSERT INTO sk_dahua_pfs_region (id,region_code,region_name,region_type,
        dev_type,statistic_model,enter_number,out_number,remainder,
        plan_person_count,up_person_count,lower_person_count,alarm_channel_code,
        exceed_upper_time,less_lower_time,not_plan_time,chain_ratio,
        create_time,update_time,today_count,yesterday_count) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id}, #{item.regionCode}, #{item.regionName}, #{item.regionType},
            #{item.devType},#{item.statisticModel}, #{item.enterNumber}, #{item.outNumber},
            #{item.remainder},#{item.planPersonCount}, #{item.upPersonCount}, #{item.lowerPersonCount},
            #{item.alarmChannelCode},#{item.exceedUpperTime}, #{item.lessLowerTime}, #{item.notPlanTime},
            #{item.chainRatio}, #{item.createTime}, #{item.updateTime},#{item.todayCount},
            #{item.yesterdayCount})
        </foreach>
    </insert>

    <!-- 表字段 -->
    <sql id="column">
        id,  <!-- 主键id -->
        region_code,  <!-- 区域编码 -->
        region_name,  <!-- 区域名称 -->
        region_type,  <!-- 区域类型 -->
        dev_type,  <!-- 设备类型：1门禁 5通用客流 -->
        statistic_model,  <!-- 统计模式：1进出统计 2区域统计 -->
        enter_number,  <!-- 进入人数 -->
        out_number,  <!-- 出去人数 -->
        remainder,  <!-- 剩余人数 -->
        plan_person_count,  <!-- 计划人数 -->
        up_person_count,  <!-- 上限人数 -->
        lower_person_count,  <!-- 下限人数 -->
        alarm_channel_code,  <!-- 报警通道编码 -->
        exceed_upper_time,  <!-- 超过上限报警时长 -->
        less_lower_time,  <!-- 少于下限报警时长 -->
        not_plan_time,  <!-- 计划不符报警时长 -->
        chain_ratio,  <!-- 环比值 -->
        create_time,  <!-- 创建时间 -->
        update_time,  <!-- 变更时间 -->
        today_count,  <!-- 今日客流人数(已弃用) -->
        yesterday_count  <!-- 昨日客流人数(已弃用) -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="id != null ">
            and id = #{id}  <!-- 主键id -->
        </if>
        <if test="regionCode != null and regionCode != ''">
            and region_code = #{regionCode}  <!-- 区域编码 -->
        </if>
        <if test="regionName != null and regionName != ''">
            and region_name = #{regionName}  <!-- 区域名称 -->
        </if>
        <if test="regionType != null ">
            and region_type = #{regionType}  <!-- 区域类型 -->
        </if>
        <if test="devType != null ">
            and dev_type = #{devType}  <!-- 设备类型：1门禁 5通用客流 -->
        </if>
        <if test="statisticModel != null ">
            and statistic_model = #{statisticModel}  <!-- 统计模式：1进出统计 2区域统计 -->
        </if>
        <if test="enterNumber != null ">
            and enter_number = #{enterNumber}  <!-- 进入人数 -->
        </if>
        <if test="outNumber != null ">
            and out_number = #{outNumber}  <!-- 出去人数 -->
        </if>
        <if test="remainder != null ">
            and remainder = #{remainder}  <!-- 剩余人数 -->
        </if>
        <if test="planPersonCount != null ">
            and plan_person_count = #{planPersonCount}  <!-- 计划人数 -->
        </if>
        <if test="upPersonCount != null ">
            and up_person_count = #{upPersonCount}  <!-- 上限人数 -->
        </if>
        <if test="lowerPersonCount != null ">
            and lower_person_count = #{lowerPersonCount}  <!-- 下限人数 -->
        </if>
        <if test="alarmChannelCode != null and alarmChannelCode != ''">
            and alarm_channel_code = #{alarmChannelCode}  <!-- 报警通道编码 -->
        </if>
        <if test="exceedUpperTime != null and exceedUpperTime != ''">
            and exceed_upper_time = #{exceedUpperTime}  <!-- 超过上限报警时长 -->
        </if>
        <if test="lessLowerTime != null and lessLowerTime != ''">
            and less_lower_time = #{lessLowerTime}  <!-- 少于下限报警时长 -->
        </if>
        <if test="notPlanTime != null and notPlanTime != ''">
            and not_plan_time = #{notPlanTime}  <!-- 计划不符报警时长 -->
        </if>
        <if test="chainRatio != null ">
            and chain_ratio = #{chainRatio}  <!-- 环比值 -->
        </if>
        <if test="createTime != null ">
            and create_time = #{createTime}  <!-- 创建时间 -->
        </if>
        <if test="updateTime != null ">
            and update_time = #{updateTime}  <!-- 变更时间 -->
        </if>
        <if test="todayCount != null ">
            and today_count = #{todayCount}  <!-- 今日客流人数(已弃用) -->
        </if>
        <if test="yesterdayCount != null ">
            and yesterday_count = #{yesterdayCount}  <!-- 昨日客流人数(已弃用) -->
        </if>
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <if test="regionCode != null and regionCode != ''">
            and region_code like concat('%', #{regionCode}, '%')  <!-- 区域编码 -->
        </if>
        <if test="regionName != null and regionName != ''">
            and region_name like concat('%', #{regionName}, '%')  <!-- 区域名称 -->
        </if>
        <if test="alarmChannelCode != null and alarmChannelCode != ''">
            and alarm_channel_code like concat('%', #{alarmChannelCode}, '%')  <!-- 报警通道编码 -->
        </if>
        <if test="exceedUpperTime != null and exceedUpperTime != ''">
            and exceed_upper_time like concat('%', #{exceedUpperTime}, '%')  <!-- 超过上限报警时长 -->
        </if>
        <if test="lessLowerTime != null and lessLowerTime != ''">
            and less_lower_time like concat('%', #{lessLowerTime}, '%')  <!-- 少于下限报警时长 -->
        </if>
        <if test="notPlanTime != null and notPlanTime != ''">
            and not_plan_time like concat('%', #{notPlanTime}, '%')  <!-- 计划不符报警时长 -->
        </if>
    </sql>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.smarkpark.mapper.EspDoorInfoMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.smarkpark.api.entity.EspDoorInfo" id="espDoorInfoResult">
        <result property="id" column="id"/> <!-- 门禁id -->
        <result property="deviceId" column="device_id"/> <!-- 设备id -->
        <result property="deviceStatus" column="device_status"/> <!-- 设备状态：0-离线；1-在线 -->
        <result property="doorName" column="door_name"/> <!-- 门禁名称 -->
        <result property="status" column="status"/> <!-- 设备状态：0-离线；1-在线 -->
        <result property="openStatus" column="open_status"/> <!-- 门禁状态：1-开门；2-关门；3-常开；4-常闭 -->
        <result property="doorNum" column="door_num"/> <!-- 门禁数量 -->
    </resultMap>

    <sql id="selectEspDoorInfoVo">
        select id, device_id, device_status, door_name, status, open_status, door_num
        from esp_door_info
    </sql>
    <!-- 查询对象List -->
    <select id="selectEspDoorInfoList" parameterType="EspDoorInfo" resultMap="espDoorInfoResult">
        <include refid="selectEspDoorInfoVo"/>
        <where>
            <include refid="equal"/>
        </where>
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectEspDoorInfoListByLike" parameterType="EspDoorInfo" resultMap="espDoorInfoResult">
        <include refid="selectEspDoorInfoVo"/>
        <where>
            <include refid="like"/>
        </where>
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectEspDoorInfoById" parameterType="String" resultMap="espDoorInfoResult">
        <include refid="selectEspDoorInfoVo"/>
        where id = #{id}
    </select>


    <update id="updateEspDoorInfo" parameterType="EspDoorInfo">
        update esp_door_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceId != null  and deviceId != ''  ">device_id = #{deviceId},</if>
            <if test="deviceStatus != null  ">device_status = #{deviceStatus},</if>
            <if test="doorName != null  and doorName != ''  ">door_name = #{doorName},</if>
            <if test="status != null  ">status = #{status},</if>
            <if test="openStatus != null  ">open_status = #{openStatus},</if>
            <if test="doorNum != null  ">door_num = #{doorNum},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEspDoorInfoById" parameterType="String">
        delete
        from esp_door_info
        where id = #{id}
    </delete>

    <delete id="deleteEspDoorInfoByIds" parameterType="Integer">
        delete from esp_door_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <!-- 新增对象 -->
    <insert id="insertEspDoorInfo" parameterType="EspDoorInfo">
        insert into esp_door_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null  and id != ''  ">id,</if>
            <if test="deviceId != null  and deviceId != ''  ">device_id,</if>
            <if test="deviceStatus != null  ">device_status,</if>
            <if test="doorName != null  and doorName != ''  ">door_name,</if>
            <if test="status != null  ">status,</if>
            <if test="openStatus != null  ">openStatus,</if>
            <if test="doorNum != null  ">door_num,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null  and id != ''  ">#{id},</if>
            <if test="deviceId != null  and deviceId != ''  ">#{deviceId},</if>
            <if test="deviceStatus != null  ">#{deviceStatus},</if>
            <if test="doorName != null  and doorName != ''  ">#{doorName},</if>
            <if test="status != null  ">#{status},</if>
            <if test="openStatus != null  ">#{openStatus},</if>
            <if test="doorNum != null  ">#{doorNum},</if>
        </trim>
    </insert>

    <!-- 清空大华同步-监控设备表 -->
    <update id="truncateEspDoorInfo">
        TRUNCATE TABLE esp_door_info;
    </update>

    <!-- 批量新增大华同步-监控设备表 -->
    <insert id="insertEspDoorInfoList">
        INSERT INTO esp_door_info (id,device_id,device_status,door_name,status,open_status,door_num) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id}, #{item.deviceId}, #{item.deviceStatus}, #{item.doorName},
            #{item.status},#{item.openStatus}, #{item.doorNum})
        </foreach>
    </insert>

    <!-- 表字段 -->
    <sql id="column">
        id,  <!-- 门禁id -->
        device_id,  <!-- 设备id -->
        device_status,  <!-- 设备状态：0-离线；1-在线 -->
        door_name,  <!-- 门禁名称 -->
        status,  <!-- 设备状态：0-离线；1-在线 -->
        open_status,  <!-- 门禁状态：1-开门；2-关门；3-常开；4-常闭 -->
        door_num  <!-- 门禁数量 -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="id != null and id != ''">
            and id = #{id}  <!-- 门禁id -->
        </if>
        <if test="deviceId != null and deviceId != ''">
            and device_id = #{deviceId}  <!-- 设备id -->
        </if>
        <if test="deviceStatus != null ">
            and device_status = #{deviceStatus}  <!-- 设备状态：0-离线；1-在线 -->
        </if>
        <if test="doorName != null and doorName != ''">
            and door_name = #{doorName}  <!-- 门禁名称 -->
        </if>
        <if test="status != null ">
            and status = #{status}  <!-- 设备状态：0-离线；1-在线 -->
        </if>
        <if test="doorNum != null ">
            and door_num = #{doorNum}  <!-- 门禁数量 -->
        </if>
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <if test="id != null and id != ''">
            and id like concat('%', #{id}, '%')  <!-- 门禁id -->
        </if>
        <if test="deviceId != null and deviceId != ''">
            and device_id like concat('%', #{deviceId}, '%')  <!-- 设备id -->
        </if>
        <if test="doorName != null and doorName != ''">
            and door_name like concat('%', #{doorName}, '%')  <!-- 门禁名称 -->
        </if>
    </sql>
</mapper>

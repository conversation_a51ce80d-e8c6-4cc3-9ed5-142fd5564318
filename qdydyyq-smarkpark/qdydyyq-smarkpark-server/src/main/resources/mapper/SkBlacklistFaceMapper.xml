<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.smarkpark.mapper.SkBlacklistFaceMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.smarkpark.api.entity.SkBlacklistFace" id="skBlacklistFaceResult">
        <result property="indexCode" column="index_code"/> <!-- 人脸的唯一标识 -->
        <result property="faceGroupIndexCode" column="face_group_index_code"/> <!-- 人脸分组唯一标识 -->
        <result property="name" column="name"/> <!-- 人脸的名称,1~32个字符；不能包含 ’ / \ : * ? " < > -->
        <result property="sex" column="sex"/> <!-- 人脸的性别信息，1-男性，2-女性，unknown-未知 -->
        <result property="certificateType" column="certificate_type"/> <!-- 人脸的证件类别，111-身份证，other-其它证件 -->
        <result property="certificateNum" column="certificate_num"/> <!-- 人脸的证件号码信。1~20个数字、字母 -->
        <result property="facePicUrl" column="face_pic_url"/> <!-- 人脸图片的url -->
    </resultMap>


    <sql id="selectSkBlacklistFaceVo">
        select index_code, face_group_index_code, name, sex, certificate_type, certificate_num, face_pic_url
        from sk_blacklist_face
    </sql>
    <!-- 查询对象List -->
    <select id="selectSkBlacklistFaceList" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkBlacklistFace" resultMap="skBlacklistFaceResult">
        <include refid="selectSkBlacklistFaceVo"/>
        <where>
            <include refid="equal"/>
        </where>
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectSkBlacklistFaceListByLike" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkBlacklistFace" resultMap="skBlacklistFaceResult">
        <include refid="selectSkBlacklistFaceVo"/>
        <where>
            <include refid="like"/>
        </where>
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectSkBlacklistFaceById" parameterType="String" resultMap="skBlacklistFaceResult">
        <include refid="selectSkBlacklistFaceVo"/>
        where index_code = #{indexCode}
    </select>


    <update id="updateSkBlacklistFace" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkBlacklistFace">
        update sk_blacklist_face
        <trim prefix="SET" suffixOverrides=",">
            <if test="faceGroupIndexCode != null  and faceGroupIndexCode != ''  ">face_group_index_code = #{faceGroupIndexCode},</if>
            <if test="name != null  and name != ''  ">name = #{name},</if>
            <if test="sex != null  and sex != ''  ">sex = #{sex},</if>
            <if test="certificateType != null  and certificateType != ''  ">certificate_type = #{certificateType},</if>
            <if test="certificateNum != null  and certificateNum != ''  ">certificate_num = #{certificateNum},</if>
            <if test="facePicUrl != null  and facePicUrl != ''  ">face_pic_url = #{facePicUrl},</if>
        </trim>
        where index_code = #{indexCode}
    </update>

    <delete id="deleteSkBlacklistFaceById" parameterType="String">
        delete
        from sk_blacklist_face
        where index_code = #{indexCode}
    </delete>

    <delete id="deleteSkBlacklistFaceByIds" parameterType="Integer">
        delete from sk_blacklist_face where index_code in
        <foreach item="indexCode" collection="array" open="(" separator="," close=")">
            #{indexCode}
        </foreach>
    </delete>


    <!-- 新增对象 -->
    <insert id="insertSkBlacklistFace" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkBlacklistFace">
        insert into sk_blacklist_face
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="indexCode != null  and indexCode != ''  ">index_code,</if>
            <if test="faceGroupIndexCode != null  and faceGroupIndexCode != ''  ">face_group_index_code,</if>
            <if test="name != null  and name != ''  ">name,</if>
            <if test="sex != null  and sex != ''  ">sex,</if>
            <if test="certificateType != null  and certificateType != ''  ">certificate_type,</if>
            <if test="certificateNum != null  and certificateNum != ''  ">certificate_num,</if>
            <if test="facePicUrl != null  and facePicUrl != ''  ">face_pic_url,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="indexCode != null  and indexCode != ''  ">#{indexCode},</if>
            <if test="faceGroupIndexCode != null  and faceGroupIndexCode != ''  ">#{faceGroupIndexCode},</if>
            <if test="name != null  and name != ''  ">#{name},</if>
            <if test="sex != null  and sex != ''  ">#{sex},</if>
            <if test="certificateType != null  and certificateType != ''  ">#{certificateType},</if>
            <if test="certificateNum != null  and certificateNum != ''  ">#{certificateNum},</if>
            <if test="facePicUrl != null  and facePicUrl != ''  ">#{facePicUrl},</if>
        </trim>
    </insert>


    <!-- 表字段 -->
    <sql id="column">
        index_code,  <!-- 人脸的唯一标识 -->
        face_group_index_code,  <!-- 人脸分组唯一标识 -->
        name,  <!-- 人脸的名称,1~32个字符；不能包含 ’ / \ : * ? " < > -->
        sex,  <!-- 人脸的性别信息，1-男性，2-女性，unknown-未知 -->
        certificate_type,  <!-- 人脸的证件类别，111-身份证，other-其它证件 -->
        certificate_num,  <!-- 人脸的证件号码信。1~20个数字、字母 -->
        face_pic_url  <!-- 人脸图片的url -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="indexCode != null and indexCode != ''">
            and index_code = #{indexCode}  <!-- 人脸的唯一标识 -->
        </if>
        <if test="faceGroupIndexCode != null and faceGroupIndexCode != ''">
            and face_group_index_code = #{faceGroupIndexCode}  <!-- 人脸分组唯一标识 -->
        </if>
        <if test="name != null and name != ''">
            and name = #{name}  <!-- 人脸的名称,1~32个字符；不能包含 ’ / \ : * ? " < > -->
        </if>
        <if test="sex != null and sex != ''">
            and sex = #{sex}  <!-- 人脸的性别信息，1-男性，2-女性，unknown-未知 -->
        </if>
        <if test="certificateType != null and certificateType != ''">
            and certificate_type = #{certificateType}  <!-- 人脸的证件类别，111-身份证，other-其它证件 -->
        </if>
        <if test="facePicUrl != null and facePicUrl != ''">
            and face_pic_url = #{facePicUrl}  <!-- 人脸图片的url -->
        </if>
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <if test="indexCode != null and indexCode != ''">
            and index_code like concat('%', #{indexCode}, '%')  <!-- 人脸的唯一标识 -->
        </if>
        <if test="faceGroupIndexCode != null and faceGroupIndexCode != ''">
            and face_group_index_code like concat('%', #{faceGroupIndexCode}, '%')  <!-- 人脸分组唯一标识 -->
        </if>
        <if test="name != null and name != ''">
            and name like concat('%', #{name}, '%')  <!-- 人脸的名称,1~32个字符；不能包含 ’ / \ : * ? " < > -->
        </if>
        <if test="sex != null and sex != ''">
            and sex like concat('%', #{sex}, '%')  <!-- 人脸的性别信息，1-男性，2-女性，unknown-未知 -->
        </if>
        <if test="certificateType != null and certificateType != ''">
            and certificate_type like concat('%', #{certificateType}, '%')  <!-- 人脸的证件类别，111-身份证，other-其它证件 -->
        </if>
        <if test="facePicUrl != null and facePicUrl != ''">
            and face_pic_url like concat('%', #{facePicUrl}, '%')  <!-- 人脸图片的url -->
        </if>
    </sql>
</mapper>

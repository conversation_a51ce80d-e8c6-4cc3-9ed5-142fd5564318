<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.smarkpark.mapper.SkAlarmLogMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.smarkpark.api.entity.SkAlarmLog" id="skAlarmLogResult">
        <result property="eventId" column="event_id"/> <!-- 事件唯一标识 -->
        <result property="alarmSource" column="alarm_source"/> <!-- 告警来源 0-海康 1-消防 -->
        <result property="sendTime" column="send_time"/> <!-- 告警时间 -->
        <result property="srcIndex" column="src_index"/> <!-- 事件源编号，物理设备是资源编号 -->
        <result property="srcType" column="src_type"/> <!-- 事件源类型 -->
        <result property="srcName" column="src_name"/> <!-- 事件源名称 -->
        <result property="eventType" column="event_type"/> <!-- 事件类型 -->
        <result property="ipAddress" column="ip_address"/> <!-- 设备的ip地址 -->
        <result property="portNo" column="port_no"/> <!-- 设备端口号 -->
        <result property="imageUrl" column="image_url"/> <!-- 背景图url -->
        <result property="eventContent" column="event_content"/> <!-- 事件内容 -->
        <result property="handleOpinion" column="handle_opinion"/> <!-- 处理意见 -->
        <result property="handleStatus" column="handle_status"/> <!-- 处理状态 -->
        <result property="handleFile" column="handle_file"/> <!-- 处理附件 -->
        <result property="handleBy" column="handle_by"/> <!-- 处理人 -->
        <result property="handleByName" column="handle_by_name"/> <!-- 处理人姓名 -->
        <result property="handleTime" column="handle_time"/> <!-- 处理时间 -->
    </resultMap>


    <sql id="selectSkAlarmLogVo">
        select
            DISTINCT event_id,
            alarm_source,
            send_time,
            src_index,
            src_name,
            event_type,
            event_content,
            handle_status,
            handle_by_name,
            handle_time,
            dict.dict_name,
            dict.dict_remarks,
            dict_index.dict_index_name,
            src_name as device_name
        from sk_alarm_log
        left join (select id as dict_id, `code` as dict_code, `name` as dict_name, remarks as dict_remarks from sys_dict where del_flag = 1) dict on dict.dict_code = event_type
        left join sys_dict_relevance relevance on relevance.dict_id = dict.dict_id
        left join (select id as dict_index_id, `code` as dict_index_code, `name` as dict_index_name from sys_dict_index where del_flag = 1) dict_index on dict_index.dict_index_id = relevance.index_id
        inner join (select event_type as etype, notify_person from sk_alarm_notify_rule WHERE del_flag = 1) rule on FIND_IN_SET(event_type, rule.etype) <if test="userName != null and userName != ''">and FIND_IN_SET(#{userName}, rule.notify_person)</if>
    </sql>
    <!-- 查询对象List -->
    <select id="selectSkAlarmLogList" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkAlarmLog" resultMap="skAlarmLogResult">
        <include refid="selectSkAlarmLogVo"/>
        <where>
            <include refid="equal"/>
        </where>
        order by IF(handle_status=0,0,1) ASC, send_time DESC
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectSkAlarmLogListByLike" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkAlarmLog" resultMap="skAlarmLogResult">
        <include refid="selectSkAlarmLogVo"/>
        <where>
            <include refid="like"/>
            <if test="clientType != null and clientType == 'bigScreen'">
                <if test="srcIndex != null and srcIndex != ''">
                    and FIND_IN_SET(src_index, (
                    SELECT GROUP_CONCAT(CONCAT(szzc.device_id, '-', szzc.zone_id))
                    FROM sk_zhoujie_zone_cameras szzc
                    WHERE szzc.del_flag = 1 and szzc.camera_code = #{srcIndex})) > 0
                </if>
            </if>
            <if test="clientType == null or clientType != 'bigScreen'">
                <if test="srcIndex != null and srcIndex != ''">
                    and src_index = #{srcIndex}  <!-- 事件源编号，物理设备是资源编号 -->
                </if>
            </if>
            <if test="dictName != null and dictName != ''">
                and dict.dict_name like concat('%', #{dictName}, '%')
            </if>
            <if test="dictIndexName != null and dictIndexName != ''">
                and dict_index.dict_index_name like concat('%', #{dictIndexName}, '%')
            </if>
            <if test="deviceName != null and deviceName != ''">
                and src_name like concat('%', #{deviceName}, '%')
            </if>
            <if test="startTime != null and startTime != ''">
                and send_time &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and send_time &lt;= #{endTime}
            </if>
            and alarm_source = #{alarmSource}
        </where>
        order by IF(handle_status=0,0,1) ASC, send_time DESC
    </select>

    <!-- 表字段 -->
    <sql id="column">
        alarm_source,  <!-- 告警来源 0-海康 1-消防 -->
        send_time,  <!-- 告警时间 -->
        event_id,  <!-- 事件唯一标识 -->
        src_index,  <!-- 事件源编号，物理设备是资源编号 -->
        src_type,  <!-- 事件源类型 -->
        src_name,  <!-- 事件源名称 -->
        event_type,  <!-- 事件类型 -->
        ip_address,  <!-- 设备的ip地址 -->
        port_no,  <!-- 设备端口号 -->
        image_url  <!-- 背景图url -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="alarmSource != null ">
            and alarm_source = #{alarmSource}  <!-- 告警来源 0-海康 1-消防 -->
        </if>
        <if test="sendTime != null ">
            and send_time = #{sendTime}  <!-- 告警时间 -->
        </if>
        <if test="eventId != null and eventId != ''">
            and event_id = #{eventId}  <!-- 事件唯一标识 -->
        </if>
        <if test="srcIndex != null and srcIndex != ''">
            and src_index = #{srcIndex}  <!-- 事件源编号，物理设备是资源编号 -->
        </if>
        <if test="srcType != null and srcType != ''">
            and src_type = #{srcType}  <!-- 事件源类型 -->
        </if>
        <if test="srcName != null and srcName != ''">
            and src_name = #{srcName}  <!-- 事件源名称 -->
        </if>
        <if test="eventType != null and eventType != ''">
            and event_type = #{eventType}  <!-- 事件类型 -->
        </if>
        <if test="ipAddress != null and ipAddress != ''">
            and ip_address = #{ipAddress}  <!-- 设备的ip地址 -->
        </if>
        <if test="portNo != null and portNo != ''">
            and port_no = #{portNo}  <!-- 设备端口号 -->
        </if>
        <if test="imageUrl != null and imageUrl != ''">
            and image_url = #{imageUrl}  <!-- 背景图url -->
        </if>
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <if test="eventId != null and eventId != ''">
            and event_id = #{eventId}  <!-- 事件唯一标识 -->
        </if>
        <if test="srcType != null and srcType != ''">
            and src_type = #{srcType}  <!-- 事件源类型 -->
        </if>
        <if test="srcName != null and srcName != ''">
            and src_name like concat('%', #{srcName}, '%')  <!-- 事件源名称 -->
        </if>
        <if test="eventType != null and eventType != ''">
            and event_type like concat('%', #{eventType}, '%')  <!-- 事件类型 -->
        </if>
        <if test="ipAddress != null and ipAddress != ''">
            and ip_address like concat('%', #{ipAddress}, '%')  <!-- 设备的ip地址 -->
        </if>
        <if test="portNo != null and portNo != ''">
            and port_no like concat('%', #{portNo}, '%')  <!-- 设备端口号 -->
        </if>
        <if test="imageUrl != null and imageUrl != ''">
            and image_url like concat('%', #{imageUrl}, '%')  <!-- 背景图url -->
        </if>
        <!-- 处理状态 -->
        <if test="handleStatus != null  and handleStatus != ''  ">
            and handle_status = #{handleStatus}
        </if>
    </sql>


    <update id="handleBatch">
        update sk_alarm_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="handleOpinion != null  and handleOpinion != ''  ">handle_opinion = #{handleOpinion},</if>
            <if test="handleStatus != null  and handleStatus != ''  ">handle_status = #{handleStatus},</if>
            <if test="handleFile != null  and handleFile != ''  ">handle_file = #{handleFile},</if>
            <if test="handleBy != null  and handleBy != ''  ">handle_by = #{handleBy},</if>
            <if test="handleByName != null  and handleByName != ''  ">handle_by_name = #{handleByName},</if>
            <if test="handleTime != null ">handle_time = #{handleTime},</if>
        </trim>
        <where>
            <if test="eventIdList != null and eventIdList.size() > 0">
                and event_id in
                <foreach collection="eventIdList" item="eventId" open="(" separator="," close=")">
                    #{eventId}
                </foreach>
            </if>
            <if test="srcIndex != null  and srcIndex != ''  ">
                and src_index = #{srcIndex}
            </if>
            and handle_status = 0
        </where>
    </update>

    <select id="getAlarmNotifyCount" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT log.event_id)
        FROM sk_alarm_log log
        INNER JOIN (SELECT event_type, notify_person FROM sk_alarm_notify_rule WHERE del_flag = 1) rule on FIND_IN_SET(log.event_type, rule.event_type) AND FIND_IN_SET(#{userName}, rule.notify_person)
        WHERE handle_status = '0'
    </select>

    <select id="getAlarmNotifyType" resultType="java.lang.Integer">
        SELECT min(log.alarm_source)
        FROM sk_alarm_log log
        INNER JOIN (SELECT event_type, notify_person FROM sk_alarm_notify_rule WHERE del_flag = 1) rule on FIND_IN_SET(log.event_type, rule.event_type) AND FIND_IN_SET(#{userName}, rule.notify_person)
        WHERE handle_status = '0'
    </select>

    <select id="alarmClassification" resultType="java.util.Map">
        select
            event_type,
            dict.dict_name as `name`,
            COUNT(1) as `total`
        from sk_alarm_log
        left join (select id as dict_id, `code` as dict_code, `name` as dict_name from sys_dict where del_flag = 1) dict on dict.dict_code = event_type
        where alarm_source = #{alarmSource}
        GROUP BY event_type,dict.dict_name
    </select>

    <select id="alarmLevel" resultType="java.util.Map">
        select
            dict.dict_name as `name`,
            COUNT(1) as `total`
        from sk_alarm_log
        left join (select `code` as dict_code, remarks as dict_name from sys_dict where del_flag = 1) dict on dict.dict_code = event_type
        where alarm_source = #{alarmSource}
        GROUP BY dict.dict_name
    </select>

    <select id="alarmStatistics" resultType="java.util.Map">
        select
            '待处理' as `name`,
            IFNULL(SUM(IF(handle_status = 0, 1, 0)), 0) as `value`
        from sk_alarm_log
        where alarm_source = #{alarmSource}

        union all

        select
            '已处理' as `name`,
            IFNULL(SUM(IF(handle_status != 0, 1, 0)), 0) as `value`
        from sk_alarm_log
        where alarm_source = #{alarmSource}
    </select>

    <select id="alarmTrend" resultType="java.util.Map">
        select t.time, IFNULL(log.total, 0) as total
        from (
                 select CONCAT(#{year}, '-01') time
                 UNION select CONCAT(#{year}, '-02')
                 UNION select CONCAT(#{year}, '-03')
                 UNION select CONCAT(#{year}, '-04')
                 UNION select CONCAT(#{year}, '-05')
                 UNION select CONCAT(#{year}, '-06')
                 UNION select CONCAT(#{year}, '-07')
                 UNION select CONCAT(#{year}, '-08')
                 UNION select CONCAT(#{year}, '-09')
                 UNION select CONCAT(#{year}, '-10')
                 UNION select CONCAT(#{year}, '-11')
                 UNION select CONCAT(#{year}, '-12')
             ) t
                 LEFT JOIN (
            select
                DATE_FORMAT(send_time, '%Y-%m') as stime,
                COUNT(1) as total
            from sk_alarm_log
            where alarm_source = #{alarmSource}
            GROUP BY DATE_FORMAT(send_time, '%Y-%m')
        ) log on log.stime = t.time
        ORDER BY t.time
    </select>

    <select id="selectMailsByUsers" resultType="java.lang.String">
        select email from sys_user where username in
        <foreach collection="users" item="user" open="(" separator="," close=")">
            #{user}
        </foreach>
    </select>

    <select id="selectSkAlarmLogById" resultType="com.huazheng.tunny.smarkpark.api.entity.SkAlarmLog">
        select
            event_id,
            alarm_source,
            send_time,
            src_index,
            src_type,
            src_name,
            event_type,
            ip_address,
            port_no,
            image_url,
            handle_opinion,
            handle_status,
            handle_file,
            handle_by,
            handle_by_name,
            handle_time,event_content,
            dict.dict_name,
            dict.dict_remarks,
            dict_index.dict_index_name,
            IFNULL(cameras.device_name, src_name) as device_name
        from sk_alarm_log
                 left join (select id as dict_id, `code` as dict_code, `name` as dict_name, remarks as dict_remarks from sys_dict where del_flag = 1) dict on dict.dict_code = event_type
                 left join sys_dict_relevance relevance on relevance.dict_id = dict.dict_id
                 left join (select id as dict_index_id, `code` as dict_index_code, `name` as dict_index_name from sys_dict_index where del_flag = 1) dict_index on dict_index.dict_index_id = relevance.index_id
                 left join (select index_code, `name` as device_name from sk_hdvision_cameras) cameras on cameras.index_code = src_index
        where event_id = #{eventId}
    </select>

    <select id="getFireControlDeviceName" resultType="java.lang.String">
        SELECT `name` FROM `smarkpark_iot`.th_iot_device WHERE `code` = #{deviceCode}
    </select>

    <!-- 迁移30天前的告警日志到备份表 -->
    <insert id="transferDataToHisTable">
        INSERT INTO sk_alarm_log_his
        SELECT sk_alarm_log.*
        FROM sk_alarm_log
        LEFT JOIN (SELECT event_id FROM sk_alarm_log_his WHERE send_time &lt; DATE_SUB(NOW(), INTERVAL ${days} DAY)) his ON his.event_id = sk_alarm_log.event_id
        WHERE his.event_id IS NULL AND send_time &lt; DATE_SUB(NOW(), INTERVAL ${days} DAY)
    </insert>

    <!-- 清理30天前的告警日志 -->
    <insert id="cleanUpAlarmLog">
        DELETE FROM sk_alarm_log WHERE send_time &lt; DATE_SUB(NOW(), INTERVAL ${days} DAY)
    </insert>
</mapper>

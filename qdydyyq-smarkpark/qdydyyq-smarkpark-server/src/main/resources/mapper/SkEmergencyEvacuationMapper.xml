<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.smarkpark.mapper.SkEmergencyEvacuationMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.smarkpark.api.entity.SkEmergencyEvacuation" id="skEmergencyEvacuationResult">
        <result property="id" column="id"/> <!--  -->
        <result property="evacuationRouteName" column="evacuation_route_name"/> <!-- 疏散线路名称 -->
        <result property="isEnabled" column="is_enabled"/> <!-- 是否开启 0-关闭 1-开启 -->
        <result property="cameraIndexCodes" column="camera_index_codes"/> <!-- 摄像头唯一标识编码 -->
        <result property="messageSendingEquipment" column="message_sending_equipment"/> <!-- 信发设备id -->
        <result property="escapeRouteMap" column="escape_route_map"/> <!-- 逃生路线图 -->
        <result property="delFlag" column="del_flag"/> <!-- 0-删除，1-正常 -->
        <result property="createTime" column="create_time"/> <!-- 创建时间 -->
        <result property="createBy" column="create_by"/> <!-- 创建人 -->
        <result property="updateTime" column="update_time"/> <!-- 修改时间 -->
        <result property="updateBy" column="update_by"/> <!-- 修改人 -->
    </resultMap>


    <sql id="selectSkEmergencyEvacuationVo">
        select id,
               evacuation_route_name,
               is_enabled,
               camera_index_codes,
               message_sending_equipment,
               escape_route_map,
               del_flag,
               create_time,
               create_by,
               update_time,
               update_by
        from sk_emergency_evacuation
    </sql>
    <!-- 查询对象List -->
    <select id="selectSkEmergencyEvacuationList" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkEmergencyEvacuation" resultMap="skEmergencyEvacuationResult">
        <include refid="selectSkEmergencyEvacuationVo"/>
        <where>
            <include refid="equal"/>
            AND del_flag = '1'
        </where>
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectSkEmergencyEvacuationListByLike" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkEmergencyEvacuation" resultMap="skEmergencyEvacuationResult">
        <include refid="selectSkEmergencyEvacuationVo"/>
        <where>
            <include refid="like"/>
            AND del_flag = '1'
        </where>
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectSkEmergencyEvacuationById" parameterType="Integer" resultMap="skEmergencyEvacuationResult">
        <include refid="selectSkEmergencyEvacuationVo"/>
        where id = #{id}
    </select>


    <update id="updateSkEmergencyEvacuation" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkEmergencyEvacuation">
        update sk_emergency_evacuation
        <trim prefix="SET" suffixOverrides=",">
            <if test="evacuationRouteName != null  and evacuationRouteName != ''  ">evacuation_route_name = #{evacuationRouteName},</if>
            <if test="isEnabled != null  ">is_enabled = #{isEnabled},</if>
            <if test="cameraIndexCodes != null  and cameraIndexCodes != ''  ">camera_index_codes = #{cameraIndexCodes},</if>
            <if test="messageSendingEquipment != null  and messageSendingEquipment != ''  ">message_sending_equipment = #{messageSendingEquipment},</if>
            <if test="escapeRouteMap != null  and escapeRouteMap != ''  ">escape_route_map = #{escapeRouteMap},</if>
            <if test="delFlag != null  and delFlag != ''  ">del_flag = #{delFlag},</if>
            <if test="createTime != null  ">create_time = #{createTime},</if>
            <if test="createBy != null  and createBy != ''  ">create_by = #{createBy},</if>
            <if test="updateTime != null  ">update_time = #{updateTime},</if>
            <if test="updateBy != null  and updateBy != ''  ">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteSkEmergencyEvacuationById" parameterType="Integer">
        update sk_emergency_evacuation set del_flag = '0' where id = #{id}
    </update>

    <delete id="deleteSkEmergencyEvacuationByIds" parameterType="Integer">
        delete from sk_emergency_evacuation where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <!-- 新增对象 -->
    <insert id="insertSkEmergencyEvacuation" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkEmergencyEvacuation" useGeneratedKeys="true" keyProperty="id">
        insert into sk_emergency_evacuation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="evacuationRouteName != null  and evacuationRouteName != ''  ">evacuation_route_name,</if>
            <if test="isEnabled != null  ">is_enabled,</if>
            <if test="cameraIndexCodes != null  and cameraIndexCodes != ''  ">camera_index_codes,</if>
            <if test="messageSendingEquipment != null  and messageSendingEquipment != ''  ">message_sending_equipment,</if>
            <if test="escapeRouteMap != null  and escapeRouteMap != ''  ">escape_route_map,</if>
            <if test="delFlag != null  and delFlag != ''  ">del_flag,</if>
            <if test="createTime != null  ">create_time,</if>
            <if test="createBy != null  and createBy != ''  ">create_by,</if>
            <if test="updateTime != null  ">update_time,</if>
            <if test="updateBy != null  and updateBy != ''  ">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="evacuationRouteName != null  and evacuationRouteName != ''  ">#{evacuationRouteName},</if>
            <if test="isEnabled != null  ">#{isEnabled},</if>
            <if test="cameraIndexCodes != null  and cameraIndexCodes != ''  ">#{cameraIndexCodes},</if>
            <if test="messageSendingEquipment != null  and messageSendingEquipment != ''  ">#{messageSendingEquipment},</if>
            <if test="escapeRouteMap != null  and escapeRouteMap != ''  ">#{escapeRouteMap},</if>
            <if test="delFlag != null  and delFlag != ''  ">#{delFlag},</if>
            <if test="createTime != null  ">#{createTime},</if>
            <if test="createBy != null  and createBy != ''  ">#{createBy},</if>
            <if test="updateTime != null  ">#{updateTime},</if>
            <if test="updateBy != null  and updateBy != ''  ">#{updateBy},</if>
        </trim>
    </insert>


    <!-- 表字段 -->
    <sql id="column">
        id,  <!--  -->
        evacuation_route_name,  <!-- 疏散线路名称 -->
        is_enabled,  <!-- 是否开启 0-关闭 1-开启 -->
        camera_index_codes,  <!-- 摄像头唯一标识编码 -->
        message_sending_equipment,  <!-- 信发设备id -->
        escape_route_map,  <!-- 逃生路线图 -->
        del_flag,  <!-- 0-删除，1-正常 -->
        create_time,  <!-- 创建时间 -->
        create_by,  <!-- 创建人 -->
        update_time,  <!-- 修改时间 -->
        update_by  <!-- 修改人 -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="id != null ">
            and id = #{id}  <!--  -->
        </if>
        <if test="evacuationRouteName != null and evacuationRouteName != ''">
            and evacuation_route_name = #{evacuationRouteName}  <!-- 疏散线路名称 -->
        </if>
        <if test="isEnabled != null ">
            and is_enabled = #{isEnabled}  <!-- 是否开启 0-关闭 1-开启 -->
        </if>
        <if test="cameraIndexCodes != null and cameraIndexCodes != ''">
            and camera_index_codes = #{cameraIndexCodes}  <!-- 摄像头唯一标识编码 -->
        </if>
        <if test="messageSendingEquipment != null and messageSendingEquipment != ''">
            and message_sending_equipment = #{messageSendingEquipment}  <!-- 信发设备id -->
        </if>
        <if test="escapeRouteMap != null and escapeRouteMap != ''">
            and escape_route_map = #{escapeRouteMap}  <!-- 逃生路线图 -->
        </if>
        <if test="delFlag != null and delFlag != ''">
            and del_flag = #{delFlag}  <!-- 0-删除，1-正常 -->
        </if>
        <if test="createTime != null ">
            and create_time = #{createTime}  <!-- 创建时间 -->
        </if>
        <if test="createBy != null and createBy != ''">
            and create_by = #{createBy}  <!-- 创建人 -->
        </if>
        <if test="updateTime != null ">
            and update_time = #{updateTime}  <!-- 修改时间 -->
        </if>
        <if test="updateBy != null and updateBy != ''">
            and update_by = #{updateBy}  <!-- 修改人 -->
        </if>
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <if test="evacuationRouteName != null and evacuationRouteName != ''">
            and evacuation_route_name like concat('%', #{evacuationRouteName}, '%')  <!-- 疏散线路名称 -->
        </if>
        <if test="cameraIndexCodes != null and cameraIndexCodes != ''">
            and camera_index_codes like concat('%', #{cameraIndexCodes}, '%')  <!-- 摄像头唯一标识编码 -->
        </if>
        <if test="messageSendingEquipment != null and messageSendingEquipment != ''">
            and message_sending_equipment like concat('%', #{messageSendingEquipment}, '%')  <!-- 信发设备id -->
        </if>
        <if test="escapeRouteMap != null and escapeRouteMap != ''">
            and escape_route_map like concat('%', #{escapeRouteMap}, '%')  <!-- 逃生路线图 -->
        </if>
        <if test="delFlag != null and delFlag != ''">
            and del_flag like concat('%', #{delFlag}, '%')  <!-- 0-删除，1-正常 -->
        </if>
        <if test="createBy != null and createBy != ''">
            and create_by like concat('%', #{createBy}, '%')  <!-- 创建人 -->
        </if>
        <if test="updateBy != null and updateBy != ''">
            and update_by like concat('%', #{updateBy}, '%')  <!-- 修改人 -->
        </if>
    </sql>
</mapper>

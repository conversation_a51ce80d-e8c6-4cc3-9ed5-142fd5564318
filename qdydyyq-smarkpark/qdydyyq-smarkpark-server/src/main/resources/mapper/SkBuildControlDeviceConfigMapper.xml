<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.smarkpark.mapper.SkBuildControlDeviceConfigMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.smarkpark.api.entity.SkBuildControlDeviceConfig"
               id="skBuildControlDeviceConfigResult">
        <result property="id" column="id"/> <!-- 主键id -->
        <result property="trueCpnName" column="true_cpn_name"/> <!-- cpn名称 -->
        <result property="paramId" column="param_id"/> <!-- 属性id -->
        <result property="parmName" column="parm_name"/> <!-- 属性名称 -->
        <result property="type" column="type"/> <!-- 属性值类型 -->
        <result property="address" column="address"/> <!-- 属性地址 -->
        <result property="appName" column="app_name"/> <!-- 应用名称 -->
        <result property="showName" column="show_name"/> <!-- 展示名称 -->
        <result property="showNameAlias" column="show_name_alias"/> <!-- 展示名称别称 -->
        <result property="showValue" column="show_value"/> <!-- 展示值 -->
        <result property="unit" column="unit"/> <!-- 属性单位 -->
        <result property="statusVar" column="status_var"/> <!-- 状态变量 -->
        <result property="varType" column="var_type"/> <!-- 变量类型 -->
        <result property="isSet" column="is_set"/> <!-- 是否可写(0:只读；1:读写) -->
        <result property="groupName" column="group_name"/> <!-- 组名 -->
        <result property="deviceCode" column="device_code"/> <!-- 设备编号 -->
    </resultMap>


    <sql id="selectSkBuildControlDeviceConfigVo">
        select id,
               true_cpn_name,
               param_id,
               parm_name,
               type,
               address,
               app_name,
               show_name,
               show_name_alias,
               show_value,
               unit,
               status_var,
               var_type,
               is_set,
               group_name,device_code
        from sk_build_control_device_config
    </sql>
    <!-- 查询对象List -->
    <select id="selectSkBuildControlDeviceConfigList" parameterType="SkBuildControlDeviceConfig"
            resultMap="skBuildControlDeviceConfigResult">
        <include refid="selectSkBuildControlDeviceConfigVo"/>
        <where>
            <include refid="equal"/>
        </where>
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectSkBuildControlDeviceConfigListByLike" parameterType="SkBuildControlDeviceConfig"
            resultMap="skBuildControlDeviceConfigResult">
        <include refid="selectSkBuildControlDeviceConfigVo"/>
        <where>
            <include refid="like"/>
        </where>
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectSkBuildControlDeviceConfigById" parameterType="Long" resultMap="skBuildControlDeviceConfigResult">
        <include refid="selectSkBuildControlDeviceConfigVo"/>
        where id = #{id}
    </select>


    <update id="updateSkBuildControlDeviceConfig" parameterType="SkBuildControlDeviceConfig">
        update sk_build_control_device_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="trueCpnName != null  and trueCpnName != ''  ">true_cpn_name = #{trueCpnName},</if>
            <if test="paramId != null  ">param_id = #{paramId},</if>
            <if test="parmName != null  and parmName != ''  ">parm_name = #{parmName},</if>
            <if test="type != null  and type != ''  ">type = #{type},</if>
            <if test="address != null  and address != ''  ">address = #{address},</if>
            <if test="appName != null  and appName != ''  ">app_name = #{appName},</if>
            <if test="showName != null  and showName != ''  ">show_name = #{showName},</if>
            <if test="showNameAlias != null  and showNameAlias != ''  ">show_name_alias = #{showNameAlias},</if>
            <if test="showValue != null  and showValue != ''  ">show_value = #{showValue},</if>
            <if test="unit != null  and unit != ''  ">unit = #{unit},</if>
            <if test="statusVar != null  ">status_var = #{statusVar},</if>
            <if test="varType != null  ">var_type = #{varType},</if>
            <if test="isSet != null  ">is_set = #{isSet},</if>
            <if test="groupName != null  and groupName != ''  ">group_name = #{groupName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSkBuildControlDeviceConfigById" parameterType="Long">
        delete
        from sk_build_control_device_config
        where id = #{id}
    </delete>

    <delete id="deleteSkBuildControlDeviceConfigByIds" parameterType="Integer">
        delete from sk_build_control_device_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <!-- 新增对象 -->
    <insert id="insertSkBuildControlDeviceConfig" parameterType="SkBuildControlDeviceConfig" useGeneratedKeys="true"
            keyProperty="id">
        insert into sk_build_control_device_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="trueCpnName != null  and trueCpnName != ''  ">true_cpn_name,</if>
            <if test="paramId != null  ">param_id,</if>
            <if test="parmName != null  and parmName != ''  ">parm_name,</if>
            <if test="type != null  and type != ''  ">type,</if>
            <if test="address != null  and address != ''  ">address,</if>
            <if test="appName != null  and appName != ''  ">app_name,</if>
            <if test="showName != null  and showName != ''  ">show_name,</if>
            <if test="showNameAlias != null  and showNameAlias != ''  ">show_name_alias,</if>
            <if test="showValue != null  and showValue != ''  ">show_value,</if>
            <if test="unit != null  and unit != ''  ">unit,</if>
            <if test="statusVar != null  ">status_var,</if>
            <if test="varType != null  ">var_type,</if>
            <if test="isSet != null  ">is_set,</if>
            <if test="groupName != null  and groupName != ''  ">group_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="trueCpnName != null  and trueCpnName != ''  ">#{trueCpnName},</if>
            <if test="paramId != null  ">#{paramId},</if>
            <if test="parmName != null  and parmName != ''  ">#{parmName},</if>
            <if test="type != null  and type != ''  ">#{type},</if>
            <if test="address != null  and address != ''  ">#{address},</if>
            <if test="appName != null  and appName != ''  ">#{appName},</if>
            <if test="showName != null  and showName != ''  ">#{showName},</if>
            <if test="showNameAlias != null  and showNameAlias != ''  ">#{showNameAlias},</if>
            <if test="showValue != null  and showValue != ''  ">#{showValue},</if>
            <if test="unit != null  and unit != ''  ">#{unit},</if>
            <if test="statusVar != null  ">#{statusVar},</if>
            <if test="varType != null  ">#{varType},</if>
            <if test="isSet != null  ">#{isSet},</if>
            <if test="groupName != null  and groupName != ''  ">#{groupName},</if>
        </trim>
    </insert>


    <!-- 表字段 -->
    <sql id="column">
        id,  <!-- 主键id -->
        true_cpn_name,  <!-- cpn名称 -->
        param_id,  <!-- 属性id -->
        parm_name,  <!-- 属性名称 -->
        type,  <!-- 属性值类型 -->
        address,  <!-- 属性地址 -->
        app_name,  <!-- 应用名称 -->
        show_name,  <!-- 展示名称 -->
        show_name_alias,  <!-- 展示名称别称 -->
        show_value,  <!-- 展示值 -->
        unit,  <!-- 属性单位 -->
        status_var,  <!-- 状态变量 -->
        var_type,  <!-- 变量类型 -->
        is_set,  <!-- 是否可写(0:只读；1:读写) -->
        group_name  <!-- 组名 -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="id != null ">
            and id = #{id}  <!-- 主键id -->
        </if>
        <if test="trueCpnName != null and trueCpnName != ''">
            and true_cpn_name = #{trueCpnName}  <!-- cpn名称 -->
        </if>
        <if test="paramId != null ">
            and param_id = #{paramId}  <!-- 属性id -->
        </if>
        <if test="parmName != null and parmName != ''">
            and parm_name = #{parmName}  <!-- 属性名称 -->
        </if>
        <if test="type != null and type != ''">
            and type = #{type}  <!-- 属性值类型 -->
        </if>
        <if test="address != null and address != ''">
            and address = #{address}  <!-- 属性地址 -->
        </if>
        <if test="appName != null and appName != ''">
            and app_name = #{appName}  <!-- 应用名称 -->
        </if>
        <if test="showName != null and showName != ''">
            and show_name = #{showName}  <!-- 展示名称 -->
        </if>
        <if test="showNameAlias != null and showNameAlias != ''">
            and show_name_alias = #{showNameAlias}  <!-- 展示名称别称 -->
        </if>
        <if test="showValue != null and showValue != ''">
            and show_value = #{showValue}  <!-- 展示值 -->
        </if>
        <if test="unit != null and unit != ''">
            and unit = #{unit}  <!-- 属性单位 -->
        </if>
        <if test="statusVar != null ">
            and status_var = #{statusVar}  <!-- 状态变量 -->
        </if>
        <if test="varType != null ">
            and var_type = #{varType}  <!-- 变量类型 -->
        </if>
        <if test="isSet != null ">
            and is_set = #{isSet}  <!-- 是否可写(0:只读；1:读写) -->
        </if>
        <if test="groupName != null and groupName != ''">
            and group_name = #{groupName}  <!-- 组名 -->
        </if>
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <if test="trueCpnName != null and trueCpnName != ''">
            and true_cpn_name like concat('%', #{trueCpnName}, '%')  <!-- cpn名称 -->
        </if>
        <if test="parmName != null and parmName != ''">
            and parm_name like concat('%', #{parmName}, '%')  <!-- 属性名称 -->
        </if>
        <if test="type != null and type != ''">
            and type like concat('%', #{type}, '%')  <!-- 属性值类型 -->
        </if>
        <if test="address != null and address != ''">
            and address like concat('%', #{address}, '%')  <!-- 属性地址 -->
        </if>
        <if test="appName != null and appName != ''">
            and app_name like concat('%', #{appName}, '%')  <!-- 应用名称 -->
        </if>
        <if test="showName != null and showName != ''">
            and show_name like concat('%', #{showName}, '%')  <!-- 展示名称 -->
        </if>
        <if test="showNameAlias != null and showNameAlias != ''">
            and show_name_alias like concat('%', #{showNameAlias}, '%')  <!-- 展示名称别称 -->
        </if>
        <if test="showValue != null and showValue != ''">
            and show_value like concat('%', #{showValue}, '%')  <!-- 展示值 -->
        </if>
        <if test="unit != null and unit != ''">
            and unit like concat('%', #{unit}, '%')  <!-- 属性单位 -->
        </if>
        <if test="groupName != null and groupName != ''">
            and group_name like concat('%', #{groupName}, '%')  <!-- 组名 -->
        </if>
    </sql>
</mapper>

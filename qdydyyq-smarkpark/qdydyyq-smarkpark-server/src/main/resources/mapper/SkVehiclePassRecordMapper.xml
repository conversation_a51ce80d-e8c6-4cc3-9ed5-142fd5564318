<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.smarkpark.mapper.SkVehiclePassRecordMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.smarkpark.api.entity.SkVehiclePassRecord" id="skVehiclePassRecordResult">
        <result property="id" column="id"/> <!-- 序列 -->
        <result property="plateNo" column="plate_no"/> <!-- 车牌，长度16 -->
        <result property="plateType" column="plate_type"/> <!-- 车牌类别  0:标准民用车,1:02式民用车,3:警车,4:民用车双行尾牌车,5:使馆车,6:农用车,7:摩托车,8:新能源车 -->
        <result property="passTime" column="pass_time"/> <!-- 通行时间 -->
        <result property="srcIndex" column="src_index"/> <!-- 出入口标识，最大长度64 -->
        <result property="plateColor" column="plate_color"/> <!-- 车牌颜色  0:蓝色,1:黄色,2:白色,3:黑色,4:绿色,5:民航黑色, 255:其他颜色 -->
        <result property="releaseMode" column="release_mode"/> <!-- 放行模式  0-禁止放行，1-固定车包期，2-临时车入场，3-预约车入场，10-离线出场，11-缴费出场，12-预付费出场，13-免费出场，30- 非法卡不放行，31-手动放行，32-特殊车辆放行，33-节假日放行，35-群组放行，36-遥控器开闸 -->
        <result property="releaseResult" column="release_result"/> <!-- 放行结果  0-未放行  1-正常放行  2-离线放行 -->
        <result property="releaseWay" column="release_way"/> <!-- 放行方式  10-未开闸  11-自动开闸  12-人工/人工开闸  13-遥控器开闸 -->
        <result property="vehicleOut" column="vehicle_out"/> <!-- 是否出场  0-进场  1-出场 -->
        <result property="vehicleColor" column="vehicle_color"/> <!-- 车辆颜色  0：其他颜色  1：白色  2：银色  3：灰色  4：黑色  5：红色  6：深蓝色  7：蓝色  8：黄色  9：绿色  10：棕色  11：粉色  12：紫色 -->
        <result property="vehicleType" column="vehicle_type"/> <!-- 车辆类型  0：其他车  1：小型车  2：大型车  3：摩托车 -->
        <result property="vehiclePicUri" column="vehicle_pic_uri"/> <!-- 车辆图片uri（最大长度256 -->
        <result property="plateNoPicUri" column="plate_no_pic_uri"/> <!-- 车牌图片uri（最大长度256 -->
    </resultMap>


    <sql id="selectSkVehiclePassRecordVo">
        select id,
               plate_no,
               plate_type,
               pass_time,
               src_index,
               plate_color,
               release_mode,
               release_result,
               release_way,
               vehicle_out,
               vehicle_color,
               vehicle_type,
               vehicle_pic_uri,
               plate_no_pic_uri,
               gate.gate_name
        from sk_vehicle_pass_record
        left join (select src_index as src_code, `name` as gate_name from sk_gate_list) gate on gate.src_code = src_index
    </sql>
    <!-- 查询对象List -->
    <select id="selectSkVehiclePassRecordList" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkVehiclePassRecord" resultMap="skVehiclePassRecordResult">
        <include refid="selectSkVehiclePassRecordVo"/>
        <where>
            <include refid="equal"/>
        </where>
        order by pass_time DESC
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectSkVehiclePassRecordListByLike" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkVehiclePassRecord" resultMap="skVehiclePassRecordResult">
        <include refid="selectSkVehiclePassRecordVo"/>
        <where>
            <include refid="like"/>
        </where>
        order by pass_time DESC
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectSkVehiclePassRecordById" parameterType="Integer" resultMap="skVehiclePassRecordResult">
        <include refid="selectSkVehiclePassRecordVo"/>
        where id = #{id}
    </select>


    <update id="updateSkVehiclePassRecord" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkVehiclePassRecord">
        update sk_vehicle_pass_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="plateNo != null  and plateNo != ''  ">plate_no = #{plateNo},</if>
            <if test="plateType != null  ">plate_type = #{plateType},</if>
            <if test="passTime != null  and passTime != ''  ">pass_time = #{passTime},</if>
            <if test="srcIndex != null  and srcIndex != ''  ">src_index = #{srcIndex},</if>
            <if test="plateColor != null  ">plate_color = #{plateColor},</if>
            <if test="releaseMode != null  ">release_mode = #{releaseMode},</if>
            <if test="releaseResult != null  ">release_result = #{releaseResult},</if>
            <if test="releaseWay != null  ">release_way = #{releaseWay},</if>
            <if test="vehicleOut != null  ">vehicle_out = #{vehicleOut},</if>
            <if test="vehicleColor != null  ">vehicle_color = #{vehicleColor},</if>
            <if test="vehicleType != null  ">vehicle_type = #{vehicleType},</if>
            <if test="vehiclePicUri != null  and vehiclePicUri != ''  ">vehicle_pic_uri = #{vehiclePicUri},</if>
            <if test="plateNoPicUri != null  and plateNoPicUri != ''  ">plate_no_pic_uri = #{plateNoPicUri},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSkVehiclePassRecordById" parameterType="Integer">
        delete
        from sk_vehicle_pass_record
        where id = #{id}
    </delete>

    <delete id="deleteSkVehiclePassRecordByIds" parameterType="Integer">
        delete from sk_vehicle_pass_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <!-- 新增对象 -->
    <insert id="insertSkVehiclePassRecord" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkVehiclePassRecord" useGeneratedKeys="true" keyProperty="id">
        insert into sk_vehicle_pass_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="plateNo != null  and plateNo != ''  ">plate_no,</if>
            <if test="plateType != null  ">plate_type,</if>
            <if test="passTime != null  and passTime != ''  ">pass_time,</if>
            <if test="srcIndex != null  and srcIndex != ''  ">src_index,</if>
            <if test="plateColor != null  ">plate_color,</if>
            <if test="releaseMode != null  ">release_mode,</if>
            <if test="releaseResult != null  ">release_result,</if>
            <if test="releaseWay != null  ">release_way,</if>
            <if test="vehicleOut != null  ">vehicle_out,</if>
            <if test="vehicleColor != null  ">vehicle_color,</if>
            <if test="vehicleType != null  ">vehicle_type,</if>
            <if test="vehiclePicUri != null  and vehiclePicUri != ''  ">vehicle_pic_uri,</if>
            <if test="plateNoPicUri != null  and plateNoPicUri != ''  ">plate_no_pic_uri,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="plateNo != null  and plateNo != ''  ">#{plateNo},</if>
            <if test="plateType != null  ">#{plateType},</if>
            <if test="passTime != null  and passTime != ''  ">#{passTime},</if>
            <if test="srcIndex != null  and srcIndex != ''  ">#{srcIndex},</if>
            <if test="plateColor != null  ">#{plateColor},</if>
            <if test="releaseMode != null  ">#{releaseMode},</if>
            <if test="releaseResult != null  ">#{releaseResult},</if>
            <if test="releaseWay != null  ">#{releaseWay},</if>
            <if test="vehicleOut != null  ">#{vehicleOut},</if>
            <if test="vehicleColor != null  ">#{vehicleColor},</if>
            <if test="vehicleType != null  ">#{vehicleType},</if>
            <if test="vehiclePicUri != null  and vehiclePicUri != ''  ">#{vehiclePicUri},</if>
            <if test="plateNoPicUri != null  and plateNoPicUri != ''  ">#{plateNoPicUri},</if>
        </trim>
    </insert>


    <!-- 表字段 -->
    <sql id="column">
        id,  <!-- 序列 -->
        plate_no,  <!-- 车牌，长度16 -->
        plate_type,  <!-- 车牌类别  0:标准民用车,1:02式民用车,3:警车,4:民用车双行尾牌车,5:使馆车,6:农用车,7:摩托车,8:新能源车 -->
        pass_time,  <!-- 通行时间 -->
        src_index,  <!-- 出入口标识，最大长度64 -->
        plate_color,  <!-- 车牌颜色  0:蓝色,1:黄色,2:白色,3:黑色,4:绿色,5:民航黑色, 255:其他颜色 -->
        release_mode,  <!-- 放行模式  0-禁止放行，1-固定车包期，2-临时车入场，3-预约车入场，10-离线出场，11-缴费出场，12-预付费出场，13-免费出场，30- 非法卡不放行，31-手动放行，32-特殊车辆放行，33-节假日放行，35-群组放行，36-遥控器开闸 -->
        release_result,  <!-- 放行结果  0-未放行  1-正常放行  2-离线放行 -->
        release_way,  <!-- 放行方式  10-未开闸  11-自动开闸  12-人工/人工开闸  13-遥控器开闸 -->
        vehicle_out,  <!-- 是否出场  0-进场  1-出场 -->
        vehicle_color,  <!-- 车辆颜色  0：其他颜色  1：白色  2：银色  3：灰色  4：黑色  5：红色  6：深蓝色  7：蓝色  8：黄色  9：绿色  10：棕色  11：粉色  12：紫色 -->
        vehicle_type,  <!-- 车辆类型  0：其他车  1：小型车  2：大型车  3：摩托车 -->
        vehicle_pic_uri,  <!-- 车辆图片uri（最大长度256 -->
        plate_no_pic_uri  <!-- 车牌图片uri（最大长度256 -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="id != null ">
            and id = #{id}  <!-- 序列 -->
        </if>
        <if test="plateNo != null and plateNo != ''">
            and plate_no = #{plateNo}  <!-- 车牌，长度16 -->
        </if>
        <if test="plateType != null ">
            and plate_type = #{plateType}  <!-- 车牌类别  0:标准民用车,1:02式民用车,3:警车,4:民用车双行尾牌车,5:使馆车,6:农用车,7:摩托车,8:新能源车 -->
        </if>
        <if test="passTime != null and passTime != ''">
            and pass_time = #{passTime}  <!-- 通行时间 -->
        </if>
        <if test="srcIndex != null and srcIndex != ''">
            and src_index = #{srcIndex}  <!-- 出入口标识，最大长度64 -->
        </if>
        <if test="plateColor != null ">
            and plate_color = #{plateColor}  <!-- 车牌颜色  0:蓝色,1:黄色,2:白色,3:黑色,4:绿色,5:民航黑色, 255:其他颜色 -->
        </if>
        <if test="releaseMode != null ">
            and release_mode = #{releaseMode}  <!-- 放行模式  0-禁止放行，1-固定车包期，2-临时车入场，3-预约车入场，10-离线出场，11-缴费出场，12-预付费出场，13-免费出场，30- 非法卡不放行，31-手动放行，32-特殊车辆放行，33-节假日放行，35-群组放行，36-遥控器开闸 -->
        </if>
        <if test="releaseResult != null ">
            and release_result = #{releaseResult}  <!-- 放行结果  0-未放行  1-正常放行  2-离线放行 -->
        </if>
        <if test="releaseWay != null ">
            and release_way = #{releaseWay}  <!-- 放行方式  10-未开闸  11-自动开闸  12-人工/人工开闸  13-遥控器开闸 -->
        </if>
        <if test="vehicleOut != null ">
            and vehicle_out = #{vehicleOut}  <!-- 是否出场  0-进场  1-出场 -->
        </if>
        <if test="vehicleColor != null ">
            and vehicle_color = #{vehicleColor}  <!-- 车辆颜色  0：其他颜色  1：白色  2：银色  3：灰色  4：黑色  5：红色  6：深蓝色  7：蓝色  8：黄色  9：绿色  10：棕色  11：粉色  12：紫色 -->
        </if>
        <if test="vehicleType != null ">
            and vehicle_type = #{vehicleType}  <!-- 车辆类型  0：其他车  1：小型车  2：大型车  3：摩托车 -->
        </if>
        <if test="vehiclePicUri != null and vehiclePicUri != ''">
            and vehicle_pic_uri = #{vehiclePicUri}  <!-- 车辆图片uri（最大长度256 -->
        </if>
        <if test="plateNoPicUri != null and plateNoPicUri != ''">
            and plate_no_pic_uri = #{plateNoPicUri}  <!-- 车牌图片uri（最大长度256 -->
        </if>
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <if test="plateNo != null and plateNo != ''">
            and plate_no like concat('%', #{plateNo}, '%')  <!-- 车牌，长度16 -->
        </if>
        <if test="passTime != null and passTime != ''">
            and pass_time like concat('%', #{passTime}, '%')  <!-- 通行时间 -->
        </if>
        <if test="srcIndex != null and srcIndex != ''">
            and src_index like concat('%', #{srcIndex}, '%')  <!-- 出入口标识，最大长度64 -->
        </if>
        <if test="vehiclePicUri != null and vehiclePicUri != ''">
            and vehicle_pic_uri like concat('%', #{vehiclePicUri}, '%')  <!-- 车辆图片uri（最大长度256 -->
        </if>
        <if test="plateNoPicUri != null and plateNoPicUri != ''">
            and plate_no_pic_uri like concat('%', #{plateNoPicUri}, '%')  <!-- 车牌图片uri（最大长度256 -->
        </if>
        <if test="vehicleOut != null ">
            and vehicle_out = #{vehicleOut}  <!-- 是否出场  0-进场  1-出场 -->
        </if>
        <if test="startTime != null and startTime != ''">
            and pass_time &gt;= #{startTime} <!-- 通行时间 -->
        </if>
        <if test="endTime != null and endTime != ''">
            and pass_time &lt;= #{endTime} <!-- 通行时间 -->
        </if>
        <if test="gateName != null and gateName != ''">
            and gate_name  like concat('%', #{gateName}, '%') <!-- 道闸名称 -->
        </if>
    </sql>

    <!-- 插入过车记录 -->
    <insert id="syncSkVehiclePassRecord">
        INSERT INTO sk_vehicle_pass_record (
            plate_no, plate_type, pass_time, src_index, plate_color, release_mode, release_result, release_way, vehicle_out, vehicle_color
            , vehicle_type, vehicle_pic_uri, plate_no_pic_uri
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.plateNo}, #{item.plateType}, #{item.passTime}, #{item.srcIndex}, #{item.plateColor}, #{item.releaseMode}, #{item.releaseResult}
            , #{item.releaseWay}, #{item.vehicleOut}, #{item.vehicleColor}, #{item.vehicleType}, #{item.vehiclePicUri}, #{item.plateNoPicUri}
            )
        </foreach>
    </insert>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.smarkpark.mapper.SkBuildControlDeviceMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.smarkpark.api.entity.SkBuildControlDevice" id="skBuildControlDeviceResult">
        <result property="id" column="id"/> <!-- 主键id -->
        <result property="type" column="type"/> <!-- 设备类型：1:能源系统 2:给排水 3:暖通空调  4:环境设备 -->
        <result property="deviceCode" column="device_code"/> <!-- 设备编号 -->
        <result property="deviceName" column="device_name"/> <!-- 设备名称 -->
        <result property="deviceAlias" column="device_alias"/> <!-- 设备名称 -->
        <result property="floor" column="floor"/> <!-- 楼层 -->
        <result property="runState" column="run_state"/> <!-- 运行状态 -->
        <result property="remark" column="remark"/> <!-- 备注 -->
        <result property="updateTime" column="update_time"/> <!-- 变更时间 -->
    </resultMap>


    <sql id="selectSkBuildControlDeviceVo">
        select id,
               type,
               device_code,
               device_name,
               device_alias,
               floor,
               run_state,
               remark,
               update_time
        from sk_build_control_device
    </sql>
    <!-- 查询对象List -->
    <select id="selectSkBuildControlDeviceList" parameterType="SkBuildControlDevice"
            resultMap="skBuildControlDeviceResult">
        <include refid="selectSkBuildControlDeviceVo"/>
        <where>
            <include refid="equal"/>
        </where>
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectSkBuildControlDeviceListByLike" parameterType="SkBuildControlDevice"
            resultMap="skBuildControlDeviceResult">
        <include refid="selectSkBuildControlDeviceVo"/>
        <where>
            <include refid="like"/>
        </where>
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectSkBuildControlDeviceById" parameterType="Long" resultMap="skBuildControlDeviceResult">
        <include refid="selectSkBuildControlDeviceVo"/>
        where id = #{id}
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectSkBuildControlDeviceByCode" parameterType="String" resultMap="skBuildControlDeviceResult">
        <include refid="selectSkBuildControlDeviceVo"/>
        where device_code = #{deviceCode} limit 1
    </select>

    <!-- 根据主键查询对象 -->
    <select id="buildingControlDeviceByType" resultType="java.util.Map">
        select type, count(id) as total
        from sk_build_control_device
        group by type
    </select>
    <select id="buildingControlDeviceState" resultType="java.util.Map">
        SELECT COUNT(id) as total, run_state as runState
        FROM sk_build_control_device
        where run_state is not null
        group by run_state
    </select>


    <update id="updateSkBuildControlDevice" parameterType="SkBuildControlDevice">
        update sk_build_control_device
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null  ">type = #{type},</if>
            <if test="deviceCode != null  and deviceCode != ''  ">device_code = #{deviceCode},</if>
            <if test="deviceName != null  and deviceName != ''  ">device_name = #{deviceName},</if>
            <if test="deviceAlias != null  and deviceAlias != ''  ">device_alias = #{deviceAlias},</if>
            <if test="floor != null  ">floor = #{floor},</if>
            <if test="runState != null  and runState != ''  ">run_state = #{runState},</if>
            <if test="remark != null  and remark != ''  ">remark = #{remark},</if>
            <if test="updateTime != null  ">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateDeviceStatusList">
        <foreach collection="list" item="item" separator=";">
            UPDATE sk_build_control_device
            SET run_state = #{runState}
            WHERE device_code = #{deviceCode}
        </foreach>
    </update>


    <delete id="truncateLightingDevice" >
        delete
        from sk_build_control_device sbcd
        where sbcd.type = 5
    </delete>

    <delete id="deleteSkBuildControlDeviceById" parameterType="Long">
        delete
        from sk_build_control_device
        where id = #{id}
    </delete>

    <delete id="deleteSkBuildControlDeviceByIds" parameterType="Integer">
        delete from sk_build_control_device where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <!-- 新增对象 -->
    <insert id="insertSkBuildControlDevice" parameterType="SkBuildControlDevice" useGeneratedKeys="true"
            keyProperty="id">
        insert into sk_build_control_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null  ">type,</if>
            <if test="deviceCode != null  and deviceCode != ''  ">device_code,</if>
            <if test="deviceName != null  and deviceName != ''  ">device_name,</if>
            <if test="deviceAlias != null  and deviceAlias != ''  ">device_alias,</if>
            <if test="floor != null  ">floor,</if>
            <if test="runState != null  and runState != ''  ">run_state,</if>
            <if test="remark != null  and remark != ''  ">remark,</if>
            <if test="updateTime != null  ">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null  ">#{type},</if>
            <if test="deviceCode != null  and deviceCode != ''  ">#{deviceCode},</if>
            <if test="deviceName != null  and deviceName != ''  ">#{deviceName},</if>
            <if test="deviceAlias != null  and deviceAlias != ''  ">#{deviceAlias},</if>
            <if test="floor != null  ">#{floor},</if>
            <if test="runState != null  and runState != ''  ">#{runState},</if>
            <if test="remark != null  and remark != ''  ">#{remark},</if>
            <if test="updateTime != null  ">#{updateTime},</if>
        </trim>
    </insert>

    <insert id="insertSkBuildControlDeviceList" useGeneratedKeys="true" keyProperty="id">
        insert into sk_build_control_device (type,device_code,device_name,device_alias,
                                             floor,run_state,remark) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.type}, #{item.deviceCode}, #{item.deviceName}, #{item.deviceAlias},
            #{item.floor},#{item.runState}, #{item.remark})
        </foreach>
    </insert>
    <!-- 表字段 -->
    <sql id="column">
        id,  <!-- 主键id -->
        type,  <!-- 设备类型：1:能源系统 2:给排水 3:暖通空调  4:环境设备 -->
        device_code,  <!-- 设备编号 -->
        device_name,  <!-- 设备名称 -->
        device_alias,  <!-- 设备名称 -->
        floor,  <!-- 楼层 -->
        run_state,  <!-- 运行状态 -->
        remark,  <!-- 备注 -->
        update_time  <!-- 变更时间 -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="id != null ">
            and id = #{id}  <!-- 主键id -->
        </if>
        <if test="type != null ">
            and type = #{type}  <!-- 设备类型：1:能源系统 2:给排水 3:暖通空调  4:环境设备 -->
        </if>
        <if test="deviceCode != null and deviceCode != ''">
            and device_code = #{deviceCode}  <!-- 设备编号 -->
        </if>
        <if test="deviceName != null and deviceName != ''">
            and device_name = #{deviceName}  <!-- 设备名称 -->
        </if>
        <if test="deviceAlias != null and deviceAlias != ''">
            and device_alias = #{deviceAlias}  <!-- 设备名称 -->
        </if>
        <if test="floor != null ">
            and floor = #{floor}  <!-- 楼层 -->
        </if>
        <if test="runState != null and runState != ''">
            and run_state = #{runState}  <!-- 运行状态 -->
        </if>
        <if test="remark != null and remark != ''">
            and remark = #{remark}  <!-- 备注 -->
        </if>
        <if test="updateTime != null ">
            and update_time = #{updateTime}  <!-- 变更时间 -->
        </if>
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <if test="type != null ">
            and type = #{type}  <!-- 设备类型：1:能源系统 2:给排水 3:暖通空调  4:环境设备 -->
        </if>
        <if test="deviceCode != null and deviceCode != ''">
            and device_code like concat('%', #{deviceCode}, '%')  <!-- 设备编号 -->
        </if>
        <if test="deviceName != null and deviceName != ''">
            and device_name like concat('%', #{deviceName}, '%')  <!-- 设备名称 -->
        </if>
        <if test="deviceAlias != null and deviceAlias != ''">
            and device_alias like concat('%', #{deviceAlias}, '%')  <!-- 设备名称 -->
        </if>
        <if test="runState != null and runState != ''">
            and run_state = #{runState}  <!-- 运行状态 -->
        </if>
        <if test="remark != null and remark != ''">
            and remark like concat('%', #{remark}, '%')  <!-- 备注 -->
        </if>
    </sql>
</mapper>

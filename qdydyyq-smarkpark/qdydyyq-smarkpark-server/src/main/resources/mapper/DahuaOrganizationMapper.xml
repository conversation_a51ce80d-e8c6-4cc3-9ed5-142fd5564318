<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.smarkpark.mapper.DahuaOrganizationMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.smarkpark.api.entity.DahuaOrganization" id="dahuaOrganizationResult">
        <result property="orgSn" column="org_sn"/> <!-- 唯一标识码 -->
        <result property="orgCode" column="org_code"/> <!-- 组织编码 -->
        <result property="orgName" column="org_name"/> <!-- 组织名称 -->
        <result property="orgType" column="org_type"/> <!-- 组织结构类型 -->
        <result property="province" column="province"/> <!-- 省 -->
        <result property="city" column="city"/> <!-- 市 -->
        <result property="county" column="county"/> <!-- 县 -->
        <result property="domainId" column="domain_id"/> <!-- 域id -->
        <result property="stat" column="stat"/> <!-- 状态 -->
        <result property="sort" column="sort"/> <!--  -->
        <result property="allSort" column="all_sort"/> <!-- 所有组排序织 -->
        <result property="service" column="service"/> <!-- 子系统服务编码,默认evo-thirdparty -->
        <result property="forbidBindUser" column="forbid_bind_user"/> <!-- 禁止绑定用户 ：0-可绑定用户，1-不可绑定用户 -->
        <result property="forbidBindService" column="forbid_bind_service"/> <!-- 禁止绑定用户服务 -->
        <result property="updateTime" column="update_time"/> <!-- 最后修改时间 -->
    </resultMap>


    <sql id="selectDahuaOrganizationVo">
        select org_sn,
               org_code,
               org_name,
               org_type,
               province,
               city,
               county,
               domain_id,
               stat,
               sort,
               all_sort,
               service,
               forbid_bind_user,
               forbid_bind_service,
               update_time
        from sk_dahua_organization
    </sql>
    <!-- 查询对象List -->
    <select id="selectDahuaOrganizationList" parameterType="DahuaOrganization" resultMap="dahuaOrganizationResult">
        <include refid="selectDahuaOrganizationVo"/>
        <where>
            <include refid="equal"/>
        </where>
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectDahuaOrganizationListByLike" parameterType="DahuaOrganization"
            resultMap="dahuaOrganizationResult">
        <include refid="selectDahuaOrganizationVo"/>
        <where>
            <include refid="like"/>
        </where>
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectDahuaOrganizationById" parameterType="String" resultMap="dahuaOrganizationResult">
        <include refid="selectDahuaOrganizationVo"/>
        where org_sn = #{orgSn}
    </select>


    <update id="updateDahuaOrganization" parameterType="DahuaOrganization">
        update sk_dahua_organization
        <trim prefix="SET" suffixOverrides=",">
            <if test="orgCode != null  and orgCode != ''  ">org_code = #{orgCode},</if>
            <if test="orgName != null  and orgName != ''  ">org_name = #{orgName},</if>
            <if test="orgType != null  and orgType != ''  ">org_type = #{orgType},</if>
            <if test="province != null  and province != ''  ">province = #{province},</if>
            <if test="city != null  and city != ''  ">city = #{city},</if>
            <if test="county != null  and county != ''  ">county = #{county},</if>
            <if test="domainId != null  ">domain_id = #{domainId},</if>
            <if test="stat != null  ">stat = #{stat},</if>
            <if test="sort != null  ">sort = #{sort},</if>
            <if test="allSort != null  and allSort != ''  ">all_sort = #{allSort},</if>
            <if test="service != null  and service != ''  ">service = #{service},</if>
            <if test="forbidBindUser != null  ">forbid_bind_user = #{forbidBindUser},</if>
            <if test="forbidBindService != null  and forbidBindService != ''  ">forbid_bind_service =
                #{forbidBindService},
            </if>
            <if test="updateTime != null  ">update_time = #{updateTime},</if>
        </trim>
        where org_sn = #{orgSn}
    </update>

    <delete id="deleteDahuaOrganizationById" parameterType="String">
        delete
        from sk_dahua_organization
        where org_sn = #{orgSn}
    </delete>

    <delete id="deleteDahuaOrganizationByIds" parameterType="Integer">
        delete from sk_dahua_organization where org_sn in
        <foreach item="orgSn" collection="array" open="(" separator="," close=")">
            #{orgSn}
        </foreach>
    </delete>


    <!-- 新增对象 -->
    <insert id="insertDahuaOrganization" parameterType="DahuaOrganization">
        insert into sk_dahua_organization
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orgSn != null  and orgSn != ''  ">org_sn,</if>
            <if test="orgCode != null  and orgCode != ''  ">org_code,</if>
            <if test="orgName != null  and orgName != ''  ">org_name,</if>
            <if test="orgType != null  and orgType != ''  ">org_type,</if>
            <if test="province != null  and province != ''  ">province,</if>
            <if test="city != null  and city != ''  ">city,</if>
            <if test="county != null  and county != ''  ">county,</if>
            <if test="domainId != null  ">domain_id,</if>
            <if test="stat != null  ">stat,</if>
            <if test="sort != null  ">sort,</if>
            <if test="allSort != null  and allSort != ''  ">all_sort,</if>
            <if test="service != null  and service != ''  ">service,</if>
            <if test="forbidBindUser != null  ">forbid_bind_user,</if>
            <if test="forbidBindService != null  and forbidBindService != ''  ">forbid_bind_service,</if>
            <if test="updateTime != null  ">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orgSn != null  and orgSn != ''  ">#{orgSn},</if>
            <if test="orgCode != null  and orgCode != ''  ">#{orgCode},</if>
            <if test="orgName != null  and orgName != ''  ">#{orgName},</if>
            <if test="orgType != null  and orgType != ''  ">#{orgType},</if>
            <if test="province != null  and province != ''  ">#{province},</if>
            <if test="city != null  and city != ''  ">#{city},</if>
            <if test="county != null  and county != ''  ">#{county},</if>
            <if test="domainId != null  ">#{domainId},</if>
            <if test="stat != null  ">#{stat},</if>
            <if test="sort != null  ">#{sort},</if>
            <if test="allSort != null  and allSort != ''  ">#{allSort},</if>
            <if test="service != null  and service != ''  ">#{service},</if>
            <if test="forbidBindUser != null  ">#{forbidBindUser},</if>
            <if test="forbidBindService != null  and forbidBindService != ''  ">#{forbidBindService},</if>
            <if test="updateTime != null  ">#{updateTime},</if>
        </trim>
    </insert>

    <!-- 清除大华同步-组织架构 -->
    <update id="truncateDahuaOrganization">
        TRUNCATE TABLE sk_dahua_organization;
    </update>

    <!-- 批量新增大华同步-组织架构 -->
    <insert id="insertDahuaOrganizationList">
        INSERT INTO sk_dahua_organization (org_sn, org_code, org_name, org_type, parent_code,
        province, city, county, domain_id, stat, sort, all_sort, service, forbid_bind_user,
        forbid_bind_service, update_time) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.orgSn}, #{item.orgCode}, #{item.orgName}, #{item.orgType}, #{item.parentCode},
            #{item.province}, #{item.city}, #{item.county}, #{item.domainId}, #{item.stat},
            #{item.sort}, #{item.sort}, #{item.allSort},#{item.forbidBindUser},
            #{item.forbidBindService}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 表字段 -->
    <sql id="column">
        org_sn,  <!-- 唯一标识码 -->
        org_code,  <!-- 组织编码 -->
        org_name,  <!-- 组织名称 -->
        org_type,  <!-- 组织结构类型 -->
        province,  <!-- 省 -->
        city,  <!-- 市 -->
        county,  <!-- 县 -->
        domain_id,  <!-- 域id -->
        stat,  <!-- 状态 -->
        sort,  <!--  -->
        all_sort,  <!-- 所有组排序织 -->
        service,  <!-- 子系统服务编码,默认evo-thirdparty -->
        forbid_bind_user,  <!-- 禁止绑定用户 ：0-可绑定用户，1-不可绑定用户 -->
        forbid_bind_service,  <!-- 禁止绑定用户服务 -->
        update_time  <!-- 最后修改时间 -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="orgSn != null and orgSn != ''">
            and org_sn = #{orgSn}  <!-- 唯一标识码 -->
        </if>
        <if test="orgCode != null and orgCode != ''">
            and org_code = #{orgCode}  <!-- 组织编码 -->
        </if>
        <if test="orgName != null and orgName != ''">
            and org_name = #{orgName}  <!-- 组织名称 -->
        </if>
        <if test="orgType != null and orgType != ''">
            and org_type = #{orgType}  <!-- 组织结构类型 -->
        </if>
        <if test="province != null and province != ''">
            and province = #{province}  <!-- 省 -->
        </if>
        <if test="city != null and city != ''">
            and city = #{city}  <!-- 市 -->
        </if>
        <if test="county != null and county != ''">
            and county = #{county}  <!-- 县 -->
        </if>
        <if test="domainId != null ">
            and domain_id = #{domainId}  <!-- 域id -->
        </if>
        <if test="stat != null ">
            and stat = #{stat}  <!-- 状态 -->
        </if>
        <if test="sort != null ">
            and sort = #{sort}  <!--  -->
        </if>
        <if test="allSort != null and allSort != ''">
            and all_sort = #{allSort}  <!-- 所有组排序织 -->
        </if>
        <if test="service != null and service != ''">
            and service = #{service}  <!-- 子系统服务编码,默认evo-thirdparty -->
        </if>
        <if test="forbidBindUser != null ">
            and forbid_bind_user = #{forbidBindUser}  <!-- 禁止绑定用户 ：0-可绑定用户，1-不可绑定用户 -->
        </if>
        <if test="forbidBindService != null and forbidBindService != ''">
            and forbid_bind_service = #{forbidBindService}  <!-- 禁止绑定用户服务 -->
        </if>
        <if test="updateTime != null ">
            and update_time = #{updateTime}  <!-- 最后修改时间 -->
        </if>
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <if test="orgSn != null and orgSn != ''">
            and org_sn like concat('%', #{orgSn}, '%')  <!-- 唯一标识码 -->
        </if>
        <if test="orgCode != null and orgCode != ''">
            and org_code like concat('%', #{orgCode}, '%')  <!-- 组织编码 -->
        </if>
        <if test="orgName != null and orgName != ''">
            and org_name like concat('%', #{orgName}, '%')  <!-- 组织名称 -->
        </if>
        <if test="orgType != null and orgType != ''">
            and org_type like concat('%', #{orgType}, '%')  <!-- 组织结构类型 -->
        </if>
        <if test="province != null and province != ''">
            and province like concat('%', #{province}, '%')  <!-- 省 -->
        </if>
        <if test="city != null and city != ''">
            and city like concat('%', #{city}, '%')  <!-- 市 -->
        </if>
        <if test="county != null and county != ''">
            and county like concat('%', #{county}, '%')  <!-- 县 -->
        </if>
        <if test="allSort != null and allSort != ''">
            and all_sort like concat('%', #{allSort}, '%')  <!-- 所有组排序织 -->
        </if>
        <if test="service != null and service != ''">
            and service like concat('%', #{service}, '%')  <!-- 子系统服务编码,默认evo-thirdparty -->
        </if>
        <if test="forbidBindService != null and forbidBindService != ''">
            and forbid_bind_service like concat('%', #{forbidBindService}, '%')  <!-- 禁止绑定用户服务 -->
        </if>
    </sql>
</mapper>

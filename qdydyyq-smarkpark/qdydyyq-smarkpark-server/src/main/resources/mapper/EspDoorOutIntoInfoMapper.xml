<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.smarkpark.mapper.EspDoorOutIntoInfoMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.smarkpark.api.entity.EspDoorOutIntoInfo" id="espDoorOutIntoInfoResult">
        <result property="id" column="id"/> <!-- 出入数据ID -->
        <result property="doorNum" column="door_num"/> <!-- 门禁编码 -->
        <result property="doorName" column="door_name"/> <!-- 门禁名称 -->
        <result property="deviceName" column="device_name"/> <!-- 设备名称 -->
        <result property="staffId" column="staff_id"/> <!-- 人员ID -->
        <result property="staffCode" column="staff_code"/> <!-- 人员编号 -->
        <result property="staffName" column="staff_name"/> <!-- 人员名称 -->
        <result property="staffType" column="staff_type"/> <!-- 身份类型编码 -->
        <result property="staffTypeName" column="staff_type_name"/> <!-- 身份类型 -->
        <result property="cardNo" column="card_no"/> <!-- 卡号 -->
        <result property="orgName" column="org_name"/> <!-- 组织名称 -->
        <result property="passType" column="pass_type"/> <!-- 通行类型编码 -->
        <result property="passTypeName" column="pass_type_name"/> <!-- 通行类型 -->
        <result property="passMode" column="pass_mode"/> <!-- 验证方式编码 -->
        <result property="passModeName" column="pass_mode_name"/> <!-- 验证方式 -->
        <result property="readType" column="read_type"/> <!-- 读卡器类型编码 -->
        <result property="readTypeName" column="read_type_name"/> <!-- 读卡器类型 -->
        <result property="readNo" column="read_no"/> <!-- 读卡器编码 -->
        <result property="temperature" column="temperature"/> <!-- 温度类型编码 -->
        <result property="temperatureType" column="temperature_type"/> <!-- 温度类型 -->
        <result property="facePhotoAddr" column="face_photo_addr"/> <!-- 人脸照片地址 -->
        <result property="monitorPhotoAddr" column="monitor_photo_addr"/> <!-- 监控照片地址 -->
        <result property="time" column="time"/> <!-- 通行时间 -->
        <result property="createdTime" column="created_time"/> <!-- 创建时间 -->
    </resultMap>


    <sql id="selectEspDoorOutIntoInfoVo">
        select id,
               door_num,
               door_name,
               device_name,
               staff_id,
               staff_code,
               staff_name,
               staff_type,
               staff_type_name,
               card_no,
               org_name,
               pass_type,
               pass_type_name,
               pass_mode,
               pass_mode_name,
               read_type,
               read_type_name,
               read_no,
               temperature,
               temperature_type,
               face_photo_addr,
               monitor_photo_addr,
               `time`,
               created_time
        from esp_door_out_into_info
    </sql>
    <!-- 查询对象List -->
    <select id="selectEspDoorOutIntoInfoList" parameterType="EspDoorOutIntoInfo" resultMap="espDoorOutIntoInfoResult">
        <include refid="selectEspDoorOutIntoInfoVo"/>
        <where>
            <include refid="equal"/>
        </where>
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectEspDoorOutIntoInfoListByLike" parameterType="EspDoorOutIntoInfo" resultMap="espDoorOutIntoInfoResult">
        <include refid="selectEspDoorOutIntoInfoVo"/>
        <where>
            <include refid="like"/>

            <if test="timeSearchStart != null and timeSearchStart != ''">
                and `time` &gt; #{timeSearchStart}
            </if>
            <if test="timeSearchEnd != null and timeSearchEnd != ''">
                and `time` &lt; #{timeSearchEnd}
            </if>
        </where>
        order by `time` desc
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectEspDoorOutIntoInfoById" parameterType="String" resultMap="espDoorOutIntoInfoResult">
        <include refid="selectEspDoorOutIntoInfoVo"/>
        where id = #{id}
    </select>


    <update id="updateEspDoorOutIntoInfo" parameterType="EspDoorOutIntoInfo">
        update esp_door_out_into_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="doorNum != null ">door_num = #{doorNum},</if>
            <if test="doorName != null  and doorName != ''  ">door_name = #{doorName},</if>
            <if test="deviceName != null  and deviceName != ''  ">device_name = #{deviceName},</if>
            <if test="staffId != null  and staffId != ''  ">staff_id = #{staffId},</if>
            <if test="staffCode != null  and staffCode != ''  ">staff_code = #{staffCode},</if>
            <if test="staffName != null  and staffName != ''  ">staff_name = #{staffName},</if>
            <if test="staffType != null ">staff_type = #{staffType},</if>
            <if test="staffTypeName != null  and staffTypeName != ''  ">staff_type_name = #{staffTypeName},</if>
            <if test="cardNo != null  and cardNo != ''  ">card_no = #{cardNo},</if>
            <if test="orgName != null  and orgName != ''  ">org_name = #{orgName},</if>
            <if test="passType != null ">pass_type = #{passType},</if>
            <if test="passTypeName != null  and passTypeName != ''  ">pass_type_name = #{passTypeName},</if>
            <if test="passMode != null  and passMode != ''  ">pass_mode = #{passMode},</if>
            <if test="passModeName != null  and passModeName != ''  ">pass_mode_name = #{passModeName},</if>
            <if test="readType != null ">read_type = #{readType},</if>
            <if test="readTypeName != null  and readTypeName != ''  ">read_type_name = #{readTypeName},</if>
            <if test="readNo != null ">read_no = #{readNo},</if>
            <if test="temperature != null  and temperature != ''  ">temperature = #{temperature},</if>
            <if test="temperatureType != null  and temperatureType != ''  ">temperature_type = #{temperatureType},</if>
            <if test="facePhotoAddr != null  and facePhotoAddr != ''  ">face_photo_addr = #{facePhotoAddr},</if>
            <if test="monitorPhotoAddr != null  and monitorPhotoAddr != ''  ">monitor_photo_addr = #{monitorPhotoAddr},</if>
            <if test="time != null  and time != ''  ">time = #{time},</if>
            <if test="createdTime != null  and createdTime != ''  ">created_time = #{createdTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEspDoorOutIntoInfoById" parameterType="String">
        delete
        from esp_door_out_into_info
        where id = #{id}
    </delete>

    <delete id="deleteEspDoorOutIntoInfoByIds" parameterType="Integer">
        delete from esp_door_out_into_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <!-- 新增对象 -->
    <insert id="insertEspDoorOutIntoInfo" parameterType="EspDoorOutIntoInfo">
        insert into esp_door_out_into_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null  and id != ''  ">id,</if>
            <if test="doorNum != null ">door_num,</if>
            <if test="doorName != null  and doorName != ''  ">door_name,</if>
            <if test="deviceName != null  and deviceName != ''  ">device_name,</if>
            <if test="staffId != null  and staffId != ''  ">staff_id,</if>
            <if test="staffCode != null  and staffCode != ''  ">staff_code,</if>
            <if test="staffName != null  and staffName != ''  ">staff_name,</if>
            <if test="staffType != null ">staff_type,</if>
            <if test="staffTypeName != null  and staffTypeName != ''  ">staff_type_name,</if>
            <if test="cardNo != null  and cardNo != ''  ">card_no,</if>
            <if test="orgName != null  and orgName != ''  ">org_name,</if>
            <if test="passType != null ">pass_type,</if>
            <if test="passTypeName != null  and passTypeName != ''  ">pass_type_name,</if>
            <if test="passMode != null  and passMode != ''  ">pass_mode,</if>
            <if test="passModeName != null  and passModeName != ''  ">pass_mode_name,</if>
            <if test="readType != null ">read_type,</if>
            <if test="readTypeName != null  and readTypeName != ''  ">read_type_name,</if>
            <if test="readNo != null ">read_no,</if>
            <if test="temperature != null  and temperature != ''  ">temperature,</if>
            <if test="temperatureType != null  and temperatureType != ''  ">temperature_type,</if>
            <if test="facePhotoAddr != null  and facePhotoAddr != ''  ">face_photo_addr,</if>
            <if test="monitorPhotoAddr != null  and monitorPhotoAddr != ''  ">monitor_photo_addr,</if>
            <if test="time != null  and time != ''  ">time,</if>
            <if test="createdTime != null  and createdTime != ''  ">created_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null  and id != ''  ">#{id},</if>
            <if test="doorNum != null ">#{doorNum},</if>
            <if test="doorName != null  and doorName != ''  ">#{doorName},</if>
            <if test="deviceName != null  and deviceName != ''  ">#{deviceName},</if>
            <if test="staffId != null  and staffId != ''  ">#{staffId},</if>
            <if test="staffCode != null  and staffCode != ''  ">#{staffCode},</if>
            <if test="staffName != null  and staffName != ''  ">#{staffName},</if>
            <if test="staffType != null ">#{staffType},</if>
            <if test="staffTypeName != null  and staffTypeName != ''  ">#{staffTypeName},</if>
            <if test="cardNo != null  and cardNo != ''  ">#{cardNo},</if>
            <if test="orgName != null  and orgName != ''  ">#{orgName},</if>
            <if test="passType != null ">#{passType},</if>
            <if test="passTypeName != null  and passTypeName != ''  ">#{passTypeName},</if>
            <if test="passMode != null  and passMode != ''  ">#{passMode},</if>
            <if test="passModeName != null  and passModeName != ''  ">#{passModeName},</if>
            <if test="readType != null ">#{readType},</if>
            <if test="readTypeName != null  and readTypeName != ''  ">#{readTypeName},</if>
            <if test="readNo != null ">#{readNo},</if>
            <if test="temperature != null  and temperature != ''  ">#{temperature},</if>
            <if test="temperatureType != null  and temperatureType != ''  ">#{temperatureType},</if>
            <if test="facePhotoAddr != null  and facePhotoAddr != ''  ">#{facePhotoAddr},</if>
            <if test="monitorPhotoAddr != null  and monitorPhotoAddr != ''  ">#{monitorPhotoAddr},</if>
            <if test="time != null  and time != ''  ">#{time},</if>
            <if test="createdTime != null  and createdTime != ''  ">#{createdTime},</if>
        </trim>
    </insert>


    <!-- 表字段 -->
    <sql id="column">
        id,  <!-- 出入数据id -->
        door_num,  <!-- 门禁编码 -->
        door_name,  <!-- 门禁名称 -->
        device_name,  <!-- 设备名称 -->
        staff_id,  <!-- 人员ID -->
        staff_code,  <!-- 人员编号 -->
        staff_name,  <!-- 人员名称 -->
        staff_type,  <!-- 身份类型编码 -->
        staff_type_name,  <!-- 身份类型 -->
        card_no,  <!-- 卡号 -->
        org_name,  <!-- 组织名称 -->
        pass_type,  <!-- 通行类型编码 -->
        pass_type_name,  <!-- 通行类型 -->
        pass_mode,  <!-- 验证方式编码 -->
        pass_mode_name,  <!-- 验证方式 -->
        read_type,  <!-- 读卡器类型编码 -->
        read_type_name,  <!-- 读卡器类型 -->
        read_no,  <!-- 读卡器编码 -->
        temperature,  <!-- 温度类型编码 -->
        temperature_type,  <!-- 温度类型 -->
        face_photo_addr,  <!-- 人脸照片地址 -->
        monitor_photo_addr,  <!-- 监控照片地址 -->
        `time`,  <!-- 通行时间 -->
        created_time  <!-- 创建时间 -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <!-- 出入数据ID -->
        <if test="id != null  and id != ''  ">and id = #{id}</if>
        <!-- 门禁编码 -->
        <if test="doorNum != null ">and door_num = #{doorNum}</if>
        <!-- 门禁名称 -->
        <if test="doorName != null  and doorName != ''  ">and door_name = #{doorName}</if>
        <!-- 设备名称 -->
        <if test="deviceName != null  and deviceName != ''  ">and device_name = #{deviceName}</if>
        <!-- 人员ID -->
        <if test="staffId != null  and staffId != ''  ">and staff_id = #{staffId}</if>
        <!-- 人员编号 -->
        <if test="staffCode != null  and staffCode != ''  ">and staff_code = #{staffCode}</if>
        <!-- 人员名称 -->
        <if test="staffName != null  and staffName != ''  ">and staff_name = #{staffName}</if>
        <!-- 身份类型编码 -->
        <if test="staffType != null ">and staff_type = #{staffType}</if>
        <!-- 身份类型 -->
        <if test="staffTypeName != null  and staffTypeName != ''  ">and staff_type_name = #{staffTypeName}</if>
        <!-- 卡号 -->
        <if test="cardNo != null  and cardNo != ''  ">and card_no = #{cardNo}</if>
        <!-- 组织名称 -->
        <if test="orgName != null  and orgName != ''  ">and org_name = #{orgName}</if>
        <!-- 通行类型编码 -->
        <if test="passType != null ">and pass_type = #{passType}</if>
        <!-- 通行类型 -->
        <if test="passTypeName != null  and passTypeName != ''  ">and pass_type_name = #{passTypeName}</if>
        <!-- 验证方式编码 -->
        <if test="passMode != null  and passMode != ''  ">and pass_mode = #{passMode}</if>
        <!-- 验证方式 -->
        <if test="passModeName != null  and passModeName != ''  ">and pass_mode_name = #{passModeName}</if>
        <!-- 读卡器类型编码 -->
        <if test="readType != null ">and read_type = #{readType}</if>
        <!-- 读卡器类型 -->
        <if test="readTypeName != null  and readTypeName != ''  ">and read_type_name = #{readTypeName}</if>
        <!-- 读卡器编码 -->
        <if test="readNo != null ">and read_no = #{readNo}</if>
        <!-- 温度类型编码 -->
        <if test="temperature != null  and temperature != ''  ">and temperature = #{temperature}</if>
        <!-- 温度类型 -->
        <if test="temperatureType != null  and temperatureType != ''  ">and temperature_type = #{temperatureType}</if>
        <!-- 人脸照片地址 -->
        <if test="facePhotoAddr != null  and facePhotoAddr != ''  ">and face_photo_addr = #{facePhotoAddr}</if>
        <!-- 监控照片地址 -->
        <if test="monitorPhotoAddr != null  and monitorPhotoAddr != ''  ">and monitor_photo_addr = #{monitorPhotoAddr}</if>
        <!-- 通行时间 -->
        <if test="time != null  and time != ''  ">and time = #{time}</if>
        <!-- 创建时间 -->
        <if test="createdTime != null  and createdTime != ''  ">and created_time = #{createdTime}</if>
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <!-- 出入数据ID -->
        <if test="id != null  and id != ''  ">and id like concat('%', #{id}, '%')</if>
        <!-- 门禁编码 -->
        <if test="doorNum != null ">and door_num like concat('%', #{doorNum}, '%')</if>
        <!-- 门禁名称 -->
        <if test="doorName != null  and doorName != ''  ">and door_name like concat('%', #{doorName}, '%')</if>
        <!-- 设备名称 -->
        <if test="deviceName != null  and deviceName != ''  ">and device_name like concat('%', #{deviceName}, '%')</if>
        <!-- 人员ID -->
        <if test="staffId != null  and staffId != ''  ">and staff_id like concat('%', #{staffId}, '%')</if>
        <!-- 人员编号 -->
        <if test="staffCode != null  and staffCode != ''  ">and staff_code like concat('%', #{staffCode}, '%')</if>
        <!-- 人员名称 -->
        <if test="staffName != null  and staffName != ''  ">and staff_name like concat('%', #{staffName}, '%')</if>
        <!-- 身份类型编码 -->
        <if test="staffType != null ">and staff_type like concat('%', #{staffType}, '%')</if>
        <!-- 身份类型 -->
        <if test="staffTypeName != null  and staffTypeName != ''  ">and staff_type_name like concat('%', #{staffTypeName}, '%')</if>
        <!-- 卡号 -->
        <if test="cardNo != null  and cardNo != ''  ">and card_no like concat('%', #{cardNo}, '%')</if>
        <!-- 组织名称 -->
        <if test="orgName != null  and orgName != ''  ">and org_name like concat('%', #{orgName}, '%')</if>
        <!-- 通行类型编码 -->
        <if test="passType != null ">and pass_type like concat('%', #{passType}, '%')</if>
        <!-- 通行类型 -->
        <if test="passTypeName != null  and passTypeName != ''  ">and pass_type_name like concat('%', #{passTypeName}, '%')</if>
        <!-- 验证方式编码 -->
        <if test="passMode != null  and passMode != ''  ">and pass_mode like concat('%', #{passMode}, '%')</if>
        <!-- 验证方式 -->
        <if test="passModeName != null  and passModeName != ''  ">and pass_mode_name like concat('%', #{passModeName}, '%')</if>
        <!-- 读卡器类型编码 -->
        <if test="readType != null ">and read_type like concat('%', #{readType}, '%')</if>
        <!-- 读卡器类型 -->
        <if test="readTypeName != null  and readTypeName != ''  ">and read_type_name like concat('%', #{readTypeName}, '%')</if>
        <!-- 读卡器编码 -->
        <if test="readNo != null ">and read_no like concat('%', #{readNo}, '%')</if>
        <!-- 温度类型编码 -->
        <if test="temperature != null  and temperature != ''  ">and temperature like concat('%', #{temperature}, '%')</if>
        <!-- 温度类型 -->
        <if test="temperatureType != null  and temperatureType != ''  ">and temperature_type like concat('%', #{temperatureType}, '%')</if>
        <!-- 人脸照片地址 -->
        <if test="facePhotoAddr != null  and facePhotoAddr != ''  ">and face_photo_addr like concat('%', #{facePhotoAddr}, '%')</if>
        <!-- 监控照片地址 -->
        <if test="monitorPhotoAddr != null  and monitorPhotoAddr != ''  ">and monitor_photo_addr like concat('%', #{monitorPhotoAddr}, '%')</if>
        <!-- 通行时间 -->
        <if test="time != null  and time != ''  ">and time like concat('%', #{time}, '%')</if>
        <!-- 创建时间 -->
        <if test="createdTime != null  and createdTime != ''  ">and created_time like concat('%', #{createdTime}, '%')</if>
    </sql>
</mapper>

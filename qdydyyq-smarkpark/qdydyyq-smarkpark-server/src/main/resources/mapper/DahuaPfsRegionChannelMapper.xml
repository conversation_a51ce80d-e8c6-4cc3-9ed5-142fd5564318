<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.smarkpark.mapper.DahuaPfsRegionChannelMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.smarkpark.api.entity.DahuaPfsRegionChannel" id="dahuaPfsRegionChannelResult">
        <result property="id" column="id"/> <!-- 主键id -->
        <result property="regionCode" column="region_code"/> <!-- 区域编码 -->
        <result property="channelCode" column="channel_code"/> <!-- 通道编码 -->
    </resultMap>


    <sql id="selectDahuaPfsRegionChannelVo">
        select id, region_code, channel_code
        from sk_dahua_pfs_region_channel
    </sql>
    <!-- 查询对象List -->
    <select id="selectDahuaPfsRegionChannelList" parameterType="DahuaPfsRegionChannel"
            resultMap="dahuaPfsRegionChannelResult">
        <include refid="selectDahuaPfsRegionChannelVo"/>
        <where>
            <include refid="equal"/>
        </where>
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectDahuaPfsRegionChannelListByLike" parameterType="DahuaPfsRegionChannel"
            resultMap="dahuaPfsRegionChannelResult">
        <include refid="selectDahuaPfsRegionChannelVo"/>
        <where>
            <include refid="like"/>
        </where>
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectDahuaPfsRegionChannelById" parameterType="Long" resultMap="dahuaPfsRegionChannelResult">
        <include refid="selectDahuaPfsRegionChannelVo"/>
        where id = #{id}
    </select>

    <!-- 清空大华同步-监控设备表 -->
    <update id="truncateDahuaPfsRegionChannel">
        TRUNCATE TABLE sk_dahua_pfs_region_channel;
    </update>

    <!-- 批量新增大华同步-监控设备表 -->
    <insert id="insertDahuaPfsRegionChannelList">
        INSERT INTO sk_dahua_pfs_region_channel (id, region_code,channel_code) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id}, #{item.regionCode}, #{item.channelCode})
        </foreach>
    </insert>

    <update id="updateDahuaPfsRegionChannel" parameterType="DahuaPfsRegionChannel">
        update sk_dahua_pfs_region_channel
        <trim prefix="SET" suffixOverrides=",">
            <if test="regionCode != null  and regionCode != ''  ">region_code = #{regionCode},</if>
            <if test="channelCode != null  and channelCode != ''  ">channel_code = #{channelCode},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDahuaPfsRegionChannelById" parameterType="Long">
        delete
        from sk_dahua_pfs_region_channel
        where id = #{id}
    </delete>

    <delete id="deleteDahuaPfsRegionChannelByIds" parameterType="Integer">
        delete from sk_dahua_pfs_region_channel where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 新增对象 -->
    <insert id="insertDahuaPfsRegionChannel" parameterType="DahuaPfsRegionChannel" useGeneratedKeys="true"
            keyProperty="id">
        insert into sk_dahua_pfs_region_channel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="regionCode != null  and regionCode != ''  ">region_code,</if>
            <if test="channelCode != null  and channelCode != ''  ">channel_code,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="regionCode != null  and regionCode != ''  ">#{regionCode},</if>
            <if test="channelCode != null  and channelCode != ''  ">#{channelCode},</if>
        </trim>
    </insert>


    <!-- 表字段 -->
    <sql id="column">
        id,  <!-- 主键id -->
        region_code,  <!-- 区域编码 -->
        channel_code  <!-- 通道编码 -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="id != null ">
            and id = #{id}  <!-- 主键id -->
        </if>
        <if test="regionCode != null and regionCode != ''">
            and region_code = #{regionCode}  <!-- 区域编码 -->
        </if>
        <if test="channelCode != null and channelCode != ''">
            and channel_code = #{channelCode}  <!-- 通道编码 -->
        </if>
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <if test="regionCode != null and regionCode != ''">
            and region_code like concat('%', #{regionCode}, '%')  <!-- 区域编码 -->
        </if>
        <if test="channelCode != null and channelCode != ''">
            and channel_code like concat('%', #{channelCode}, '%')  <!-- 通道编码 -->
        </if>
    </sql>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.smarkpark.mapper.ZhoujieDeviceMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.smarkpark.api.entity.ZhoujieDevice" id="zhoujieDeviceResult">
        <result property="id" column="id"/> <!--  -->
        <result property="deviceId" column="device_id"/> <!-- 设备id -->
        <result property="deviceType" column="device_type"/> <!-- 设备类型 -->
        <result property="deviceTypeName" column="device_type_name"/> <!-- 设备类型名称 -->
        <result property="deviceName" column="device_name"/> <!-- 设备名称 -->
        <result property="deviceStatus" column="device_status"/> <!-- 设备状态(0-未知状态,1-在线状态,2-离线状态,3-报警状态,4-故障状态) -->
        <result property="armingStatus" column="arming_status"/> <!-- 布防状态(0-未知状态,1-外出布防,2-在家布防,3-撤防状态,4-布防延时) -->
        <result property="version" column="version"/> <!-- 设备版本号 -->
    </resultMap>


    <sql id="selectZhoujieDeviceVo">
        select id,
               device_id,
               device_type,
               device_type_name,
               device_name,
               device_status,
               arming_status,
               version
        from sk_zhoujie_device
    </sql>
    <!-- 查询对象List -->
    <select id="selectZhoujieDeviceList" parameterType="ZhoujieDevice" resultMap="zhoujieDeviceResult">
        <include refid="selectZhoujieDeviceVo"/>
        <where>
            <include refid="equal"/>
        </where>
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectZhoujieDeviceListByLike" parameterType="ZhoujieDevice" resultMap="zhoujieDeviceResult">
        <include refid="selectZhoujieDeviceVo"/>
        <where>
            <include refid="like"/>
        </where>
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectZhoujieDeviceById" parameterType="Long" resultMap="zhoujieDeviceResult">
        <include refid="selectZhoujieDeviceVo"/>
        where id = #{id}
    </select>


    <update id="updateZhoujieDevice" parameterType="ZhoujieDevice">
        update sk_zhoujie_device
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceId != null  ">device_id = #{deviceId},</if>
            <if test="deviceType != null  ">device_type = #{deviceType},</if>
            <if test="deviceTypeName != null  and deviceTypeName != ''  ">device_type_name = #{deviceTypeName},</if>
            <if test="deviceName != null  and deviceName != ''  ">device_name = #{deviceName},</if>
            <if test="deviceStatus != null  ">device_status = #{deviceStatus},</if>
            <if test="armingStatus != null  ">arming_status = #{armingStatus},</if>
            <if test="version != null  ">version = #{version},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteZhoujieDeviceById" parameterType="Long">
        delete
        from sk_zhoujie_device
        where id = #{id}
    </delete>

    <delete id="deleteZhoujieDeviceByIds" parameterType="Integer">
        delete from sk_zhoujie_device where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <!-- 新增对象 -->
    <insert id="insertZhoujieDevice" parameterType="ZhoujieDevice" useGeneratedKeys="true" keyProperty="id">
        insert into sk_zhoujie_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceId != null  ">device_id,</if>
            <if test="deviceType != null  ">device_type,</if>
            <if test="deviceTypeName != null  and deviceTypeName != ''  ">device_type_name,</if>
            <if test="deviceName != null  and deviceName != ''  ">device_name,</if>
            <if test="deviceStatus != null  ">device_status,</if>
            <if test="armingStatus != null  ">arming_status,</if>
            <if test="version != null  ">version,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceId != null  ">#{deviceId},</if>
            <if test="deviceType != null  ">#{deviceType},</if>
            <if test="deviceTypeName != null  and deviceTypeName != ''  ">#{deviceTypeName},</if>
            <if test="deviceName != null  and deviceName != ''  ">#{deviceName},</if>
            <if test="deviceStatus != null  ">#{deviceStatus},</if>
            <if test="armingStatus != null  ">#{armingStatus},</if>
            <if test="version != null  ">#{version},</if>
        </trim>
    </insert>


    <!-- 表字段 -->
    <sql id="column">
        id,  <!--  -->
        device_id,  <!-- 设备id -->
        device_type,  <!-- 设备类型 -->
        device_type_name,  <!-- 设备类型名称 -->
        device_name,  <!-- 设备名称 -->
        device_status,  <!-- 设备状态(0-未知状态,1-在线状态,2-离线状态,3-报警状态,4-故障状态) -->
        arming_status,  <!-- 布防状态(0-未知状态,1-外出布防,2-在家布防,3-撤防状态,4-布防延时) -->
        version  <!-- 设备版本号 -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="id != null ">
            and id = #{id}  <!--  -->
        </if>
        <if test="deviceId != null ">
            and device_id = #{deviceId}  <!-- 设备id -->
        </if>
        <if test="deviceType != null ">
            and device_type = #{deviceType}  <!-- 设备类型 -->
        </if>
        <if test="deviceTypeName != null and deviceTypeName != ''">
            and device_type_name = #{deviceTypeName}  <!-- 设备类型名称 -->
        </if>
        <if test="deviceName != null and deviceName != ''">
            and device_name = #{deviceName}  <!-- 设备名称 -->
        </if>
        <if test="deviceStatus != null ">
            and device_status = #{deviceStatus}  <!-- 设备状态(0-未知状态,1-在线状态,2-离线状态,3-报警状态,4-故障状态) -->
        </if>
        <if test="armingStatus != null ">
            and arming_status = #{armingStatus}  <!-- 布防状态(0-未知状态,1-外出布防,2-在家布防,3-撤防状态,4-布防延时) -->
        </if>
        <if test="version != null ">
            and version = #{version}  <!-- 设备版本号 -->
        </if>
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <if test="deviceTypeName != null and deviceTypeName != ''">
            and device_type_name like concat('%', #{deviceTypeName}, '%')  <!-- 设备类型名称 -->
        </if>
        <if test="deviceName != null and deviceName != ''">
            and device_name like concat('%', #{deviceName}, '%')  <!-- 设备名称 -->
        </if>
        <if test="deviceStatus != null ">
            and device_status = #{deviceStatus}  <!-- 设备状态(0-未知状态,1-在线状态,2-离线状态,3-报警状态,4-故障状态) -->
        </if>
        <if test="armingStatus != null ">
            and arming_status = #{armingStatus}  <!-- 布防状态(0-未知状态,1-外出布防,2-在家布防,3-撤防状态,4-布防延时) -->
        </if>
    </sql>
</mapper>

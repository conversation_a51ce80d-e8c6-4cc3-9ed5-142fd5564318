<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.smarkpark.mapper.SkBlacklistPlanMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.smarkpark.api.entity.SkBlacklistPlan" id="skBlacklistPlanResult">
        <result property="planIndexCode" column="plan_index_code"/> <!-- 识别计划的唯一标识 -->
        <result property="faceGroupIndexCode" column="face_group_index_code"/> <!-- 人脸分组唯一标识 -->
        <result property="name" column="name"/> <!-- 识别计划/人脸分组名称,1~32个字符；不能包含 ’ / \ : * ? " < > -->
        <result property="cameraIndexCodes" column="camera_index_codes"/> <!-- 唯一标识的集合 -->
        <result property="description" column="description"/> <!-- 识别计划描述，1~128个字符 -->
        <result property="threshold" column="threshold"/> <!-- 识别计划的阈值，重点目标是指相似度超过该值时报警，范围[1, 100) -->
        <result property="createTime" column="create_time"/> <!-- 创建时间 -->
        <result property="createBy" column="create_by"/> <!-- 创建人 -->
        <result property="createByName" column="create_by_name"/> <!-- 创建人姓名 -->
        <result property="updateTime" column="update_time"/> <!-- 修改时间 -->
        <result property="updateBy" column="update_by"/> <!-- 修改人 -->
        <result property="updateByName" column="update_by_name"/> <!-- 修改人姓名 -->
        <result property="delFlag" column="del_flag"/> <!-- 删除标记 1有效 0无效 -->
    </resultMap>


    <sql id="selectSkBlacklistPlanVo">
        select plan_index_code,
               face_group_index_code,
               name,
               camera_index_codes,
               description,
               threshold,
               create_time,
               create_by,
               create_by_name,
               update_time,
               update_by,
               update_by_name,
               del_flag
        from sk_blacklist_plan
    </sql>
    <!-- 查询对象List -->
    <select id="selectSkBlacklistPlanList" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkBlacklistPlan" resultMap="skBlacklistPlanResult">
        <include refid="selectSkBlacklistPlanVo"/>
        <where>
            <include refid="equal"/>
        </where>
        order by create_by desc
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectSkBlacklistPlanListByLike" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkBlacklistPlan" resultMap="skBlacklistPlanResult">
        <include refid="selectSkBlacklistPlanVo"/>
        <where>
            <include refid="like"/>
            and del_flag = '1'
        </where>
        order by create_by desc
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectSkBlacklistPlanById" parameterType="String" resultMap="skBlacklistPlanResult">
        <include refid="selectSkBlacklistPlanVo"/>
        where plan_index_code = #{planIndexCode}
    </select>


    <update id="updateSkBlacklistPlan" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkBlacklistPlan">
        update sk_blacklist_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="faceGroupIndexCode != null  and faceGroupIndexCode != ''  ">face_group_index_code = #{faceGroupIndexCode},</if>
            <if test="name != null  and name != ''  ">name = #{name},</if>
            <if test="cameraIndexCodes != null  and cameraIndexCodes != ''  ">camera_index_codes = #{cameraIndexCodes},</if>
            <if test="description != null  and description != ''  ">description = #{description},</if>
            <if test="threshold != null  ">threshold = #{threshold},</if>
            <if test="createTime != null  ">create_time = #{createTime},</if>
            <if test="createBy != null  and createBy != ''  ">create_by = #{createBy},</if>
            <if test="createByName != null  and createByName != ''  ">create_by_name = #{createByName},</if>
            <if test="updateTime != null  ">update_time = #{updateTime},</if>
            <if test="updateBy != null  and updateBy != ''  ">update_by = #{updateBy},</if>
            <if test="updateByName != null  and updateByName != ''  ">update_by_name = #{updateByName},</if>
            <if test="delFlag != null  and delFlag != ''  ">del_flag = #{delFlag},</if>
        </trim>
        where plan_index_code = #{planIndexCode}
    </update>

    <delete id="deleteSkBlacklistPlanById" parameterType="String">
        delete
        from sk_blacklist_plan
        where plan_index_code = #{planIndexCode}
    </delete>

    <delete id="deleteSkBlacklistPlanByIds" parameterType="Integer">
        delete from sk_blacklist_plan where plan_index_code in
        <foreach item="planIndexCode" collection="array" open="(" separator="," close=")">
            #{planIndexCode}
        </foreach>
    </delete>


    <!-- 新增对象 -->
    <insert id="insertSkBlacklistPlan" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkBlacklistPlan">
        insert into sk_blacklist_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planIndexCode != null  and planIndexCode != ''  ">plan_index_code,</if>
            <if test="faceGroupIndexCode != null  and faceGroupIndexCode != ''  ">face_group_index_code,</if>
            <if test="name != null  and name != ''  ">name,</if>
            <if test="cameraIndexCodes != null  and cameraIndexCodes != ''  ">camera_index_codes,</if>
            <if test="description != null  and description != ''  ">description,</if>
            <if test="threshold != null  ">threshold,</if>
            <if test="createTime != null  ">create_time,</if>
            <if test="createBy != null  and createBy != ''  ">create_by,</if>
            <if test="createByName != null  and createByName != ''  ">create_by_name,</if>
            <if test="updateTime != null  ">update_time,</if>
            <if test="updateBy != null  and updateBy != ''  ">update_by,</if>
            <if test="createByName != null  and createByName != ''  ">update_by_name,</if>
            <if test="delFlag != null  and delFlag != ''  ">del_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planIndexCode != null  and planIndexCode != ''  ">#{planIndexCode},</if>
            <if test="faceGroupIndexCode != null  and faceGroupIndexCode != ''  ">#{faceGroupIndexCode},</if>
            <if test="name != null  and name != ''  ">#{name},</if>
            <if test="cameraIndexCodes != null  and cameraIndexCodes != ''  ">#{cameraIndexCodes},</if>
            <if test="description != null  and description != ''  ">#{description},</if>
            <if test="threshold != null  ">#{threshold},</if>
            <if test="createTime != null  ">#{createTime},</if>
            <if test="createBy != null  and createBy != ''  ">#{createBy},</if>
            <if test="createByName != null  and createByName != ''  ">#{createByName},</if>
            <if test="updateTime != null  ">#{updateTime},</if>
            <if test="updateBy != null  and updateBy != ''  ">#{updateBy},</if>
            <if test="updateByName != null  and updateByName != ''  ">#{updateByName},</if>
            <if test="delFlag != null  and delFlag != ''  ">#{delFlag},</if>
        </trim>
    </insert>


    <!-- 表字段 -->
    <sql id="column">
        plan_index_code,  <!-- 识别计划的唯一标识 -->
        face_group_index_code,  <!-- 人脸分组唯一标识 -->
        name,  <!-- 识别计划/人脸分组名称,1~32个字符；不能包含 ’ / \ : * ? " < > -->
        camera_index_codes,  <!-- 唯一标识的集合 -->
        description,  <!-- 识别计划描述，1~128个字符 -->
        threshold,  <!-- 识别计划的阈值，重点目标是指相似度超过该值时报警，范围[1, 100) -->
        create_time,  <!-- 创建时间 -->
        create_by,  <!-- 创建人 -->
        create_by_name,  <!-- 创建人姓名 -->
        update_time,  <!-- 修改时间 -->
        update_by,  <!-- 修改人 -->
        update_by_name,  <!-- 修改人姓名 -->
        del_flag  <!-- 删除标记 1有效 0无效 -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="planIndexCode != null and planIndexCode != ''">
            and plan_index_code = #{planIndexCode}  <!-- 识别计划的唯一标识 -->
        </if>
        <if test="faceGroupIndexCode != null and faceGroupIndexCode != ''">
            and face_group_index_code = #{faceGroupIndexCode}  <!-- 人脸分组唯一标识 -->
        </if>
        <if test="name != null and name != ''">
            and name = #{name}  <!-- 识别计划/人脸分组名称,1~32个字符；不能包含 ’ / \ : * ? " < > -->
        </if>
        <if test="cameraIndexCodes != null and cameraIndexCodes != ''">
            and camera_index_codes = #{cameraIndexCodes}  <!-- 唯一标识的集合 -->
        </if>
        <if test="description != null and description != ''">
            and description = #{description}  <!-- 识别计划描述，1~128个字符 -->
        </if>
        <if test="threshold != null ">
            and threshold = #{threshold}  <!-- 识别计划的阈值，重点目标是指相似度超过该值时报警，范围[1, 100) -->
        </if>
        <if test="createTime != null ">
            and create_time = #{createTime}  <!-- 创建时间 -->
        </if>
        <if test="createBy != null and createBy != ''">
            and create_by = #{createBy}  <!-- 创建人 -->
        </if>
        <if test="updateTime != null ">
            and update_time = #{updateTime}  <!-- 修改时间 -->
        </if>
        <if test="updateBy != null and updateBy != ''">
            and update_by = #{updateBy}  <!-- 修改人 -->
        </if>
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <if test="planIndexCode != null and planIndexCode != ''">
            and plan_index_code like concat('%', #{planIndexCode}, '%')  <!-- 识别计划的唯一标识 -->
        </if>
        <if test="faceGroupIndexCode != null and faceGroupIndexCode != ''">
            and face_group_index_code like concat('%', #{faceGroupIndexCode}, '%')  <!-- 人脸分组唯一标识 -->
        </if>
        <if test="name != null and name != ''">
            and name like concat('%', #{name}, '%')  <!-- 识别计划/人脸分组名称,1~32个字符；不能包含 ’ / \ : * ? " < > -->
        </if>
        <if test="cameraIndexCodes != null and cameraIndexCodes != ''">
            and camera_index_codes like concat('%', #{cameraIndexCodes}, '%')  <!-- 唯一标识的集合 -->
        </if>
        <if test="description != null and description != ''">
            and description like concat('%', #{description}, '%')  <!-- 识别计划描述，1~128个字符 -->
        </if>
        <if test="createBy != null and createBy != ''">
            and create_by like concat('%', #{createBy}, '%')  <!-- 创建人 -->
        </if>
        <if test="createByName != null and createByName != ''">
            and create_by_name like concat('%', #{createByName}, '%')  <!-- 创建人姓名 -->
        </if>
        <if test="updateBy != null and updateBy != ''">
            and update_by like concat('%', #{updateBy}, '%')  <!-- 修改人 -->
        </if>
        <if test="updateByName != null and updateByName != ''">
            and update_by_name like concat('%', #{updateByName}, '%')  <!-- 修改人姓名 -->
        </if>
    </sql>
</mapper>

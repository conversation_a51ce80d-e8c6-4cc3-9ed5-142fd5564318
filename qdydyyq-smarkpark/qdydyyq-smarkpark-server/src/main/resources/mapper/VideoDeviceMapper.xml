<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.smarkpark.mapper.VideoDeviceMapper">

    <!-- 截断区域表 -->
    <update id="truncateRegions">
        TRUNCATE TABLE sk_hdvision_regions;
    </update>

    <!-- 插入区域表 -->
    <insert id="syncRegions">
        INSERT INTO sk_hdvision_regions (
            index_code,  `name`,  region_path,  parent_index_code,  available,  leaf,  cascade_code,  cascade_type,  catalog_type,  external_index_code
            ,  sort,  create_time,  update_time
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.indexCode},  #{item.name},  #{item.regionPath},  #{item.parentIndexCode},  #{item.available},  #{item.leaf},  #{item.cascadeCode}
            ,  #{item.cascadeType},  #{item.catalogType},  #{item.externalIndexCode},  #{item.sort},  #{item.createTime},  #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 截断监控点表 -->
    <update id="truncateCameras">
        TRUNCATE TABLE sk_hdvision_cameras;
    </update>

    <!-- 插入监控点表 -->
    <insert id="syncCameras">
        INSERT INTO sk_hdvision_cameras (
            index_code, region_index_code, region_path, external_index_code, `name`, parent_index_code, longitude, latitude, elevation
            , camera_type, install_location, chan_num, cascade_code, dac_index_code, capability, record_location, channel_type, trans_type
            , treaty_type, create_time, update_time, resource_type, camera_relate_talk, dis_order, sort, decode_tag, com_id, key_board_code
            , cascade_type, region_name
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
                #{item.indexCode}, #{item.regionIndexCode}, #{item.regionPath}, #{item.externalIndexCode}, #{item.name}, #{item.parentIndexCode}
                , #{item.longitude}, #{item.latitude}, #{item.elevation}, #{item.cameraType}, #{item.installLocation}, #{item.chanNum}
                , #{item.cascadeCode}, #{item.dacIndexCode}, #{item.capability}, #{item.recordLocation}, #{item.channelType}, #{item.transType}
                , #{item.treatyType}, #{item.createTime}, #{item.updateTime}, #{item.resourceType}, #{item.cameraRelateTalk}, #{item.disOrder}
                , #{item.sort}, #{item.decodeTag}, #{item.comId}, #{item.keyBoardCode}, #{item.cascadeType}, #{item.regionName}
            )
        </foreach>
    </insert>

    <!-- 截断监控点在线状态表 -->
    <update id="truncateCamerasOnline">
        TRUNCATE TABLE sk_hdvision_cameras_online;
    </update>

    <!-- 插入监控点在线状态表 -->
    <insert id="syncCamerasOnline">
        INSERT INTO sk_hdvision_cameras_online (
            index_code, device_type, device_index_code, region_index_code, collect_time, region_name, cn, treaty_type, manufacturer, ip, `port`, `online`
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.indexCode}, #{item.deviceType}, #{item.deviceIndexCode}, #{item.regionIndexCode}, #{item.collectTime}, #{item.regionName}
            , #{item.cn}, #{item.treatyType}, #{item.manufacturer}, #{item.ip}, #{item.port}, #{item.online}
            )
        </foreach>
    </insert>

    <!-- 区域&监控点列表 -->
    <select id="regionsAndCamerasList" resultType="com.huazheng.tunny.smarkpark.api.dto.RegionsAndCamerasDto">
        SELECT
            index_code
            , `name`
            , parent_index_code
            , '0' AS node_type
            , null AS camera_type
            , null AS `online`
            , SUM(camera.online_num) AS online_num
            , SUM(camera.total_num) AS total_num
            , sort
        FROM sk_hdvision_regions
        LEFT JOIN (
            SELECT
                c.region_index_code
                 , region.region_path
                 , COUNT(c.region_index_code) AS total_num
                 , SUM(IFNULL(ol.`online`, 0)) AS online_num
            FROM sk_hdvision_cameras c
            LEFT JOIN (SELECT index_code AS iCode, `online` FROM sk_hdvision_cameras_online) ol ON ol.iCode = index_code
            LEFT JOIN sk_hdvision_regions region ON region.index_code = c.region_index_code
            GROUP BY region_index_code
        ) camera ON camera.region_path LIKE CONCAT('%@', index_code, '@%')
        GROUP BY index_code

        UNION ALL

        SELECT
            index_code
            , `name`
            , region_index_code AS parent_index_code
            , '1' AS node_type
            ,  camera_type
            , `online`
            , null AS online_num
            , null AS total_num
            ,sort
        FROM sk_hdvision_cameras
        LEFT JOIN (SELECT index_code AS iCode, `online` FROM sk_hdvision_cameras_online) ol ON ol.iCode = index_code

        ORDER BY sort DESC
    </select>

    <!-- 监控点列表 -->
    <select id="camerasList" resultType="java.util.Map">
        SELECT
            c.index_code AS indexCode
            ,c.`name`
            , CONCAT_WS('-', r10.`name`, r9.`name`, r8.`name`, r7.`name`, r6.`name`, r5.`name`, r4.`name`, r3.`name`, r2.`name`, r1.`name`) AS regionNameList
        FROM sk_hdvision_cameras c
        LEFT JOIN sk_hdvision_regions r1 ON r1.index_code = c.region_index_code
        LEFT JOIN sk_hdvision_regions r2 ON r2.index_code = r1.parent_index_code
        LEFT JOIN sk_hdvision_regions r3 ON r3.index_code = r2.parent_index_code
        LEFT JOIN sk_hdvision_regions r4 ON r4.index_code = r3.parent_index_code
        LEFT JOIN sk_hdvision_regions r5 ON r5.index_code = r4.parent_index_code
        LEFT JOIN sk_hdvision_regions r6 ON r6.index_code = r5.parent_index_code
        LEFT JOIN sk_hdvision_regions r7 ON r7.index_code = r6.parent_index_code
        LEFT JOIN sk_hdvision_regions r8 ON r8.index_code = r7.parent_index_code
        LEFT JOIN sk_hdvision_regions r9 ON r9.index_code = r8.parent_index_code
        LEFT JOIN sk_hdvision_regions r10 ON r10.index_code = r9.parent_index_code
        ORDER BY regionNameList
    </select>
</mapper>

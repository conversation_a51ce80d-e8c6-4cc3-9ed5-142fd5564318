<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.smarkpark.mapper.SkVehicleAlarmRecordMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.smarkpark.api.entity.SkVehicleAlarmRecord" id="skVehicleAlarmRecordResult">
        <result property="id" column="id"/> <!-- 序列 -->
        <result property="plateNo" column="plate_no"/> <!-- 车牌，长度16 -->
        <result property="illegalType" column="illegal_type"/> <!-- 违章类型  1（超速），2（逆行），3（黑名单），5（违停） -->
        <result property="speedType" column="speed_type"/> <!-- 测速类型  1（点位测速），2（区间测速） -->
        <result property="monitoringId" column="monitoring_id"/> <!-- 事件源  (卡口点的编号) -->
        <result property="monitoringName" column="monitoring_name"/> <!-- 事件源名称  (卡口点的名称) -->
        <result property="platePicUri" column="plate_pic_uri"/> <!-- 车牌图片uri（最大长度256 -->
        <result property="carPicUri" column="car_pic_uri"/> <!-- 车辆图片uri（最大长度256 -->
        <result property="speed" column="speed"/> <!-- 车速  (单位km/h)，大于0 -->
        <result property="crossTime" column="cross_time"/> <!-- 过车时间 -->
    </resultMap>


    <sql id="selectSkVehicleAlarmRecordVo">
        select id,
               plate_no,
               illegal_type,
               speed_type,
               monitoring_id,
               monitoring_name,
               plate_pic_uri,
               car_pic_uri,
               speed,
               cross_time
        from sk_vehicle_alarm_record
    </sql>
    <!-- 查询对象List -->
    <select id="selectSkVehicleAlarmRecordList" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkVehicleAlarmRecord" resultMap="skVehicleAlarmRecordResult">
        <include refid="selectSkVehicleAlarmRecordVo"/>
        <where>
            <include refid="equal"/>
        </where>
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectSkVehicleAlarmRecordListByLike" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkVehicleAlarmRecord" resultMap="skVehicleAlarmRecordResult">
        <include refid="selectSkVehicleAlarmRecordVo"/>
        <where>
            <include refid="like"/>
        </where>
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectSkVehicleAlarmRecordById" parameterType="Integer" resultMap="skVehicleAlarmRecordResult">
        <include refid="selectSkVehicleAlarmRecordVo"/>
        where id = #{id}
    </select>


    <update id="updateSkVehicleAlarmRecord" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkVehicleAlarmRecord">
        update sk_vehicle_alarm_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="plateNo != null  and plateNo != ''  ">plate_no = #{plateNo},</if>
            <if test="illegalType != null  ">illegal_type = #{illegalType},</if>
            <if test="speedType != null  ">speed_type = #{speedType},</if>
            <if test="monitoringId != null  and monitoringId != ''  ">monitoring_id = #{monitoringId},</if>
            <if test="monitoringName != null  and monitoringName != ''  ">monitoring_name = #{monitoringName},</if>
            <if test="platePicUri != null  and platePicUri != ''  ">plate_pic_uri = #{platePicUri},</if>
            <if test="carPicUri != null  and carPicUri != ''  ">car_pic_uri = #{carPicUri},</if>
            <if test="speed != null  and speed != ''  ">speed = #{speed},</if>
            <if test="crossTime != null  and crossTime != ''  ">cross_time = #{crossTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSkVehicleAlarmRecordById" parameterType="Integer">
        delete
        from sk_vehicle_alarm_record
        where id = #{id}
    </delete>

    <delete id="deleteSkVehicleAlarmRecordByIds" parameterType="Integer">
        delete from sk_vehicle_alarm_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <!-- 新增对象 -->
    <insert id="insertSkVehicleAlarmRecord" parameterType="com.huazheng.tunny.smarkpark.api.entity.SkVehicleAlarmRecord" useGeneratedKeys="true" keyProperty="id">
        insert into sk_vehicle_alarm_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="plateNo != null  and plateNo != ''  ">plate_no,</if>
            <if test="illegalType != null  ">illegal_type,</if>
            <if test="speedType != null  ">speed_type,</if>
            <if test="monitoringId != null  and monitoringId != ''  ">monitoring_id,</if>
            <if test="monitoringName != null  and monitoringName != ''  ">monitoring_name,</if>
            <if test="platePicUri != null  and platePicUri != ''  ">plate_pic_uri,</if>
            <if test="carPicUri != null  and carPicUri != ''  ">car_pic_uri,</if>
            <if test="speed != null  and speed != ''  ">speed,</if>
            <if test="crossTime != null  and crossTime != ''  ">cross_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="plateNo != null  and plateNo != ''  ">#{plateNo},</if>
            <if test="illegalType != null  ">#{illegalType},</if>
            <if test="speedType != null  ">#{speedType},</if>
            <if test="monitoringId != null  and monitoringId != ''  ">#{monitoringId},</if>
            <if test="monitoringName != null  and monitoringName != ''  ">#{monitoringName},</if>
            <if test="platePicUri != null  and platePicUri != ''  ">#{platePicUri},</if>
            <if test="carPicUri != null  and carPicUri != ''  ">#{carPicUri},</if>
            <if test="speed != null  and speed != ''  ">#{speed},</if>
            <if test="crossTime != null  and crossTime != ''  ">#{crossTime},</if>
        </trim>
    </insert>


    <!-- 表字段 -->
    <sql id="column">
        id,  <!-- 序列 -->
        plate_no,  <!-- 车牌，长度16 -->
        illegal_type,  <!-- 违章类型  1（超速），2（逆行），3（黑名单），5（违停） -->
        speed_type,  <!-- 测速类型  1（点位测速），2（区间测速） -->
        monitoring_id,  <!-- 事件源  (卡口点的编号) -->
        monitoring_name,  <!-- 事件源名称  (卡口点的名称) -->
        plate_pic_uri,  <!-- 车牌图片uri（最大长度256 -->
        car_pic_uri,  <!-- 车辆图片uri（最大长度256 -->
        speed,  <!-- 车速  (单位km/h)，大于0 -->
        cross_time  <!-- 过车时间 -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="id != null ">
            and id = #{id}  <!-- 序列 -->
        </if>
        <if test="plateNo != null and plateNo != ''">
            and plate_no = #{plateNo}  <!-- 车牌，长度16 -->
        </if>
        <if test="illegalType != null ">
            and illegal_type = #{illegalType}  <!-- 违章类型  1（超速），2（逆行），3（黑名单），5（违停） -->
        </if>
        <if test="speedType != null ">
            and speed_type = #{speedType}  <!-- 测速类型  1（点位测速），2（区间测速） -->
        </if>
        <if test="monitoringId != null and monitoringId != ''">
            and monitoring_id = #{monitoringId}  <!-- 事件源  (卡口点的编号) -->
        </if>
        <if test="monitoringName != null and monitoringName != ''">
            and monitoring_name = #{monitoringName}  <!-- 事件源名称  (卡口点的名称) -->
        </if>
        <if test="platePicUri != null and platePicUri != ''">
            and plate_pic_uri = #{platePicUri}  <!-- 车牌图片uri（最大长度256 -->
        </if>
        <if test="carPicUri != null and carPicUri != ''">
            and car_pic_uri = #{carPicUri}  <!-- 车辆图片uri（最大长度256 -->
        </if>
        <if test="speed != null and speed != ''">
            and speed = #{speed}  <!-- 车速  (单位km/h)，大于0 -->
        </if>
        <if test="crossTime != null and crossTime != ''">
            and cross_time = #{crossTime}  <!-- 过车时间 -->
        </if>
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <if test="plateNo != null and plateNo != ''">
            and plate_no like concat('%', #{plateNo}, '%')  <!-- 车牌，长度16 -->
        </if>
        <if test="monitoringId != null and monitoringId != ''">
            and monitoring_id like concat('%', #{monitoringId}, '%')  <!-- 事件源  (卡口点的编号) -->
        </if>
        <if test="monitoringName != null and monitoringName != ''">
            and monitoring_name like concat('%', #{monitoringName}, '%')  <!-- 事件源名称  (卡口点的名称) -->
        </if>
        <if test="platePicUri != null and platePicUri != ''">
            and plate_pic_uri like concat('%', #{platePicUri}, '%')  <!-- 车牌图片uri（最大长度256 -->
        </if>
        <if test="carPicUri != null and carPicUri != ''">
            and car_pic_uri like concat('%', #{carPicUri}, '%')  <!-- 车辆图片uri（最大长度256 -->
        </if>
        <if test="speed != null and speed != ''">
            and speed like concat('%', #{speed}, '%')  <!-- 车速  (单位km/h)，大于0 -->
        </if>
        <if test="crossTime != null and crossTime != ''">
            and cross_time like concat('%', #{crossTime}, '%')  <!-- 过车时间 -->
        </if>
    </sql>

    <!-- 插入车辆告警记录 -->
    <insert id="syncSkVehicleAlarmRecord">
        INSERT INTO sk_vehicle_alarm_record (
            plate_no, illegal_type, speed_type, monitoring_id, monitoring_name, plate_pic_uri, car_pic_uri, speed, cross_time
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.plateNo}, #{item.illegalType}, #{item.speedType}, #{item.monitoringId}, #{item.monitoringName}, #{item.platePicUri}, #{item.carPicUri}
            , #{item.speed}, #{item.crossTime}
            )
        </foreach>
    </insert>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.smarkpark.mapper.DahuaCamerasMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.smarkpark.api.entity.DahuaCameras" id="dahuaCamerasResult">
        <result property="deviceSn" column="device_sn"/> <!-- 设备唯一标识码 -->
        <result property="deviceCode" column="device_code"/> <!-- 设备编码 -->
        <result property="deviceName" column="device_name"/> <!-- 设备名称 -->
        <result property="deviceCategory" column="device_category"/> <!-- 设备大类 -->
        <result property="deviceType" column="device_type"/> <!-- 设备小类 -->
        <result property="deviceManufacturer" column="device_manufacturer"/> <!-- 厂商类型 -->
        <result property="deviceModel" column="device_model"/> <!-- 设备型号 -->
        <result property="deviceIp" column="device_ip"/> <!-- 设备ip -->
        <result property="devicePort" column="device_port"/> <!-- 设备端口 -->
        <result property="ownerCode" column="owner_code"/> <!-- 设备所属组织编码 -->
        <result property="isOnline" column="is_online"/> <!-- 设备在线状态 -->
        <result property="offlineReason" column="offline_reason"/> <!-- 设备离线原因 -->
        <result property="channelCode" column="channel_code"/> <!-- 通道编码 -->
        <result property="channelName" column="channel_name"/> <!-- 通道名称 -->
        <result property="cameraType" column="camera_type"/> <!-- 摄像头类型 -->
        <result property="channelState" column="channel_state"/> <!-- 通道状态 -->
        <result property="channelDeviceIp" column="channel_device_ip"/> <!-- 通道设备IP -->
        <result property="treeSort" column="tree_sort"/> <!-- 树排序 -->
        <result property="updateTime" column="update_time"/> <!-- 最后修改时间 -->
    </resultMap>


    <sql id="selectDahuaCamerasVo">
        select device_sn,
               device_code,
               device_name,
               device_category,
               device_type,
               device_manufacturer,
               device_model,
               device_ip,
               device_port,
               owner_code,
               is_online,
               offline_reason,
               channel_code,
               channel_name,
               camera_type,
               update_time
        from sk_dahua_cameras
    </sql>
    <!-- 查询对象List -->
    <select id="selectDahuaCamerasList" parameterType="DahuaCameras" resultMap="dahuaCamerasResult">
        <include refid="selectDahuaCamerasVo"/>
        <where>
            <include refid="equal"/>
        </where>
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectDahuaCamerasListByLike" parameterType="DahuaCameras" resultMap="dahuaCamerasResult">
        <include refid="selectDahuaCamerasVo"/>
        <where>
            <include refid="like"/>
        </where>
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectDahuaCamerasById" parameterType="String" resultMap="dahuaCamerasResult">
        <include refid="selectDahuaCamerasVo"/>
        where device_sn = #{deviceSn}
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectDahuaCamerasByChannelCode" parameterType="String" resultMap="dahuaCamerasResult">
        select channel_code,
               channel_name,
               camera_type,
               update_time
        from sk_dahua_cameras
        where channel_code = #{channelCode} limit 1
    </select>

    <!-- 模糊查询对象List -->
    <select id="camerasPage" parameterType="DahuaCameras" resultType="com.huazheng.tunny.smarkpark.api.dto.DaHuaCamerasDTO">
        select sdc.channel_code, sdc.channel_name, sdc.camera_type, sdc.channel_state, sdc.channel_device_ip
        from sk_dahua_cameras sdc
        <where>
            <if test="channelState != null ">
                and sdc.channel_state = #{channelState} <!-- 通道状态 -->
            </if>
            <if test="zjDeviceId != null or zjZoneId != null">
                and FIND_IN_SET(sdc.channel_code, (
                SELECT GROUP_CONCAT(szzc.camera_code)
                FROM sk_zhoujie_zone_cameras szzc
                WHERE szzc.del_flag = 1
                <if test="zjDeviceId != null ">
                    and szzc.device_id = #{zjDeviceId} <!-- 周界设备ID -->
                </if>
                <if test="zjZoneId != null ">
                    and szzc.zone_id = #{zjZoneId} <!-- 周界设备区域ID -->
                </if>)) = 0
            </if>
            <if test="channelCode != null and channelCode != ''">
                and sdc.channel_code like concat('%', #{channelCode}, '%')  <!-- 通道编码 -->
            </if>
            <if test="channelName != null and channelName != ''">
                and sdc.channel_name like concat('%', #{channelName}, '%')  <!-- 通道名称 -->
            </if>
            <if test="channelDeviceIp != null and channelDeviceIp != ''">
                and sdc.channel_device_ip like concat('%', #{channelDeviceIp}, '%')  <!-- 通道设备IP -->
            </if>
            and sdc.owner_code LIKE 'L%'
        </where>
    </select>

    <update id="updateDahuaCameras" parameterType="DahuaCameras">
        update sk_dahua_cameras
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceCode != null  and deviceCode != ''  ">device_code = #{deviceCode},</if>
            <if test="deviceName != null  and deviceName != ''  ">device_name = #{deviceName},</if>
            <if test="deviceCategory != null  ">device_category = #{deviceCategory},</if>
            <if test="deviceType != null  ">device_type = #{deviceType},</if>
            <if test="deviceManufacturer != null  and deviceManufacturer != ''  ">device_manufacturer =
                #{deviceManufacturer},
            </if>
            <if test="deviceModel != null  and deviceModel != ''  ">device_model = #{deviceModel},</if>
            <if test="deviceIp != null  and deviceIp != ''  ">device_ip = #{deviceIp},</if>
            <if test="devicePort != null  and devicePort != ''  ">device_port = #{devicePort},</if>
            <if test="ownerCode != null  and ownerCode != ''  ">owner_code = #{ownerCode},</if>
            <if test="isOnline != null  ">is_online = #{isOnline},</if>
            <if test="offlineReason != null  and offlineReason != ''  ">offline_reason = #{offlineReason},</if>
            <if test="channelCode != null  and channelCode != ''  ">channel_code = #{channelCode},</if>
            <if test="channelName != null  and channelName != ''  ">channel_name = #{channelName},</if>
            <if test="cameraType != null  and cameraType != ''  ">camera_type = #{cameraType},</if>
            <if test="updateTime != null  ">update_time = #{updateTime},</if>
        </trim>
        where device_sn = #{deviceSn}
    </update>

    <update id="updateDahuaCamerasState" parameterType="DahuaCameras">
        update sk_dahua_cameras
        set is_online     = #{isOnline},
            owner_code = #{ownerCode},
            channel_state = #{channelState}
        where channel_code = #{channelCode}
    </update>

    <delete id="deleteDahuaCamerasById" parameterType="String">
        delete
        from sk_dahua_cameras
        where device_sn = #{deviceSn}
    </delete>

    <delete id="deleteDahuaCamerasByIds" parameterType="Integer">
        delete from sk_dahua_cameras where device_sn in
        <foreach item="deviceSn" collection="array" open="(" separator="," close=")">
            #{deviceSn}
        </foreach>
    </delete>


    <!-- 新增对象 -->
    <insert id="insertDahuaCameras" parameterType="DahuaCameras">
        insert into sk_dahua_cameras
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceSn != null  and deviceSn != ''  ">device_sn,</if>
            <if test="deviceCode != null  and deviceCode != ''  ">device_code,</if>
            <if test="deviceName != null  and deviceName != ''  ">device_name,</if>
            <if test="deviceCategory != null  ">device_category,</if>
            <if test="deviceType != null  ">device_type,</if>
            <if test="deviceManufacturer != null  and deviceManufacturer != ''  ">device_manufacturer,</if>
            <if test="deviceModel != null  and deviceModel != ''  ">device_model,</if>
            <if test="deviceIp != null  and deviceIp != ''  ">device_ip,</if>
            <if test="devicePort != null  and devicePort != ''  ">device_port,</if>
            <if test="ownerCode != null  and ownerCode != ''  ">owner_code,</if>
            <if test="isOnline != null  ">is_online,</if>
            <if test="offlineReason != null  and offlineReason != ''  ">offline_reason,</if>
            <if test="channelCode != null  and channelCode != ''  ">channel_code,</if>
            <if test="channelName != null  and channelName != ''  ">channel_name,</if>
            <if test="cameraType != null  and cameraType != ''  ">camera_type,</if>
            <if test="updateTime != null  ">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceSn != null  and deviceSn != ''  ">#{deviceSn},</if>
            <if test="deviceCode != null  and deviceCode != ''  ">#{deviceCode},</if>
            <if test="deviceName != null  and deviceName != ''  ">#{deviceName},</if>
            <if test="deviceCategory != null  ">#{deviceCategory},</if>
            <if test="deviceType != null  ">#{deviceType},</if>
            <if test="deviceManufacturer != null  and deviceManufacturer != ''  ">#{deviceManufacturer},</if>
            <if test="deviceModel != null  and deviceModel != ''  ">#{deviceModel},</if>
            <if test="deviceIp != null  and deviceIp != ''  ">#{deviceIp},</if>
            <if test="devicePort != null  and devicePort != ''  ">#{devicePort},</if>
            <if test="ownerCode != null  and ownerCode != ''  ">#{ownerCode},</if>
            <if test="isOnline != null  ">#{isOnline},</if>
            <if test="offlineReason != null  and offlineReason != ''  ">#{offlineReason},</if>
            <if test="channelCode != null  and channelCode != ''  ">#{channelCode},</if>
            <if test="channelName != null  and channelName != ''  ">#{channelName},</if>
            <if test="cameraType != null  and cameraType != ''  ">#{cameraType},</if>
            <if test="updateTime != null  ">#{updateTime},</if>
        </trim>
    </insert>

    <!-- 清空大华同步-监控设备表 -->
    <update id="truncateDahuaCameras">
        TRUNCATE TABLE sk_dahua_cameras;
    </update>

    <!-- 批量新增大华同步-监控设备表 -->
    <insert id="insertDahuaCamerasList">
        INSERT INTO sk_dahua_cameras (device_sn, device_code, device_name, device_category, device_type,
        device_manufacturer, device_model, device_ip, device_port, owner_code, is_online, offline_reason,
        channel_code, channel_name, camera_type, update_time,channel_state, channel_capability, channel_stat,
        channel_device_ip, tree_sort) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.deviceSn}, #{item.deviceCode}, #{item.deviceName}, #{item.deviceCategory},
            #{item.deviceType},#{item.deviceManufacturer}, #{item.deviceModel}, #{item.deviceIp},
            #{item.devicePort},#{item.ownerCode}, #{item.isOnline}, #{item.offlineReason},
            #{item.channelCode},#{item.channelName}, #{item.cameraType}, #{item.updateTime},
            #{item.channelState}, #{item.channelCapability}, #{item.channelStat},#{item.channelDeviceIp}, #{item.treeSort})
        </foreach>
    </insert>

    <!-- 区域&监控点列表 -->
    <select id="regionsAndCamerasList" resultType="com.huazheng.tunny.smarkpark.api.dto.RegionsAndCamerasDto">
        SELECT org_code as indexCode
             , org_name as name
             , parent_code as parentIndexCode
             , '0'                    AS nodeType
             , null                   AS cameraType
             , null                   AS `online`
             , SUM(camera.online_num) AS onlineNum
             , SUM(camera.total_num)  AS totalNum
             , null                   AS channelCode
             , sort
        FROM sk_dahua_organization
                 LEFT JOIN (SELECT c.channel_code
                                 , c.owner_code
                                 , COUNT(c.channel_code) AS total_num
                                 , SUM(c.channel_state)     AS online_num
                            FROM sk_dahua_cameras c
                            GROUP BY c.channel_code,owner_code) camera ON camera.owner_code = org_code
        GROUP BY org_code
        UNION ALL
        SELECT sdc.channel_code as indexCode
             , sdc.channel_name as name
             , sdc.owner_code AS parentIndexCode
             , '1'               AS nodeType
             , sdc.camera_type AS cameraType
             , sdc.channel_state AS `online`
             , null              AS onlineNum
             , null              AS totalNum
             , sdc.channel_code as channelCode
             , sdc.tree_sort as sort
        FROM sk_dahua_cameras sdc
        LEFT JOIN sk_dahua_organization sdo on sdo.org_code = sdc.owner_code
        where sdo.org_name is not null and sdc.tree_sort is not null
        ORDER BY sort DESC
    </select>


    <!-- 表字段 -->
    <sql id="column">
        device_sn,  <!-- 设备唯一标识码 -->
        device_code,  <!-- 设备编码 -->
        device_name,  <!-- 设备名称 -->
        device_category,  <!-- 设备大类 -->
        device_type,  <!-- 设备小类 -->
        device_manufacturer,  <!-- 厂商类型 -->
        device_model,  <!-- 设备型号 -->
        device_ip,  <!-- 设备ip -->
        device_port,  <!-- 设备端口 -->
        owner_code,  <!-- 设备所属组织编码 -->
        is_online,  <!-- 设备在线状态 -->
        offline_reason,  <!-- 设备离线原因 -->
        channel_code,  <!-- 通道编码 -->
        channel_name,  <!-- 通道名称 -->
        camera_type,  <!-- 摄像头类型 -->
        update_time  <!-- 最后修改时间 -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="deviceSn != null and deviceSn != ''">
            and device_sn = #{deviceSn}  <!-- 设备唯一标识码 -->
        </if>
        <if test="deviceCode != null and deviceCode != ''">
            and device_code = #{deviceCode}  <!-- 设备编码 -->
        </if>
        <if test="deviceName != null and deviceName != ''">
            and device_name = #{deviceName}  <!-- 设备名称 -->
        </if>
        <if test="deviceCategory != null ">
            and device_category = #{deviceCategory}  <!-- 设备大类 -->
        </if>
        <if test="deviceType != null ">
            and device_type = #{deviceType}  <!-- 设备小类 -->
        </if>
        <if test="deviceManufacturer != null and deviceManufacturer != ''">
            and device_manufacturer = #{deviceManufacturer}  <!-- 厂商类型 -->
        </if>
        <if test="deviceModel != null and deviceModel != ''">
            and device_model = #{deviceModel}  <!-- 设备型号 -->
        </if>
        <if test="deviceIp != null and deviceIp != ''">
            and device_ip = #{deviceIp}  <!-- 设备ip -->
        </if>
        <if test="devicePort != null and devicePort != ''">
            and device_port = #{devicePort}  <!-- 设备端口 -->
        </if>
        <if test="ownerCode != null and ownerCode != ''">
            and owner_code = #{ownerCode}  <!-- 设备所属组织编码 -->
        </if>
        <if test="isOnline != null ">
            and is_online = #{isOnline}  <!-- 设备在线状态 -->
        </if>
        <if test="offlineReason != null and offlineReason != ''">
            and offline_reason = #{offlineReason}  <!-- 设备离线原因 -->
        </if>
        <if test="channelCode != null and channelCode != ''">
            and channel_code = #{channelCode}  <!-- 通道编码 -->
        </if>
        <if test="channelName != null and channelName != ''">
            and channel_name = #{channelName}  <!-- 通道名称 -->
        </if>
        <if test="cameraType != null and cameraType != ''">
            and camera_type = #{cameraType}  <!-- 摄像头类型 -->
        </if>
        <if test="updateTime != null ">
            and update_time = #{updateTime}  <!-- 最后修改时间 -->
        </if>
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <if test="deviceSn != null and deviceSn != ''">
            and device_sn like concat('%', #{deviceSn}, '%')  <!-- 设备唯一标识码 -->
        </if>
        <if test="deviceCode != null and deviceCode != ''">
            and device_code like concat('%', #{deviceCode}, '%')  <!-- 设备编码 -->
        </if>
        <if test="deviceName != null and deviceName != ''">
            and device_name like concat('%', #{deviceName}, '%')  <!-- 设备名称 -->
        </if>
        <if test="deviceManufacturer != null and deviceManufacturer != ''">
            and device_manufacturer like concat('%', #{deviceManufacturer}, '%')  <!-- 厂商类型 -->
        </if>
        <if test="deviceModel != null and deviceModel != ''">
            and device_model like concat('%', #{deviceModel}, '%')  <!-- 设备型号 -->
        </if>
        <if test="deviceIp != null and deviceIp != ''">
            and device_ip like concat('%', #{deviceIp}, '%')  <!-- 设备ip -->
        </if>
        <if test="devicePort != null and devicePort != ''">
            and device_port like concat('%', #{devicePort}, '%')  <!-- 设备端口 -->
        </if>
        <if test="ownerCode != null and ownerCode != ''">
            and owner_code like concat('%', #{ownerCode}, '%')  <!-- 设备所属组织编码 -->
        </if>
        <if test="offlineReason != null and offlineReason != ''">
            and offline_reason like concat('%', #{offlineReason}, '%')  <!-- 设备离线原因 -->
        </if>
        <if test="channelCode != null and channelCode != ''">
            and channel_code like concat('%', #{channelCode}, '%')  <!-- 通道编码 -->
        </if>
        <if test="channelName != null and channelName != ''">
            and channel_name like concat('%', #{channelName}, '%')  <!-- 通道名称 -->
        </if>
        <if test="cameraType != null and cameraType != ''">
            and camera_type like concat('%', #{cameraType}, '%')  <!-- 摄像头类型 -->
        </if>
    </sql>

    <select id="statisticsCamerasNum" resultType="java.util.Map">
        SELECT SUM(CASE WHEN channel_state = 0 THEN 1 ELSE 0 END) AS offlineNum,
               SUM(CASE WHEN channel_state = 1 THEN 1 ELSE 0 END) AS onlineNum
        FROM sk_dahua_cameras
        where owner_code LIKE 'L%'
    </select>

    <select id="statisticsKeLiuDeviceNum" resultType="java.util.Map">
        SELECT SUM(CASE WHEN sdc.channel_state = 0 THEN 1 ELSE 0 END) AS offlineNum,
               SUM(CASE WHEN sdc.channel_state = 1 THEN 1 ELSE 0 END) AS onlineNum
        FROM sk_dahua_cameras sdc
        left join sk_dahua_pfs_region_channel sprc on sdc.channel_code = sprc.channel_code
        WHERE sprc.region_code is not null
    </select>

    <select id="statisticsKeLiuDeviceList" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT sdc.channel_code deviceCode, sdc.channel_name deviceName, sdc.channel_state deviceState,
               sdc.channel_device_ip deviceIp
        FROM sk_dahua_cameras sdc
        left join sk_dahua_pfs_region_channel sprc on sdc.channel_code = sprc.channel_code
        WHERE sprc.region_code is not null
        <if test="deviceState != null">
            and sdc.channel_state = #{deviceState}
        </if>
    </select>
</mapper>

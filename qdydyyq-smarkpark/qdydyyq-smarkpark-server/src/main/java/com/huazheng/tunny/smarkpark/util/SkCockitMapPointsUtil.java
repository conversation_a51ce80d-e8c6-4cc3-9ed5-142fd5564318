package com.huazheng.tunny.smarkpark.util;

import com.huazheng.tunny.smarkpark.api.entity.SkCockpitMapPoints;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SkCockitMapPointsUtil {
    /**
     * 使用递归方法根据indexCode和parentIndexCode构建所有顶层节点的树形结构
     * @param allItems 所有的SkCockpitMapPoints对象列表
     * @return 返回包含所有顶层节点的树结构列表
     */
    public List<SkCockpitMapPoints> buildTrees(List<SkCockpitMapPoints> allItems) {
        // 创建一个临时Map来存储所有节点，便于快速查找
        Map<Integer, SkCockpitMapPoints> itemMap = new HashMap<>();
        for (SkCockpitMapPoints item : allItems) {
            itemMap.put(item.getId(), item);
        }

        // 查找所有顶层节点（顶层节点的parentIndexCode为0）
        List<SkCockpitMapPoints> roots = new ArrayList<>();
        for (SkCockpitMapPoints item : allItems) {
            if (item.getParentId() == 0) {
                roots.add(item);
            }
        }

        // 为每个顶层节点构建子树
        for (SkCockpitMapPoints root : roots) {
            buildChildren(root, itemMap);
        }

        return roots;
    }

    /**
     * 递归为指定节点构建子节点列表
     * @param node 当前处理的节点
     * @param itemMap 所有节点的Map，用于快速查找
     */
    public void buildChildren(SkCockpitMapPoints node, Map<Integer, SkCockpitMapPoints> itemMap) {
        List<SkCockpitMapPoints> children = new ArrayList<>();
        for (SkCockpitMapPoints child : itemMap.values()) {
            if (child.getParentId() != null && child.getParentId().intValue() == node.getId().intValue()) {
                children.add(child);
                // 继续为找到的子节点构建其子节点
                buildChildren(child, itemMap);
            }
        }
        node.setChildren(children);
    }

}

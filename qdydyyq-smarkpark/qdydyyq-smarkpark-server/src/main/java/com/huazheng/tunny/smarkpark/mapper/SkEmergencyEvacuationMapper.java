package com.huazheng.tunny.smarkpark.mapper;

import com.huazheng.tunny.smarkpark.api.entity.SkEmergencyEvacuation;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.huazheng.tunny.common.core.util.Query;
import java.util.List;
/**
 * 紧急疏散管理  mapper层
 *
 * <AUTHOR> code generator
 * @date 2024-07-31 16:45:34
 */
public interface SkEmergencyEvacuationMapper extends BaseMapper<SkEmergencyEvacuation> {
    /**
     * 查询紧急疏散管理信息
     *
     * @param id 紧急疏散管理ID
     * @return 紧急疏散管理信息
     */
    public SkEmergencyEvacuation selectSkEmergencyEvacuationById(Integer id);

    /**
     * 查询紧急疏散管理列表
     *
     * @param skEmergencyEvacuation 紧急疏散管理信息
     * @return 紧急疏散管理集合
     */
    public List<SkEmergencyEvacuation> selectSkEmergencyEvacuationList(SkEmergencyEvacuation skEmergencyEvacuation);

    /**
     * 模糊查询紧急疏散管理列表
     *
     * @param skEmergencyEvacuation 紧急疏散管理信息
     * @return 紧急疏散管理集合
     */
    public List<SkEmergencyEvacuation> selectSkEmergencyEvacuationListByLike(SkEmergencyEvacuation skEmergencyEvacuation);


    /**
     * 分页模糊查询紧急疏散管理列表
     *
     * @param skEmergencyEvacuation 紧急疏散管理信息
     * @return 紧急疏散管理集合
     */
    public List<SkEmergencyEvacuation> selectSkEmergencyEvacuationListByLike(Query query, SkEmergencyEvacuation skEmergencyEvacuation);


    /**
     * 新增紧急疏散管理
     *
     * @param skEmergencyEvacuation 紧急疏散管理信息
     * @return 结果
     */
    public int insertSkEmergencyEvacuation(SkEmergencyEvacuation skEmergencyEvacuation);

    /**
     * 修改紧急疏散管理
     *
     * @param skEmergencyEvacuation 紧急疏散管理信息
     * @return 结果
     */
    public int updateSkEmergencyEvacuation(SkEmergencyEvacuation skEmergencyEvacuation);

    /**
     * 删除紧急疏散管理
     *
     * @param id 紧急疏散管理ID
     * @return 结果
     */
    public int deleteSkEmergencyEvacuationById(Integer id);

    /**
     * 批量删除紧急疏散管理
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteSkEmergencyEvacuationByIds(Integer[] ids);



}

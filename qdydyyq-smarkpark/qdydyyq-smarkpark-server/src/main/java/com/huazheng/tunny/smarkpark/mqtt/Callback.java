package com.huazheng.tunny.smarkpark.mqtt;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.huazheng.tunny.smarkpark.api.entity.SkAlarmLog;
import com.huazheng.tunny.smarkpark.api.entity.SkAlarmLogRaw;
import com.huazheng.tunny.smarkpark.service.SkAlarmLogRawService;
import com.huazheng.tunny.smarkpark.service.SkAlarmLogService;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttCallback;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.Optional;

/**
 * 发布消息的回调类
 * <p>
 * 必须实现MqttCallback的接口并实现对应的相关接口方法CallBack 类将实现 MqttCallBack。
 * 每个客户机标识都需要一个回调实例。在此示例中，构造函数传递客户机标识以另存为实例数据。
 * 在回调中，将它用来标识已经启动了该回调的哪个实例。
 * 必须在回调类中实现三个方法：
 * <p>
 * public void messageArrived(MqttTopic topic, MqttMessage message)接收已经预订的发布。
 * <p>
 * public void connectionLost(Throwable cause)在断开连接时调用。
 * <p>
 * public void deliveryComplete(MqttDeliveryToken token))
 * 接收到已经发布的 QoS 1 或 QoS 2 消息的传递令牌时调用。
 * 由 MqttClient.connect 激活此回调。
 */
@Slf4j
@DependsOn("springUtils")
public class Callback implements MqttCallback {

    private static final String EVENT_TOPIC = "/iot/events/";

    /**
     * MQTT 断开连接会执行此方法
     */
    @Override
    public void connectionLost(Throwable throwable) {
        log.info("断开了MQTT连接 ：{}", throwable.getMessage());
        log.error(throwable.getMessage(), throwable);
    }

    /**
     * publish发布成功后会执行到这里
     */
    @Override
    public void deliveryComplete(IMqttDeliveryToken token) {
        log.info("发布消息成功" + token.isComplete());
    }

    /**
     * subscribe订阅后得到的消息会执行到这里
     */
    @Override
    public void messageArrived(String topic, MqttMessage message) {
        // subscribe后得到的消息会执行到这里面
        // 此处可以将订阅得到的消息进行业务处理、数据存储

        new Thread(() -> {
            this.handle(topic, message);
        }).start();

    }

    public void handle(String topic, MqttMessage message) {
        String dataString = new String(message.getPayload());
//        log.info("IoT " + System.currentTimeMillis() + " 收到来自 " + topic + " 的消息 start：{}", dataString);

        if (topic.startsWith(EVENT_TOPIC)) {
            // 设备事件上报
            saveEventReport(topic, dataString);
        }

//        log.info("IoT " + System.currentTimeMillis() + " 收到来自 " + topic + " 的消息 end：{}", dataString);

    }

    /**
     * @Description: 保存数据
     * @Param: topic, dataString
     * @Return: void
     * @Author: wx
     * @Date: 2021/6/18 11:39
     */
    public void saveEventReport(String topic, String dataString) {
        try {
            log.info("接收到mqtt推送报文:{}", dataString);

            //告警记录原始数据存库
            saveSkAlarmLogRaw(dataString);

            // 告警记录存库
            saveSkAlarmLog(dataString);

        } catch (Exception e) {
            log.error("saveEventReport.Exception:{}", e);
        }
    }


    /**
     * 告警记录原始数据存库
     */
    private void saveSkAlarmLogRaw(String dataString) {
        try {
            JSONObject json = JSONObject.parseObject(dataString);
            SkAlarmLogRawService skAlarmLogRawService = SpringUtils.getBean(SkAlarmLogRawService.class);
            if (skAlarmLogRawService != null) {
                SkAlarmLogRaw skAlarmLogRaw = new SkAlarmLogRaw();
                skAlarmLogRaw.setAlarmSource(1);
                skAlarmLogRaw.setEventId(json.getString("eventId"));
                skAlarmLogRaw.setRawData(dataString);
                skAlarmLogRawService.insertOrUpdate(skAlarmLogRaw);
            }
        } catch (Exception e) {
            log.error("servicesResponse.Exception:{}", e);
        }
    }

    /**
     * 告警记录存库
     */
    private void saveSkAlarmLog(String dataString) {
        try {
            JSONObject json = JSONObject.parseObject(dataString);
            SkAlarmLogService skAlarmLogService = SpringUtils.getBean(SkAlarmLogService.class);
            if (skAlarmLogService != null) {
                SkAlarmLog skAlarmLog = new SkAlarmLog();
                skAlarmLog.setSendTime(DateUtil.date(json.getLong("eventTime")).toTimestamp().toLocalDateTime());
                skAlarmLog.setAlarmSource(1);
                skAlarmLog.setEventId(json.getString("eventId"));
                skAlarmLog.setSrcIndex(json.getString("deviceCode"));
                skAlarmLog.setSrcType(json.getString("productKey"));
                skAlarmLog.setEventType(json.getJSONObject("params").getString("message"));
                //查设备名称
                skAlarmLog.setSrcName(skAlarmLogService.getFireControlDeviceName(json.getString("deviceCode")));
                skAlarmLogService.insertOrUpdate(skAlarmLog);
            }
        } catch (Exception e) {
            log.error("servicesResponse.Exception:{}", e);
        }
    }
}


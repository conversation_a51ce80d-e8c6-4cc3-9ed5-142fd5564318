package com.huazheng.tunny.smarkpark.mapper;

import com.huazheng.tunny.smarkpark.api.entity.SkGateList;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.huazheng.tunny.common.core.util.Query;
import java.util.List;
/**
 * 一卡通同步 - 闸机列表：包含人车道闸、测速点  mapper层
 *
 * <AUTHOR> code generator
 * @date 2024-07-08 15:47:11
 */
public interface SkGateListMapper extends BaseMapper<SkGateList> {
    /**
     * 查询一卡通同步 - 闸机列表：包含人车道闸、测速点信息
     *
     * @param id 一卡通同步 - 闸机列表：包含人车道闸、测速点ID
     * @return 一卡通同步 - 闸机列表：包含人车道闸、测速点信息
     */
    public SkGateList selectSkGateListById(Integer id);

    /**
     * 查询一卡通同步 - 闸机列表：包含人车道闸、测速点列表
     *
     * @param skGateList 一卡通同步 - 闸机列表：包含人车道闸、测速点信息
     * @return 一卡通同步 - 闸机列表：包含人车道闸、测速点集合
     */
    public List<SkGateList> selectSkGateListList(SkGateList skGateList);

    /**
     * 模糊查询一卡通同步 - 闸机列表：包含人车道闸、测速点列表
     *
     * @param skGateList 一卡通同步 - 闸机列表：包含人车道闸、测速点信息
     * @return 一卡通同步 - 闸机列表：包含人车道闸、测速点集合
     */
    public List<SkGateList> selectSkGateListListByLike(SkGateList skGateList);


    /**
     * 分页模糊查询一卡通同步 - 闸机列表：包含人车道闸、测速点列表
     *
     * @param skGateList 一卡通同步 - 闸机列表：包含人车道闸、测速点信息
     * @return 一卡通同步 - 闸机列表：包含人车道闸、测速点集合
     */
    public List<SkGateList> selectSkGateListListByLike(Query query, SkGateList skGateList);


    /**
     * 新增一卡通同步 - 闸机列表：包含人车道闸、测速点
     *
     * @param skGateList 一卡通同步 - 闸机列表：包含人车道闸、测速点信息
     * @return 结果
     */
    public int insertSkGateList(SkGateList skGateList);

    /**
     * 修改一卡通同步 - 闸机列表：包含人车道闸、测速点
     *
     * @param skGateList 一卡通同步 - 闸机列表：包含人车道闸、测速点信息
     * @return 结果
     */
    public int updateSkGateList(SkGateList skGateList);

    /**
     * 删除一卡通同步 - 闸机列表：包含人车道闸、测速点
     *
     * @param id 一卡通同步 - 闸机列表：包含人车道闸、测速点ID
     * @return 结果
     */
    public int deleteSkGateListById(Integer id);

    /**
     * 批量删除一卡通同步 - 闸机列表：包含人车道闸、测速点
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteSkGateListByIds(Integer[] ids);

    /**
     * 截断闸机列表：包含人车道闸、测速点
     * */
    void truncateSkGateList();

    /**
     * 一卡通同步 - 闸机列表：包含人车道闸、测速点
     * */
    void syncSkGateList(List<SkGateList> list);
}

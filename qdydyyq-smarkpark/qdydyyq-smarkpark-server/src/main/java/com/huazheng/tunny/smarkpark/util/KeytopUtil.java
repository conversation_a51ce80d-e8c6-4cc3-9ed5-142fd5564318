package com.huazheng.tunny.smarkpark.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.huazheng.tunny.smarkpark.api.entity.EspDoorDevice;
import com.huazheng.tunny.smarkpark.api.entity.EspDoorInfo;
import com.huazheng.tunny.smarkpark.api.entity.EspDoorOutIntoInfo;
import com.huazheng.tunny.smarkpark.mapper.EspDoorInfoMapper;
import com.huazheng.tunny.smarkpark.service.EspDoorDeviceService;
import com.huazheng.tunny.smarkpark.service.EspDoorInfoService;
import com.huazheng.tunny.smarkpark.service.EspDoorOutIntoInfoService;
import com.xxl.job.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 科拓智慧人行平台API
 *
 * <AUTHOR>
 * @date 2025/5/6
 */
@Slf4j
@Component
public class KeytopUtil {

    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private EspDoorDeviceService espDoorDeviceService;
    @Resource
    private EspDoorOutIntoInfoService espDoorOutIntoInfoService;
    @Resource
    private EspDoorInfoService espDoorInfoService;
    // 平台域名
    @Value("${keytopConfig.host}")
    private String host;

    // 登录账户
    @Value("${keytopConfig.account}")
    private String account;

    // 登录授权码（密码，base64加密）
    @Value("${keytopConfig.code}")
    private String code;


    /**
     * 身份验证
     *
     * @param force 是否强制从平台获取token
     */
    public String getToken(Boolean force) {
        String token = (String) redisTemplate.opsForValue().get("KeytopToken");
        if (token != null && !force) {
            return token;
        }
        //设置请求参数
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("account", account);
        paramMap.put("code", code);
        String json = JSONUtil.toJsonStr(paramMap);
        HttpRequest request = HttpUtil.createPost(host + "/authorize/getToken");
        request.header("Content-Type", "application/json");
        request.body(json);
        // 发送POST请求
        String result = request.execute().body();

        //解析参数
        JSONObject jsonObject = JSONUtil.parseObj(result);
        String data = String.valueOf(jsonObject.get("data"));
        JSONObject dataObject = JSONUtil.parseObj(data);
        //token
        token = String.valueOf(dataObject.get("token"));

        redisTemplate.opsForValue().set("KeytopToken", token, 10, TimeUnit.MINUTES);

        return token;
    }

    /**
     * 同步门禁设备列表
     * <p>
     * currentPage: 当前页，int类型，必传
     * pageSize: 每页查询数量，int类型，必传
     * deviceStatus：设备状态（0.离线；1.在线），int类型
     * deviceName：设备名称，String类型
     * deviceIp：设备IP，String类型
     * id：设备ID，String类型
     * belongBuildingId：设备所属楼宇ID，String类型
     */
    public synchronized void syncDoorDevicePage(Map<String, Object> paramMap, String token) {
        List<EspDoorDevice> records = new ArrayList<>();

        paramMap.put("type", "1");
        String json = JSONUtil.toJsonStr(paramMap);
        HttpRequest request = HttpUtil.createPost(host + "/device/listPage");
        request.header("Content-Type", "application/json");
        request.header("token", token);
        request.body(json);
        // 发送POST请求
        String result = request.execute().body();
//        // TODO 测试
//        String result = "{\"data\":{\"records\":[{\"id\":\"1921840776267767810\",\"name\":\"***********\",\"type\":1,\"subType\":11,\"subTypeName\":\"门禁控制器\",\"brand\":\"8\",\"brandName\":\"ACC-A\",\"ip\":\"***********\",\"buildingName\":\"默认大楼\",\"buildingId\":\"1\",\"sn\":\"3938364D0005333231341342\",\"status\":1,\"controlStatus\":null,\"groupDeviceId\":null,\"createdTime\":\"2025-05-12 16:12:02\"},{\"id\":\"1921840443969839105\",\"name\":\"*************\",\"type\":1,\"subType\":11,\"subTypeName\":\"门禁控制器\",\"brand\":\"1\",\"brandName\":\"ACC-C\",\"ip\":\"*************\",\"buildingName\":\"默认大楼\",\"buildingId\":\"1\",\"sn\":\"\",\"status\":1,\"controlStatus\":null,\"groupDeviceId\":null,\"createdTime\":\"2025-05-12 16:10:43\"},{\"id\":\"1921827741344014338\",\"name\":\"************\",\"type\":1,\"subType\":13,\"subTypeName\":\"门禁一体机\",\"brand\":\"4\",\"brandName\":\"YTJ-F\",\"ip\":\"************\",\"buildingName\":\"默认大楼\",\"buildingId\":\"1\",\"sn\":\"\",\"status\":1,\"controlStatus\":null,\"groupDeviceId\":null,\"createdTime\":\"2025-05-12 15:20:14\"},{\"id\":\"1920315572194451457\",\"name\":\"***********\",\"type\":1,\"subType\":11,\"subTypeName\":\"门禁控制器\",\"brand\":\"1\",\"brandName\":\"ACC-C\",\"ip\":\"***********\",\"buildingName\":\"默认大楼\",\"buildingId\":\"1\",\"sn\":\"\",\"status\":1,\"controlStatus\":null,\"groupDeviceId\":null,\"createdTime\":\"2025-05-08 11:11:25\"},{\"id\":\"1913148704916623361\",\"name\":\"中餐厅通道\",\"type\":1,\"subType\":11,\"subTypeName\":\"门禁控制器\",\"brand\":\"8\",\"brandName\":\"ACC-A\",\"ip\":\"************\",\"buildingName\":\"默认大楼\",\"buildingId\":\"1\",\"sn\":\"3938364D001233323134F90C\",\"status\":1,\"controlStatus\":null,\"groupDeviceId\":null,\"createdTime\":\"2025-04-18 16:32:51\"},{\"id\":\"1913148196965437442\",\"name\":\"2层入口门\",\"type\":1,\"subType\":11,\"subTypeName\":\"门禁控制器\",\"brand\":\"1\",\"brandName\":\"ACC-C\",\"ip\":\"************\",\"buildingName\":\"默认大楼\",\"buildingId\":\"1\",\"sn\":\"\",\"status\":1,\"controlStatus\":null,\"groupDeviceId\":null,\"createdTime\":\"2025-04-18 16:30:49\"},{\"id\":\"1913145916157448193\",\"name\":\"************\",\"type\":1,\"subType\":12,\"subTypeName\":\"人脸一体机\",\"brand\":\"2\",\"brandName\":\"FACE-D\",\"ip\":\"************\",\"buildingName\":\"默认大楼\",\"buildingId\":\"1\",\"sn\":\"\",\"status\":1,\"controlStatus\":null,\"groupDeviceId\":null,\"createdTime\":\"2025-04-18 16:21:46\"},{\"id\":\"1913143218259480577\",\"name\":\"************\",\"type\":1,\"subType\":12,\"subTypeName\":\"人脸一体机\",\"brand\":\"2\",\"brandName\":\"FACE-D\",\"ip\":\"************\",\"buildingName\":\"默认大楼\",\"buildingId\":\"1\",\"sn\":\"\",\"status\":1,\"controlStatus\":null,\"groupDeviceId\":null,\"createdTime\":\"2025-04-18 16:11:02\"},{\"id\":\"1913141216595005441\",\"name\":\"RKG-1F-01\",\"type\":1,\"subType\":11,\"subTypeName\":\"门禁控制器\",\"brand\":\"1\",\"brandName\":\"ACC-C\",\"ip\":\"************\",\"buildingName\":\"默认大楼\",\"buildingId\":\"1\",\"sn\":\"\",\"status\":1,\"controlStatus\":null,\"groupDeviceId\":null,\"createdTime\":\"2025-04-18 16:03:05\"},{\"id\":\"1913139243497934850\",\"name\":\"***********\",\"type\":1,\"subType\":11,\"subTypeName\":\"门禁控制器\",\"brand\":\"1\",\"brandName\":\"ACC-C\",\"ip\":\"***********\",\"buildingName\":\"默认大楼\",\"buildingId\":\"1\",\"sn\":\"\",\"status\":1,\"controlStatus\":null,\"groupDeviceId\":null,\"createdTime\":\"2025-04-18 15:55:15\"},{\"id\":\"1913139174212227073\",\"name\":\"***********\",\"type\":1,\"subType\":11,\"subTypeName\":\"门禁控制器\",\"brand\":\"1\",\"brandName\":\"ACC-C\",\"ip\":\"***********\",\"buildingName\":\"默认大楼\",\"buildingId\":\"1\",\"sn\":\"\",\"status\":1,\"controlStatus\":null,\"groupDeviceId\":null,\"createdTime\":\"2025-04-18 15:54:58\"},{\"id\":\"1913139122714562562\",\"name\":\"***********\",\"type\":1,\"subType\":11,\"subTypeName\":\"门禁控制器\",\"brand\":\"1\",\"brandName\":\"ACC-C\",\"ip\":\"***********\",\"buildingName\":\"默认大楼\",\"buildingId\":\"1\",\"sn\":\"\",\"status\":1,\"controlStatus\":null,\"groupDeviceId\":null,\"createdTime\":\"2025-04-18 15:54:46\"},{\"id\":\"1913139043400273921\",\"name\":\"***********\",\"type\":1,\"subType\":11,\"subTypeName\":\"门禁控制器\",\"brand\":\"1\",\"brandName\":\"ACC-C\",\"ip\":\"***********\",\"buildingName\":\"默认大楼\",\"buildingId\":\"1\",\"sn\":\"\",\"status\":1,\"controlStatus\":null,\"groupDeviceId\":null,\"createdTime\":\"2025-04-18 15:54:27\"},{\"id\":\"1913138902597488641\",\"name\":\"消控室\",\"type\":1,\"subType\":11,\"subTypeName\":\"门禁控制器\",\"brand\":\"1\",\"brandName\":\"ACC-C\",\"ip\":\"************\",\"buildingName\":\"默认大楼\",\"buildingId\":\"1\",\"sn\":\"\",\"status\":1,\"controlStatus\":null,\"groupDeviceId\":null,\"createdTime\":\"2025-04-18 15:53:53\"},{\"id\":\"1913138676222513153\",\"name\":\"************\",\"type\":1,\"subType\":11,\"subTypeName\":\"门禁控制器\",\"brand\":\"1\",\"brandName\":\"ACC-C\",\"ip\":\"************\",\"buildingName\":\"默认大楼\",\"buildingId\":\"1\",\"sn\":\"\",\"status\":1,\"controlStatus\":null,\"groupDeviceId\":null,\"createdTime\":\"2025-04-18 15:52:59\"}],\"total\":15,\"size\":20,\"current\":1,\"orders\":[],\"optimizeCountSql\":true,\"hitCount\":false,\"countId\":null,\"maxLimit\":null,\"searchCount\":true,\"pages\":1},\"resultCode\":200,\"resultMsg\":\"操作成功！\"}";

        //解析参数
        JSONObject jsonObject = JSONUtil.parseObj(result);
        String data = String.valueOf(jsonObject.get("data"));
        log.info("同步门禁设备列表 data:{}", data);
        JSONObject dataObject = JSONUtil.parseObj(data);

        List<EspDoorDevice> records1 = JSONArray.parseArray(String.valueOf(dataObject.get("records")), EspDoorDevice.class);
        if (CollUtil.isNotEmpty(records1)) {
            for (EspDoorDevice record : records1) {
                record.setType(1);
            }
        }
        records.addAll(records1);


        // 清空设备目录
        EspDoorDevice record = new EspDoorDevice();
        record.setType(1);
        espDoorDeviceService.delete(new EntityWrapper<>(record));
        // 插入新获取的设备信息
        espDoorDeviceService.insertOrUpdateBatch(records);
    }

    /**
     * 查询人员通行记录
     * <p>
     * currentPage：分页参数，当前页，int类型，必传
     * pageSize：每页展示数量，int类型，必传
     * doorName：门禁名称，String类型
     * userName：用户名称，String类型
     * timeStart：出入时间起始，String类型，yyyy-MM-dd HH:mm:ss
     * timeEnd：出入时间截止，String类型，yyyy-MM-dd HH:mm:ss
     */
    public synchronized void syncDoorOutIntoInfo(Map<String, Object> paramMap, String token) {
        String json = JSONUtil.toJsonStr(paramMap);
        HttpRequest request = HttpUtil.createPost(host + "/doorInOutInfo/findPage");
        request.header("Content-Type", "application/json");
        request.header("token", token);
        request.body(json);
        // 发送POST请求
        String result = request.execute().body();
        // TODO 测试
//        String result = "{\"data\":{\"records\":[{\"id\":\"1921852029946109953\",\"doorNum\":3,\"doorName\":\"9-门禁3\",\"deviceName\":\"***********\",\"staffId\":\"1919944405168263169\",\"staffCode\":\"100000010\",\"staffName\":\"朱岩\",\"staffType\":1,\"staffTypeName\":\"业主\",\"cardNo\":\"1101739649\",\"orgName\":\"默认组织\",\"passType\":1,\"passTypeName\":\"通行\",\"passMode\":\"card\",\"passModeName\":\"卡号\",\"readType\":1,\"readTypeName\":\"进门\",\"readNo\":3,\"temperature\":null,\"temperatureType\":null,\"facePhotoAddr\":null,\"monitorPhotoAddr\":null,\"time\":\"2025-05-12 16:56:44\",\"createdTime\":\"2025-05-12 16:56:45\"},{\"id\":\"1921851991605977089\",\"doorNum\":3,\"doorName\":\"9-门禁3\",\"deviceName\":\"***********\",\"staffId\":\"1919944405168263169\",\"staffCode\":\"100000010\",\"staffName\":\"朱岩\",\"staffType\":1,\"staffTypeName\":\"业主\",\"cardNo\":\"1101739649\",\"orgName\":\"默认组织\",\"passType\":1,\"passTypeName\":\"通行\",\"passMode\":\"card\",\"passModeName\":\"卡号\",\"readType\":1,\"readTypeName\":\"进门\",\"readNo\":3,\"temperature\":null,\"temperatureType\":null,\"facePhotoAddr\":null,\"monitorPhotoAddr\":null,\"time\":\"2025-05-12 16:56:35\",\"createdTime\":\"2025-05-12 16:56:36\"},{\"id\":\"1921851975692787713\",\"doorNum\":3,\"doorName\":\"9-门禁3\",\"deviceName\":\"***********\",\"staffId\":\"1919944405168263169\",\"staffCode\":\"100000010\",\"staffName\":\"朱岩\",\"staffType\":1,\"staffTypeName\":\"业主\",\"cardNo\":\"1101739649\",\"orgName\":\"默认组织\",\"passType\":1,\"passTypeName\":\"通行\",\"passMode\":\"card\",\"passModeName\":\"卡号\",\"readType\":1,\"readTypeName\":\"进门\",\"readNo\":3,\"temperature\":null,\"temperatureType\":null,\"facePhotoAddr\":null,\"monitorPhotoAddr\":null,\"time\":\"2025-05-12 16:56:31\",\"createdTime\":\"2025-05-12 16:56:32\"},{\"id\":\"1921851774005485569\",\"doorNum\":2,\"doorName\":\"10-门禁2\",\"deviceName\":\"RKG-1F-01\",\"staffId\":null,\"staffCode\":null,\"staffName\":null,\"staffType\":null,\"staffTypeName\":null,\"cardNo\":null,\"orgName\":null,\"passType\":22,\"passTypeName\":\"按钮开门\",\"passMode\":\"button\",\"passModeName\":\"按钮开门\",\"readType\":1,\"readTypeName\":\"进门\",\"readNo\":1,\"temperature\":null,\"temperatureType\":null,\"facePhotoAddr\":null,\"monitorPhotoAddr\":null,\"time\":\"2025-05-12 16:55:43\",\"createdTime\":\"2025-05-12 16:55:44\"},{\"id\":\"1921851453065732098\",\"doorNum\":2,\"doorName\":\"10-门禁2\",\"deviceName\":\"RKG-1F-01\",\"staffId\":null,\"staffCode\":null,\"staffName\":null,\"staffType\":null,\"staffTypeName\":null,\"cardNo\":null,\"orgName\":null,\"passType\":22,\"passTypeName\":\"按钮开门\",\"passMode\":\"button\",\"passModeName\":\"按钮开门\",\"readType\":1,\"readTypeName\":\"进门\",\"readNo\":1,\"temperature\":null,\"temperatureType\":null,\"facePhotoAddr\":null,\"monitorPhotoAddr\":null,\"time\":\"2025-05-12 16:54:26\",\"createdTime\":\"2025-05-12 16:54:27\"},{\"id\":\"1921850408038113281\",\"doorNum\":2,\"doorName\":\"10-门禁2\",\"deviceName\":\"RKG-1F-01\",\"staffId\":null,\"staffCode\":null,\"staffName\":null,\"staffType\":null,\"staffTypeName\":null,\"cardNo\":null,\"orgName\":null,\"passType\":22,\"passTypeName\":\"按钮开门\",\"passMode\":\"button\",\"passModeName\":\"按钮开门\",\"readType\":1,\"readTypeName\":\"进门\",\"readNo\":1,\"temperature\":null,\"temperatureType\":null,\"facePhotoAddr\":null,\"monitorPhotoAddr\":null,\"time\":\"2025-05-12 16:50:17\",\"createdTime\":\"2025-05-12 16:50:18\"},{\"id\":\"1921849415510597634\",\"doorNum\":4,\"doorName\":\"消防控制室北门\",\"deviceName\":\"消控室\",\"staffId\":null,\"staffCode\":null,\"staffName\":null,\"staffType\":null,\"staffTypeName\":null,\"cardNo\":null,\"orgName\":null,\"passType\":22,\"passTypeName\":\"按钮开门\",\"passMode\":\"button\",\"passModeName\":\"按钮开门\",\"readType\":1,\"readTypeName\":\"进门\",\"readNo\":1,\"temperature\":null,\"temperatureType\":null,\"facePhotoAddr\":null,\"monitorPhotoAddr\":null,\"time\":\"2025-05-12 16:46:20\",\"createdTime\":\"2025-05-12 16:46:22\"},{\"id\":\"1921848345178742785\",\"doorNum\":4,\"doorName\":\"消防控制室北门\",\"deviceName\":\"消控室\",\"staffId\":null,\"staffCode\":null,\"staffName\":null,\"staffType\":null,\"staffTypeName\":null,\"cardNo\":null,\"orgName\":null,\"passType\":22,\"passTypeName\":\"按钮开门\",\"passMode\":\"button\",\"passModeName\":\"按钮开门\",\"readType\":1,\"readTypeName\":\"进门\",\"readNo\":1,\"temperature\":null,\"temperatureType\":null,\"facePhotoAddr\":null,\"monitorPhotoAddr\":null,\"time\":\"2025-05-12 16:42:05\",\"createdTime\":\"2025-05-12 16:42:06\"},{\"id\":\"1921847917636558850\",\"doorNum\":4,\"doorName\":\"消防控制室北门\",\"deviceName\":\"消控室\",\"staffId\":\"1913143344055046146\",\"staffCode\":\"100000000\",\"staffName\":\"李明\",\"staffType\":1,\"staffTypeName\":\"业主\",\"cardNo\":\"2441229569\",\"orgName\":\"默认组织\",\"passType\":1,\"passTypeName\":\"通行\",\"passMode\":\"card\",\"passModeName\":\"卡号\",\"readType\":1,\"readTypeName\":\"进门\",\"readNo\":4,\"temperature\":null,\"temperatureType\":null,\"facePhotoAddr\":null,\"monitorPhotoAddr\":null,\"time\":\"2025-05-12 16:40:23\",\"createdTime\":\"2025-05-12 16:40:24\"},{\"id\":\"1921847420728975362\",\"doorNum\":1,\"doorName\":\"7-门禁1\",\"deviceName\":\"***********\",\"staffId\":\"1913157657784340481\",\"staffCode\":\"100000001\",\"staffName\":\"通卡\",\"staffType\":1,\"staffTypeName\":\"业主\",\"cardNo\":\"2795225995\",\"orgName\":\"默认组织\",\"passType\":1,\"passTypeName\":\"通行\",\"passMode\":\"card\",\"passModeName\":\"卡号\",\"readType\":2,\"readTypeName\":\"出门\",\"readNo\":1,\"temperature\":null,\"temperatureType\":null,\"facePhotoAddr\":null,\"monitorPhotoAddr\":null,\"time\":\"2025-05-12 16:38:25\",\"createdTime\":\"2025-05-12 16:38:26\"},{\"id\":\"1921847377192099841\",\"doorNum\":1,\"doorName\":\"7-门禁1\",\"deviceName\":\"***********\",\"staffId\":\"1913157657784340481\",\"staffCode\":\"100000001\",\"staffName\":\"通卡\",\"staffType\":1,\"staffTypeName\":\"业主\",\"cardNo\":\"2795225995\",\"orgName\":\"默认组织\",\"passType\":1,\"passTypeName\":\"通行\",\"passMode\":\"card\",\"passModeName\":\"卡号\",\"readType\":2,\"readTypeName\":\"出门\",\"readNo\":1,\"temperature\":null,\"temperatureType\":null,\"facePhotoAddr\":null,\"monitorPhotoAddr\":null,\"time\":\"2025-05-12 16:38:14\",\"createdTime\":\"2025-05-12 16:38:16\"},{\"id\":\"1921847369571049473\",\"doorNum\":1,\"doorName\":\"7-门禁1\",\"deviceName\":\"***********\",\"staffId\":\"1913157657784340481\",\"staffCode\":\"100000001\",\"staffName\":\"通卡\",\"staffType\":1,\"staffTypeName\":\"业主\",\"cardNo\":\"2795225995\",\"orgName\":\"默认组织\",\"passType\":1,\"passTypeName\":\"通行\",\"passMode\":\"card\",\"passModeName\":\"卡号\",\"readType\":2,\"readTypeName\":\"出门\",\"readNo\":1,\"temperature\":null,\"temperatureType\":null,\"facePhotoAddr\":null,\"monitorPhotoAddr\":null,\"time\":\"2025-05-12 16:38:13\",\"createdTime\":\"2025-05-12 16:38:14\"},{\"id\":\"1921847354534469634\",\"doorNum\":1,\"doorName\":\"7-门禁1\",\"deviceName\":\"***********\",\"staffId\":\"1913143344055046146\",\"staffCode\":\"100000000\",\"staffName\":\"李明\",\"staffType\":1,\"staffTypeName\":\"业主\",\"cardNo\":\"2441229569\",\"orgName\":\"默认组织\",\"passType\":1,\"passTypeName\":\"通行\",\"passMode\":\"card\",\"passModeName\":\"卡号\",\"readType\":1,\"readTypeName\":\"进门\",\"readNo\":2,\"temperature\":null,\"temperatureType\":null,\"facePhotoAddr\":null,\"monitorPhotoAddr\":null,\"time\":\"2025-05-12 16:38:09\",\"createdTime\":\"2025-05-12 16:38:10\"},{\"id\":\"1921847353754329090\",\"doorNum\":1,\"doorName\":\"7-门禁1\",\"deviceName\":\"***********\",\"staffId\":\"1913157657784340481\",\"staffCode\":\"100000001\",\"staffName\":\"通卡\",\"staffType\":1,\"staffTypeName\":\"业主\",\"cardNo\":\"2795225995\",\"orgName\":\"默认组织\",\"passType\":1,\"passTypeName\":\"通行\",\"passMode\":\"card\",\"passModeName\":\"卡号\",\"readType\":2,\"readTypeName\":\"出门\",\"readNo\":1,\"temperature\":null,\"temperatureType\":null,\"facePhotoAddr\":null,\"monitorPhotoAddr\":null,\"time\":\"2025-05-12 16:38:09\",\"createdTime\":\"2025-05-12 16:38:10\"},{\"id\":\"1921847320703213569\",\"doorNum\":1,\"doorName\":\"7-门禁1\",\"deviceName\":\"***********\",\"staffId\":\"1913143344055046146\",\"staffCode\":\"100000000\",\"staffName\":\"李明\",\"staffType\":1,\"staffTypeName\":\"业主\",\"cardNo\":\"2441229569\",\"orgName\":\"默认组织\",\"passType\":1,\"passTypeName\":\"通行\",\"passMode\":\"card\",\"passModeName\":\"卡号\",\"readType\":2,\"readTypeName\":\"出门\",\"readNo\":1,\"temperature\":null,\"temperatureType\":null,\"facePhotoAddr\":null,\"monitorPhotoAddr\":null,\"time\":\"2025-05-12 16:38:01\",\"createdTime\":\"2025-05-12 16:38:02\"},{\"id\":\"1921846839109033986\",\"doorNum\":1,\"doorName\":\"7-门禁1\",\"deviceName\":\"***********\",\"staffId\":\"1915602187733250049\",\"staffCode\":\"100000006\",\"staffName\":\"变配电室\",\"staffType\":1,\"staffTypeName\":\"业主\",\"cardNo\":\"2805769795\",\"orgName\":\"默认组织\",\"passType\":1,\"passTypeName\":\"通行\",\"passMode\":\"card\",\"passModeName\":\"卡号\",\"readType\":2,\"readTypeName\":\"出门\",\"readNo\":1,\"temperature\":null,\"temperatureType\":null,\"facePhotoAddr\":null,\"monitorPhotoAddr\":null,\"time\":\"2025-05-12 16:36:06\",\"createdTime\":\"2025-05-12 16:36:07\"},{\"id\":\"1921846790497050626\",\"doorNum\":1,\"doorName\":\"7-门禁1\",\"deviceName\":\"***********\",\"staffId\":\"1915602187733250049\",\"staffCode\":\"100000006\",\"staffName\":\"变配电室\",\"staffType\":1,\"staffTypeName\":\"业主\",\"cardNo\":\"2805769795\",\"orgName\":\"默认组织\",\"passType\":1,\"passTypeName\":\"通行\",\"passMode\":\"card\",\"passModeName\":\"卡号\",\"readType\":1,\"readTypeName\":\"进门\",\"readNo\":2,\"temperature\":null,\"temperatureType\":null,\"facePhotoAddr\":null,\"monitorPhotoAddr\":null,\"time\":\"2025-05-12 16:35:54\",\"createdTime\":\"2025-05-12 16:35:56\"},{\"id\":\"1921846756674183169\",\"doorNum\":1,\"doorName\":\"7-门禁1\",\"deviceName\":\"***********\",\"staffId\":\"1915602187733250049\",\"staffCode\":\"100000006\",\"staffName\":\"变配电室\",\"staffType\":1,\"staffTypeName\":\"业主\",\"cardNo\":\"2805769795\",\"orgName\":\"默认组织\",\"passType\":1,\"passTypeName\":\"通行\",\"passMode\":\"card\",\"passModeName\":\"卡号\",\"readType\":1,\"readTypeName\":\"进门\",\"readNo\":2,\"temperature\":null,\"temperatureType\":null,\"facePhotoAddr\":null,\"monitorPhotoAddr\":null,\"time\":\"2025-05-12 16:35:46\",\"createdTime\":\"2025-05-12 16:35:48\"},{\"id\":\"1921846725153988609\",\"doorNum\":1,\"doorName\":\"7-门禁1\",\"deviceName\":\"***********\",\"staffId\":\"1915602187733250049\",\"staffCode\":\"100000006\",\"staffName\":\"变配电室\",\"staffType\":1,\"staffTypeName\":\"业主\",\"cardNo\":\"2805769795\",\"orgName\":\"默认组织\",\"passType\":1,\"passTypeName\":\"通行\",\"passMode\":\"card\",\"passModeName\":\"卡号\",\"readType\":2,\"readTypeName\":\"出门\",\"readNo\":1,\"temperature\":null,\"temperatureType\":null,\"facePhotoAddr\":null,\"monitorPhotoAddr\":null,\"time\":\"2025-05-12 16:35:39\",\"createdTime\":\"2025-05-12 16:35:40\"},{\"id\":\"1921845477033975809\",\"doorNum\":2,\"doorName\":\"10-门禁2\",\"deviceName\":\"RKG-1F-01\",\"staffId\":\"1913157657784340481\",\"staffCode\":\"100000001\",\"staffName\":\"通卡\",\"staffType\":1,\"staffTypeName\":\"业主\",\"cardNo\":\"2795225995\",\"orgName\":\"默认组织\",\"passType\":1,\"passTypeName\":\"通行\",\"passMode\":\"card\",\"passModeName\":\"卡号\",\"readType\":1,\"readTypeName\":\"进门\",\"readNo\":3,\"temperature\":null,\"temperatureType\":null,\"facePhotoAddr\":null,\"monitorPhotoAddr\":null,\"time\":\"2025-05-12 16:30:41\",\"createdTime\":\"2025-05-12 16:30:43\"}],\"total\":2996,\"size\":20,\"current\":1,\"orders\":[],\"optimizeCountSql\":true,\"hitCount\":false,\"countId\":null,\"maxLimit\":null,\"searchCount\":true,\"pages\":150},\"resultCode\":200,\"resultMsg\":\"操作成功！\"}";

        //解析参数
        JSONObject jsonObject = JSONUtil.parseObj(result);
        String data = String.valueOf(jsonObject.get("data"));
        JSONObject dataObject = JSONUtil.parseObj(data);

        List<EspDoorOutIntoInfo> records = JSONArray.parseArray(String.valueOf(dataObject.get("records")), EspDoorOutIntoInfo.class);
        if (CollUtil.isNotEmpty(records)) {
            espDoorOutIntoInfoService.insertOrUpdateBatch(records);
        }
    }

    public void syncDoorInfoInfo(Map<String, Object> paramMap, String token) {
        String json = JSONUtil.toJsonStr(paramMap);
        HttpRequest request = HttpUtil.createPost(host + "/door/control/getList");
        request.header("Content-Type", "application/json");
        request.header("token", token);
        request.body(json);
        // 发送POST请求
        String result = request.execute().body();
        //解析参数
        JSONObject jsonObject = JSONUtil.parseObj(result);
        String data = String.valueOf(jsonObject.get("data"));

        List<EspDoorInfo> records = JSONArray.parseArray(String.valueOf(data), EspDoorInfo.class);
        if (CollUtil.isNotEmpty(records)) {
            espDoorInfoService.insertEspDoorInfoList(records);
            String doorIds = records.stream().map(EspDoorInfo::getId).collect(Collectors.joining(","));
            redisTemplate.opsForValue().set("doorIds", doorIds);
        }
    }

    public void syncDoorStatus(String token) {
        String doorIds = (String) redisTemplate.opsForValue().get("doorIds");
        HttpRequest request = HttpUtil.createPost(host + "/door/control/getDoorStatusByDoorIds");
        request.header("Content-Type", "application/json");
        request.header("token", token);
        doorIds = JSONUtil.toJsonStr(doorIds.split(","));
        request.body(doorIds);
        // 发送POST请求
        String result = request.execute().body();
        //解析参数
        JSONObject jsonObject = JSONUtil.parseObj(result);
        String data = String.valueOf(jsonObject.get("data"));

        List<JSONObject> records = JSONArray.parseArray(data, JSONObject.class);
        if (CollUtil.isNotEmpty(records)) {
            for (JSONObject object : records) {
                String doorId = object.getStr("doorId");
                Integer openStatus = object.getInt("doorOpenStatus");
                EspDoorInfo espDoorInfo = new EspDoorInfo();
                espDoorInfo.setId(doorId);
                espDoorInfo.setOpenStatus(openStatus);
                espDoorInfoService.updateEspDoorInfo(espDoorInfo);
            }
        }
    }
}

package com.huazheng.tunny.smarkpark.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.smarkpark.api.entity.SkVehiclePassRecord;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 一卡通同步 - 过车记录 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2024-07-08 15:46:58
 */
public interface SkVehiclePassRecordService extends IService<SkVehiclePassRecord> {
    /**
     * 查询一卡通同步 - 过车记录信息
     *
     * @param id 一卡通同步 - 过车记录ID
     * @return 一卡通同步 - 过车记录信息
     */
    public SkVehiclePassRecord selectSkVehiclePassRecordById(Integer id);

    /**
     * 查询一卡通同步 - 过车记录列表
     *
     * @param skVehiclePassRecord 一卡通同步 - 过车记录信息
     * @return 一卡通同步 - 过车记录集合
     */
    public List<SkVehiclePassRecord> selectSkVehiclePassRecordList(SkVehiclePassRecord skVehiclePassRecord);


    /**
     * 分页模糊查询一卡通同步 - 过车记录列表
     * @return 一卡通同步 - 过车记录集合
     */
    public Page selectSkVehiclePassRecordListByLike(Query query);



    /**
     * 新增一卡通同步 - 过车记录
     *
     * @param skVehiclePassRecord 一卡通同步 - 过车记录信息
     * @return 结果
     */
    public int insertSkVehiclePassRecord(SkVehiclePassRecord skVehiclePassRecord);

    /**
     * 修改一卡通同步 - 过车记录
     *
     * @param skVehiclePassRecord 一卡通同步 - 过车记录信息
     * @return 结果
     */
    public int updateSkVehiclePassRecord(SkVehiclePassRecord skVehiclePassRecord);

    /**
     * 删除一卡通同步 - 过车记录
     *
     * @param id 一卡通同步 - 过车记录ID
     * @return 结果
     */
    public int deleteSkVehiclePassRecordById(Integer id);

    /**
     * 批量删除一卡通同步 - 过车记录
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteSkVehiclePassRecordByIds(Integer[] ids);

    /**
     * 一卡通同步 - 过车记录
     * */
    void syncSkVehiclePassRecord(List<SkVehiclePassRecord> list);
}


package com.huazheng.tunny.smarkpark.mapper;

import com.huazheng.tunny.smarkpark.api.entity.SkAlarmLog;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.huazheng.tunny.common.core.util.Query;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 告警记录  mapper层
 *
 * <AUTHOR> code generator
 * @date 2024-07-16 15:25:31
 */
public interface SkAlarmLogMapper extends BaseMapper<SkAlarmLog> {
    /**
     * 分页模糊查询告警记录列表
     *
     * @param skAlarmLog 告警记录信息
     * @return 告警记录集合
     */
    public List<SkAlarmLog> selectSkAlarmLogListByLike(Query query, SkAlarmLog skAlarmLog);


    void handleBatch(SkAlarmLog skAlarmLog);

    Integer getAlarmNotifyCount(@Param("userName") String userName);

    List<Map<String, Object>> alarmClassification(Map<String, Object> params);

    List<Map<String, Object>> alarmLevel(Map<String, Object> params);

    List<Map<String, Object>> alarmStatistics(Map<String, Object> params);

    List<Map<String, Object>> alarmTrend(@Param("year") int year, @Param("alarmSource") String alarmSource);

    List<String> selectMailsByUsers(@Param("users") List<String> users);

    SkAlarmLog selectSkAlarmLogById(@Param("eventId") String eventId);

    String getFireControlDeviceName(@Param("deviceCode") String deviceCode);

    /**迁移30天前的告警日志到备份表*/
    void transferDataToHisTable(@Param("days") Integer days);

    /**清理30天前的告警日志*/
    void cleanUpAlarmLog(@Param("days") Integer days);

    Integer getAlarmNotifyType(@Param("userName") String userName);
}

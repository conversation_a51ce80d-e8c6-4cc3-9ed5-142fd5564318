package com.huazheng.tunny.smarkpark.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import com.huazheng.tunny.smarkpark.mapper.SkBlacklistFaceMapper;
import com.huazheng.tunny.smarkpark.api.entity.SkBlacklistFace;
import com.huazheng.tunny.smarkpark.service.SkBlacklistFaceService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Data
@Service("skBlacklistFaceService")
public class SkBlacklistFaceServiceImpl extends ServiceImpl<SkBlacklistFaceMapper, SkBlacklistFace> implements SkBlacklistFaceService {

    @Autowired
    private SkBlacklistFaceMapper skBlacklistFaceMapper;

    /**
     * 查询人员布控人脸信息信息
     *
     * @param indexCode 人员布控人脸信息ID
     * @return 人员布控人脸信息信息
     */
    @Override
    public SkBlacklistFace selectSkBlacklistFaceById(String indexCode) {
        return skBlacklistFaceMapper.selectSkBlacklistFaceById(indexCode);
    }

    /**
     * 查询人员布控人脸信息列表
     *
     * @param skBlacklistFace 人员布控人脸信息信息
     * @return 人员布控人脸信息集合
     */
    @Override
    public List<SkBlacklistFace> selectSkBlacklistFaceList(SkBlacklistFace skBlacklistFace) {
        return skBlacklistFaceMapper.selectSkBlacklistFaceList(skBlacklistFace);
    }


    /**
     * 分页模糊查询人员布控人脸信息列表
     *
     * @return 人员布控人脸信息集合
     */
    @Override
    public Page selectSkBlacklistFaceListByLike(Query query) {
        SkBlacklistFace skBlacklistFace = BeanUtil.toBean(query.getCondition(), SkBlacklistFace.class, CopyOptions.create());
        query.setRecords(skBlacklistFaceMapper.selectSkBlacklistFaceListByLike(query, skBlacklistFace));
        return query;
    }

    /**
     * 新增人员布控人脸信息
     *
     * @param skBlacklistFace 人员布控人脸信息信息
     * @return 结果
     */
    @Override
    public int insertSkBlacklistFace(SkBlacklistFace skBlacklistFace) {
        return skBlacklistFaceMapper.insertSkBlacklistFace(skBlacklistFace);
    }

    /**
     * 修改人员布控人脸信息
     *
     * @param skBlacklistFace 人员布控人脸信息信息
     * @return 结果
     */
    @Override
    public int updateSkBlacklistFace(SkBlacklistFace skBlacklistFace) {
        return skBlacklistFaceMapper.updateSkBlacklistFace(skBlacklistFace);
    }


    /**
     * 删除人员布控人脸信息
     *
     * @param indexCode 人员布控人脸信息ID
     * @return 结果
     */
    @Override
    public int deleteSkBlacklistFaceById(String indexCode) {
        return skBlacklistFaceMapper.deleteSkBlacklistFaceById(indexCode);
    }

    ;


    /**
     * 批量删除人员布控人脸信息对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteSkBlacklistFaceByIds(Integer[] indexCodes) {
        return skBlacklistFaceMapper.deleteSkBlacklistFaceByIds(indexCodes);
    }

}

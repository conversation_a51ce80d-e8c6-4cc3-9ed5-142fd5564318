package com.huazheng.tunny.smarkpark.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.smarkpark.api.entity.ZhoujieZoneCameras;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 周界电子围栏设备防区关联摄像头 服务接口层
 *
 * <AUTHOR>
 * @date 2025-05-21 16:12:28
 */
public interface ZhoujieZoneCamerasService extends IService<ZhoujieZoneCameras> {
    /**
     * 查询周界电子围栏设备防区关联摄像头信息
     *
     * @param id 周界电子围栏设备防区关联摄像头ID
     * @return 周界电子围栏设备防区关联摄像头信息
     */
    public ZhoujieZoneCameras selectZhoujieZoneCamerasById(Long id);

    /**
     * 查询周界电子围栏设备防区关联摄像头列表
     *
     * @param zhoujieZoneCameras 周界电子围栏设备防区关联摄像头信息
     * @return 周界电子围栏设备防区关联摄像头集合
     */
    public List<ZhoujieZoneCameras> selectZhoujieZoneCamerasList(ZhoujieZoneCameras zhoujieZoneCameras);


    /**
     * 分页模糊查询周界电子围栏设备防区关联摄像头列表
     * @return 周界电子围栏设备防区关联摄像头集合
     */
    public Page selectZhoujieZoneCamerasListByLike(Query query);

    /**
     * 新增周界电子围栏设备防区关联摄像头
     *
     * @param zhoujieZoneCameras 周界电子围栏设备防区关联摄像头信息
     * @return 结果
     */
    public R insertZhoujieZoneCameras(ZhoujieZoneCameras zhoujieZoneCameras);

    /**
     * 修改周界电子围栏设备防区关联摄像头
     *
     * @param zhoujieZoneCameras 周界电子围栏设备防区关联摄像头信息
     * @return 结果
     */
    public int updateZhoujieZoneCameras(ZhoujieZoneCameras zhoujieZoneCameras);

    /**
     * 删除周界电子围栏设备防区关联摄像头
     *
     * @param id 周界电子围栏设备防区关联摄像头ID
     * @return 结果
     */
    public int deleteZhoujieZoneCamerasById(Long id);

    /**
     * 批量删除周界电子围栏设备防区关联摄像头
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteZhoujieZoneCamerasByIds(Integer[] ids);

}


//package com.huazheng.tunny.smarkpark.filter;
//
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.http.server.reactive.ServerHttpRequest;
//import org.springframework.security.access.AccessDeniedException;
//import org.springframework.security.web.access.AccessDeniedHandler;
//import org.springframework.stereotype.Component;
//
//import javax.servlet.*;
//import javax.servlet.annotation.WebFilter;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import java.io.IOException;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//
//@Component
//public class IpFilter implements Filter {
//
//    //@Value("${hello.ip-whitelist.mappings}")
//    private Map<String, List<String>> ipMappings;
//
//    private AccessDeniedHandler accessDeniedHandler;
//
//    public IpFilter(AccessDeniedHandler accessDeniedHandler) {
//        this.accessDeniedHandler = accessDeniedHandler;
//    }
//
//    @Override
//    public void init(FilterConfig filterConfig) throws ServletException {
//        //初始化IP映射配置
//        ipMappings=new HashMap<String, List<String>>();
//        List<String> t1=new ArrayList<String>();
//        t1.add("/info");
//        ipMappings.put("127.0.0.1",t1);
//    }
//
//    @Override
//    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
//            throws IOException, ServletException {
//        System.out.println("Filter is processing the request.");
//        String clientIp = getClientIp((HttpServletRequest) request);
//        String requestPath = getRequestPath((HttpServletRequest) request);
//        System.out.println("IP地址："+clientIp+"   请求地址： "+requestPath);
//        //此处逻辑有问题，应先判断当前接口是否有IP白名单策略，如果有再进行IP白名单校验，如果没有则直接放行
//        if (ipMappings.containsKey(clientIp)) {
//            if (ipMappings.get(clientIp).stream().anyMatch(requestPath::matches)) {
//                chain.doFilter(request, response);
//            } else {
//                accessDeniedHandler.handle((HttpServletRequest) request, (HttpServletResponse) response, new AccessDeniedException("Access Denied"));
//            }
//        } else {
//            chain.doFilter(request, response);
//        }
//
//        // 在此处添加你的 IP 和接口访问控制逻辑
//        if (ipMappings.containsKey(clientIp) && ipMappings.get(clientIp).stream().anyMatch(requestPath::matches)) {
//            chain.doFilter(request, response);
//        } else {
//            accessDeniedHandler.handle((HttpServletRequest) request, (HttpServletResponse) response, new AccessDeniedException("Access Denied"));
//        }
//    }
//
//    @Override
//    public void destroy() {
//
//    }
//
//    private String getClientIp(HttpServletRequest request) {
//        String unknown = "unknown";
//        String ip = request.getHeader("X-Forwarded-For");
//        if (ip == null || ip.length() == 0 || unknown.equalsIgnoreCase(ip)) {
//            ip = request.getHeader("Proxy-Client-IP");
//        }
//        if (ip == null || ip.length() == 0 || unknown.equalsIgnoreCase(ip)) {
//            ip = request.getHeader("WL-Proxy-Client-IP");
//        }
//        if (ip == null || ip.length() == 0 || unknown.equalsIgnoreCase(ip)) {
//            ip = request.getHeader("HTTP_CLIENT_IP");
//        }
//        if (ip == null || ip.length() == 0 || unknown.equalsIgnoreCase(ip)) {
//            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
//        }
//        if (ip == null || ip.length() == 0 || unknown.equalsIgnoreCase(ip)) {
//            ip = request.getHeader("X-Real-IP");
//        }
//        if (ip == null || ip.length() == 0 || unknown.equalsIgnoreCase(ip)) {
//            ip = request.getRemoteAddr();
//        }
//        //对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
//        if (ip != null && ip.length() > 0) {
//            String[] ips = ip.split(",");
//            if (ips.length > 0) {
//                ip = ips[0];
//            }
//        }
//        return ip;
//
//
//
//    }
//
//    private String getRequestPath(HttpServletRequest request) {
//        return request.getRequestURI();
//    }
//}

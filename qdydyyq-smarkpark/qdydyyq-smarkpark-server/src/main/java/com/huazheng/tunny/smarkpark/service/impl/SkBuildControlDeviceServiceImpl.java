package com.huazheng.tunny.smarkpark.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.smarkpark.api.entity.SkAlarmLog;
import com.huazheng.tunny.smarkpark.api.entity.SkBuildControlDeviceAttribute;
import com.huazheng.tunny.smarkpark.api.entity.SkBuildControlDeviceConfig;
import com.huazheng.tunny.smarkpark.mapper.SkAlarmLogMapper;
import com.huazheng.tunny.smarkpark.mapper.SkBuildControlDeviceAttributeMapper;
import com.huazheng.tunny.smarkpark.mapper.SkBuildControlDeviceConfigMapper;
import com.huazheng.tunny.smarkpark.mapper.SkBuildControlDeviceMapper;
import com.huazheng.tunny.smarkpark.api.entity.SkBuildControlDevice;
import com.huazheng.tunny.smarkpark.service.SkBuildControlDeviceService;
import com.huazheng.tunny.smarkpark.util.LightingUtil;
import com.huazheng.tunny.smarkpark.util.SnowUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.huazheng.tunny.smarkpark.util.ParkingUtil.MD5;

@Slf4j
@Service("skBuildControlDeviceService")
public class SkBuildControlDeviceServiceImpl extends ServiceImpl<SkBuildControlDeviceMapper, SkBuildControlDevice> implements SkBuildControlDeviceService {

    @Autowired
    private SkBuildControlDeviceMapper skBuildControlDeviceMapper;
    @Autowired
    private SkBuildControlDeviceAttributeMapper skBuildControlDeviceAttributeMapper;
    @Autowired
    private SkBuildControlDeviceConfigMapper skBuildControlDeviceConfigMapper;
    @Resource
    private SkAlarmLogMapper skAlarmLogMapper;
    @Autowired
    private StringRedisTemplate redisTemplate;
    // 平台域名
    @Value("${buildControl.host}")
    private String host;
    // 登录账户
    @Value("${buildControl.appId}")
    private String appId;
    // 登录授权码
    @Value("${buildControl.key}")
    private String key;
    @Value("${lighting.host:http://***********}")
    private String lightingHost;

    /**
     * 查询楼控设备表信息
     *
     * @param id 楼控设备表ID
     * @return 楼控设备表信息
     */
    @Override
    public SkBuildControlDevice selectSkBuildControlDeviceById(Long id) {
        SkBuildControlDevice device = skBuildControlDeviceMapper.selectSkBuildControlDeviceById(id);
        if (device != null) {
            setAttributeList(device);
        }
        return device;
    }

    @Override
    public SkBuildControlDevice selectSkBuildControlDeviceByCode(String deviceCode) {
        SkBuildControlDevice device = skBuildControlDeviceMapper.selectSkBuildControlDeviceByCode(deviceCode);
        if (device != null) {
            log.info("device:{}", JSONUtil.toJsonStr(device));
            setAttributeList(device);
        }
        return device;
    }

    public void setAttributeList(SkBuildControlDevice device) {
        List<SkBuildControlDeviceAttribute> attributeList = new ArrayList<>();
        log.info("device.getType():{}", device.getType());
        if (device.getType() < 5) {
            log.info("device.getType():{}", device.getType());
            attributeList = queryAttributeList(device.getDeviceCode(), device.getDeviceAlias());
        }
        if (CollUtil.isEmpty(attributeList)) {
            SkBuildControlDeviceAttribute attribute = new SkBuildControlDeviceAttribute();
            attribute.setDeviceCode(device.getDeviceCode());
            attributeList = skBuildControlDeviceAttributeMapper.selectList(new EntityWrapper<>(attribute));
        }
        device.setAttributeList(attributeList);
    }

    /**
     * 查询楼控设备表列表
     *
     * @param skBuildControlDevice 楼控设备表信息
     * @return 楼控设备表集合
     */
    @Override
    public List<SkBuildControlDevice> selectSkBuildControlDeviceList(SkBuildControlDevice skBuildControlDevice) {
        return skBuildControlDeviceMapper.selectSkBuildControlDeviceList(skBuildControlDevice);
    }


    /**
     * 分页模糊查询楼控设备表列表
     *
     * @return 楼控设备表集合
     */
    @Override
    public R selectSkBuildControlDeviceListByLike(Query query) {
        SkBuildControlDevice skBuildControlDevice = BeanUtil.toBean(query.getCondition(), SkBuildControlDevice.class, CopyOptions.create());
        query.setRecords(skBuildControlDeviceMapper.selectSkBuildControlDeviceListByLike(query, skBuildControlDevice));
        return R.success(query);
    }

    /**
     * 新增楼控设备表
     *
     * @param skBuildControlDevice 楼控设备表信息
     * @return 结果
     */
    @Override
    public int insertSkBuildControlDevice(SkBuildControlDevice skBuildControlDevice) {
        return skBuildControlDeviceMapper.insertSkBuildControlDevice(skBuildControlDevice);
    }

    /**
     * 修改楼控设备表
     *
     * @param skBuildControlDevice 楼控设备表信息
     * @return 结果
     */
    @Override
    public int updateSkBuildControlDevice(SkBuildControlDevice skBuildControlDevice) {
        return skBuildControlDeviceMapper.updateSkBuildControlDevice(skBuildControlDevice);
    }


    /**
     * 删除楼控设备表
     *
     * @param id 楼控设备表ID
     * @return 结果
     */
    @Override
    public int deleteSkBuildControlDeviceById(Long id) {
        return skBuildControlDeviceMapper.deleteSkBuildControlDeviceById(id);
    }


    /**
     * 批量删除楼控设备表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteSkBuildControlDeviceByIds(Integer[] ids) {
        return skBuildControlDeviceMapper.deleteSkBuildControlDeviceByIds(ids);
    }

    /**
     * 获取属性
     */
    @Transactional(rollbackFor = Exception.class)
    public List<SkBuildControlDeviceAttribute> queryAttributeList(String deviceCode, String trurCpnName) {
        List<SkBuildControlDeviceAttribute> list = new ArrayList<>();
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("appid", appId);
        paramMap.put("cpn_name", trurCpnName);
        paramMap.put("sign", sign(paramMap, key, "sign"));
        try {
            log.info("{} :请求参数：{}", host + "/Data/IndexV2/getParmNameByCpn", JSONUtil.toJsonStr(paramMap));
            String str = HttpRequest.get(host + "/Data/IndexV2/getParmNameByCpn").form(paramMap).execute().body();
            log.info("{} :获取设备属性接口返回：{}", trurCpnName, str);
            JSONObject json = JSONUtil.parseObj(str);
            if (json.getInt("code") == 0) {
                List<SkBuildControlDeviceConfig> configList = queryConfigList(trurCpnName, deviceCode);
                List<JSONObject> jsonList = JSONUtil.toList(json.getStr("data"), JSONObject.class);
                if (CollUtil.isNotEmpty(jsonList)) {
                    for (JSONObject dataObject : jsonList) {
                        SkBuildControlDeviceAttribute attribute = createSkBuildControlDeviceAttribute(dataObject, configList);
                        if (attribute != null) {
                            attribute.setDeviceCode(deviceCode);
                            list.add(attribute);
                        }
                    }
                }
            }
            if (CollUtil.isNotEmpty(list)) {
                skBuildControlDeviceAttributeMapper.deleteDeviceAttributeByDeviceCode(deviceCode);
                skBuildControlDeviceAttributeMapper.insertSkBuildControlDeviceAttributeList(list);
            }

        } catch (Exception e) {
            log.error("接口调用异常：{}", e);
            e.printStackTrace();
        }
        return list;
    }

    /**
     * 同步设备状态
     */
    @Override
    public void syncDeviceStatus() {
        List<SkBuildControlDevice> deviceList = skBuildControlDeviceMapper.selectList(new EntityWrapper<SkBuildControlDevice>());
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("appid", appId);
        String sign = sign(paramMap, key, "sign");
        for (SkBuildControlDevice device : deviceList) {
            if (device.getType() < 5) {
                Map<String, Object> bodyMap = new HashMap<>();
                bodyMap.put("cpn_name", device.getDeviceCode());
                try {
                    String str = HttpRequest.post(host + "/Data/DevicesInfo/getIsalive?appid=" + appId + "&sign=" + sign).body(JSONUtil.toJsonStr(bodyMap)).execute().body();
                    JSONObject json = JSONUtil.parseObj(str);
                    if (json.getInt("code") == 0) {
                        JSONObject data = JSONUtil.parseObj(json.getStr("data"));
                        device.setRunState(data.getStr("online"));
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        //修改设备状态
        updateDeviceStatusList(deviceList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateDeviceStatusList(List<SkBuildControlDevice> deviceList) {
        int step = 50;
        for (int i = 0; i < deviceList.size(); i += step) {
            // 计算当前批次的结束索引（防止越界）
            int endIndex = (i + step) < deviceList.size() ? (i + step) : deviceList.size();
            skBuildControlDeviceMapper.updateDeviceStatusList(deviceList.subList(i, endIndex));
        }
    }

    public SkBuildControlDeviceAttribute createSkBuildControlDeviceAttribute(JSONObject dataObject, List<SkBuildControlDeviceConfig> configList) {
        SkBuildControlDeviceAttribute attribute = new SkBuildControlDeviceAttribute();
        String paramName = dataObject.getStr("param_name").trim();
        for (SkBuildControlDeviceConfig config : configList) {
            if (paramName.equals(config.getStatusVar().toString())) {
                attribute.setAttributeCode(paramName);
                attribute.setAttributeName(config.getShowName());
                attribute.setAttributeUnit(config.getUnit());
                String paramValue = dataObject.getStr("param_value").trim();
                if (StrUtil.isNotBlank(config.getShowValue())) {
                    String[] valsList = config.getShowValue().split("#");
                    for (String vals : valsList) {
                        String[] val = vals.split(":");
                        if (val.length > 1 && val[0].equals(paramValue)) {
                            attribute.setAttributeVal(val[1]);
                        }
                    }
                }
                if (StrUtil.isBlank(attribute.getAttributeVal())) {
                    attribute.setAttributeVal(paramValue);
                }
                attribute.setReportTime(LocalDateTime.now());
                continue;
            }
        }
        if (StrUtil.isBlank(attribute.getAttributeName())) {
            return null;
        }
        return attribute;
    }

    public List<SkBuildControlDeviceConfig> queryConfigList(String trurCpnName, String deviceCode) {
        List<SkBuildControlDeviceConfig> list = new ArrayList<>();
        String lkDeviceConfigStr = redisTemplate.opsForValue().get("lk_device_config:" + trurCpnName);
        if (StrUtil.isNotBlank(lkDeviceConfigStr)) {
            list = JSONUtil.toList(lkDeviceConfigStr, SkBuildControlDeviceConfig.class);
        }
        if (CollUtil.isEmpty(list)) {
            SkBuildControlDeviceConfig config = new SkBuildControlDeviceConfig();
            config.setTrueCpnName(trurCpnName);
            config.setDeviceCode(deviceCode);
            list = skBuildControlDeviceConfigMapper.selectList(new EntityWrapper<>(config));
            redisTemplate.opsForValue().set("lk_device_config:" + trurCpnName, JSONUtil.toJsonStr(list));
        }
        return list;
    }

    public static String sign(final Map<String, Object> data, String key, String signField) {
        StringBuilder sb = new StringBuilder();
        for (String k : data.keySet()) {
            if (k.equals(signField)) {
                continue;
            }
            // 参数值为空，则不参与签名
            if (data.get(k) != null && data.get(k).toString().trim().length() > 0) {
                sb.append(k).append("=").append(data.get(k).toString().trim()).append("&");
            }
        }
        sb.append("key=").append(key);
        String result = MD5(sb.toString()).toUpperCase();
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncLightingDevice() {
        LightingUtil lightingUtil = new LightingUtil();
        List<SkBuildControlDevice> list = lightingUtil.queryBrowseTag(lightingHost, "");
        if (CollUtil.isNotEmpty(list)) {
            // 清空现有数据（注意：该操作不可逆）
            skBuildControlDeviceMapper.truncateLightingDevice();

            // 分批次插入数据（每批50条，避免单次SQL数据量过大）
            int step = 50;
            for (int i = 0; i < list.size(); i += step) {
                // 计算当前批次的结束索引（防止越界）
                int endIndex = (i + step) < list.size() ? (i + step) : list.size();
                skBuildControlDeviceMapper.insertSkBuildControlDeviceList(list.subList(i, endIndex));
            }
            skBuildControlDeviceAttributeMapper.deleteDeviceAttributeByLightingDevice();
            List<SkBuildControlDeviceAttribute> attributeList = new ArrayList<>();
            for (SkBuildControlDevice device : list) {
                attributeList.addAll(device.getAttributeList());
            }
            for (int i = 0; i < attributeList.size(); i += step) {
                // 计算当前批次的结束索引（防止越界）
                int endIndex = (i + step) < attributeList.size() ? (i + step) : attributeList.size();
                skBuildControlDeviceAttributeMapper.insertSkBuildControlDeviceAttributeList(attributeList.subList(i, endIndex));
            }
        }

    }

    @Override
    public void syncDeviceEvent() {
        List<SkAlarmLog> alarmList = new ArrayList();
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("appid", appId);
        String sign = sign(paramMap, key, "sign");
        try {
            String str = HttpRequest.get(host + "/Data/DevicesInfo/getWarning?appid=" + appId + "&sign=" + sign).execute().body();
            JSONObject json = JSONUtil.parseObj(str);
            log.info("获取楼控事件:{}", str);
            if (json.getInt("code") == 0) {
                List<JSONObject> dataList = JSONUtil.toList(json.getJSONArray("data"), JSONObject.class);
                for (JSONObject dataObject : dataList) {
                    String eventId = "buildControl_" + dataObject.getStr("id");
                    String deviceCode = dataObject.getStr("cpn_name");
                    SkAlarmLog alarmLog = skAlarmLogMapper.selectSkAlarmLogById(eventId);
                    if (alarmLog != null) {
                        continue;
                    }
                    SkBuildControlDevice device = skBuildControlDeviceMapper.selectSkBuildControlDeviceByCode(deviceCode);
                    alarmLog = new SkAlarmLog();
                    alarmLog.setAlarmSource(2);
                    alarmLog.setSendTime(LocalDateTime.now());
                    alarmLog.setEventId(eventId);
                    alarmLog.setEventType("malfunctionAlarm");
                    alarmLog.setSrcIndex(deviceCode);
                    alarmLog.setSrcType(dataObject.getStr("warning_level"));
                    alarmLog.setSrcName(device.getDeviceName());
                    alarmLog.setEventContent(dataObject.getStr("act_content"));
                    alarmList.add(alarmLog);
                    skAlarmLogMapper.insert(alarmLog);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

}

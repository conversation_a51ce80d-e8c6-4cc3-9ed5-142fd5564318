package com.huazheng.tunny.smarkpark.util;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报警类型枚举
 */
@Getter
@AllArgsConstructor
public enum DoorAlarmType {
    ALARM1_FIRE("alarm1Fire", "报警输入1消防报警",""),
    ALARM1_ALERT("alarm1Alert", "报警输入1普通报警",""),
    ALARM2_FIRE("alarm2Fire", "报警输入2消防报警",""),
    ALARM2_ALERT("alarm2Alert", "报警输入2普通报警",""),
    DOORBELL("doorbell", "门铃",""),
    AUTH_CRACK("authCrack", "认证超限",""),
    STORAGE_FULL("storageFull", "存储空间满",""),
    MULTI_CARD_SWIPE("13", "多卡重复刷卡",""),
    MULTI_CARD_OPEN("14", "多卡开门",""),
    EXPIRY_CONTROL("15", "到期管制",""),
    PUBLIC_FLOOR_PWD("16", "公共楼层密码",""),
    DEDUCTION_FAIL("17", "扣次失败",""),
    FLOOR_PWD("18", "楼层密码",""),
    FLOOR_PWD_ERROR("19", "楼层密码错误",""),
    INVALID_CARD_ALARM("20", "连续非法卡报警",""),
    BLACKLIST_CARD("21", "黑名单卡",""),
    BUTTON_OPEN("22", "按钮开门",""),
    QR_PWD_ERROR("23", "二维码密码错误",""),
    EXPIRED("24", "过期",""),
    LINKAGE_ALARM("25", "联动报警",""),
    PUBLIC_PWD_ERROR("26", "公共密码错误",""),
    PUBLIC_PWD_OPEN("27", "公共密码开门",""),
    TIMEOUT_ALARM("30", "超时报警","TimeoutFailed"),
    BREAK_IN_ALARM("31", "闯入报警","BreakIntoFailed"),
    TAMPER_ALARM("32", "防拆报警","TamperFailed"),
    HOSTAGE_ALARM("33", "挟持报警","HijackFailed"),
    SECRET_KEY_ERROR("34", "秘钥错误","OtherFailed"),
    TAILGATING_CONTROL("35", "尾随管制","TailgatingControl"),
    EMERGENCY_CLOSE("36", "紧急关闭","OtherFailed"),
    CLOCK_FAULT("37", "控制器时钟故障","OtherFailed"),
    STORAGE_FAULT("38", "控制器存储器故障","OtherFailed"),
    DUAL_CARD_OPEN1("39", "双卡开门卡1","OtherFailed"),
    FIRST_CARD_CONTROL("40", "首卡管制","OtherFailed"),
    FIRST_CARD_OUT_OF_TIME("41", "首卡管制时段外","OtherFailed"),
    TIMED_OPEN("42", "定时开门","OtherFailed"),
    EMERGENCY_SHUTDOWN("43", "紧急关门","OtherFailed"),
    EMERGENCY_OPEN("44", "紧急开启","OtherFailed"),
    UNKNOWN("99", "未知","OtherFailed");

    private final String code;
    private final String description;
    private final String eventCode;
    /**
     * 根据编码获取枚举
     */
    public static String getDescriptionByCode(Object code) {
        for (DoorAlarmType type : values()) {
            if (type.code.equals(code)) {
                return type.description;
            }
        }
        return UNKNOWN.description;
    }

    /**
     * 根据编码获取枚举
     */
    public static String getEventCodeByCode(Object code) {
        for (DoorAlarmType type : values()) {
            if (type.code.equals(code)) {
                return type.eventCode;
            }
        }
        return UNKNOWN.eventCode;
    }
}

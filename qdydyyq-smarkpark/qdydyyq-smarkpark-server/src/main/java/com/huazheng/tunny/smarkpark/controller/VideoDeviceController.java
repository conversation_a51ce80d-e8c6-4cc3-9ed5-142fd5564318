package com.huazheng.tunny.smarkpark.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.log.annotation.SysLog;
import com.huazheng.tunny.smarkpark.api.dto.PlaybackParamDto;
import com.huazheng.tunny.smarkpark.api.dto.PreviewParamDto;
import com.huazheng.tunny.smarkpark.api.dto.RegionsAndCamerasDto;
import com.huazheng.tunny.smarkpark.api.entity.SkHdvisionCameras;
import com.huazheng.tunny.smarkpark.api.entity.SkHdvisionCamerasOnline;
import com.huazheng.tunny.smarkpark.api.entity.SkHdvisionRegions;
import com.huazheng.tunny.smarkpark.service.SkPersonPassRecordService;
import com.huazheng.tunny.smarkpark.service.VideoDeviceService;
import com.huazheng.tunny.smarkpark.util.HikvisionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

/**
 * 视频设备管理
 * */
@Slf4j
@RestController
public class VideoDeviceController {
    @Resource
    private HikvisionUtil hdvisionUtil;
    @Resource
    private VideoDeviceService videoDeviceService;

    /**
     * 区域&监控点数据同步
     * （每4小时执行一次自动同步）
     * */
    @Scheduled(cron = "0 30 0/4 * * ?")
    @SysLog("区域&监控点数据同步")
    @GetMapping("/sync/regionsAndCameras")
    public R syncRegionsAndCameras() {
        log.info("执行 - 区域&监控点数据同步");
        // 区域同步
        JSONObject body_regions = new JSONObject();
        body_regions.put("pageNo", 1);
        body_regions.put("pageSize", 1000);
        body_regions.put("resourceType", "region");
        String regions_str = hdvisionUtil.doPostStringArtemis(HikvisionUtil.regionsPage, body_regions);
        JSONObject regions_json = JSONObject.parseObject(regions_str);
        JSONObject regions_data = regions_json.getJSONObject("data");
        if (regions_data != null) {
            JSONArray regions_list = regions_data.getJSONArray("list");
            if (regions_list != null && regions_list.size() > 0) {
                List<SkHdvisionRegions> list = JSONObject.parseArray(regions_list.toJSONString(), SkHdvisionRegions.class);
                videoDeviceService.syncRegions(list);
            }
        }

        // 监控点同步
        JSONObject body_cameras = new JSONObject();
        body_cameras.put("pageNo", 1);
        body_cameras.put("pageSize", 1000);
        body_cameras.put("resourceType", "camera");
        String cameras_str = hdvisionUtil.doPostStringArtemis(HikvisionUtil.camerasPage, body_cameras);
        JSONObject cameras_json = JSONObject.parseObject(cameras_str);
        JSONObject cameras_data = cameras_json.getJSONObject("data");
        if (cameras_data != null) {
            JSONArray cameras_list = cameras_data.getJSONArray("list");
            if (cameras_list != null && cameras_list.size() > 0) {
                List<SkHdvisionCameras> list = JSONObject.parseArray(cameras_list.toJSONString(), SkHdvisionCameras.class);
                videoDeviceService.syncCameras(list);
            }
        }

        // 监控点状态同步
        JSONObject body_cameras_online = new JSONObject();
        body_cameras_online.put("pageNo", 1);
        body_cameras_online.put("pageSize", 1000);
        String cameras_online_str = hdvisionUtil.doPostStringArtemis(HikvisionUtil.camerasOnlineList, body_cameras_online);
        JSONObject cameras_online_json = JSONObject.parseObject(cameras_online_str);
        JSONObject cameras_online_data = cameras_online_json.getJSONObject("data");
        if (cameras_online_data != null) {
            JSONArray cameras_online_list = cameras_online_data.getJSONArray("list");
            if (cameras_online_list != null && cameras_online_list.size() > 0) {
                List<SkHdvisionCamerasOnline> list = JSONObject.parseArray(cameras_online_list.toJSONString(), SkHdvisionCamerasOnline.class);
                videoDeviceService.syncCamerasOnline(list);
            }
        }

        return new R(Boolean.TRUE);
    }

    /**
     * 区域&监控点树
     * */
    @SysLog("区域&监控点树")
    @GetMapping("/videoDevice/tree")
    public R videoDeviceTree() {
        List<RegionsAndCamerasDto> list = videoDeviceService.regionsAndCamerasList();
        // 构建树
        return new R<>(list);
    }

    /**
     * 监控点列表
     * */
    @SysLog("监控点列表")
    @GetMapping("/videoDevice/camerasList")
    public R camerasList() {
        List<Map<String, Object>> list = videoDeviceService.camerasList();
        // 构建树
        return new R<>(list);
    }

    /**
     * 监控点实时取流
     * */
    @SysLog("监控点实时取流")
    @PostMapping("/videoDevice/preview")
    public R preview(@RequestBody PreviewParamDto previewParamDto) {
        JSONObject body_preview = JSONObject.parseObject(JSONObject.toJSONString(previewParamDto));
        String preview_str = hdvisionUtil.doPostStringArtemis(HikvisionUtil.previewUrls, body_preview);
        JSONObject preview_json = JSONObject.parseObject(preview_str);
        return new R(preview_str);
    }

    /**
     * 监控点回放取流
     * */
    @SysLog("监控点回放取流")
    @PostMapping("/videoDevice/playback")
    public R playback(@RequestBody PlaybackParamDto playbackParamDto) {
        JSONObject body_playback = JSONObject.parseObject(JSONObject.toJSONString(playbackParamDto));
        String playback_str = hdvisionUtil.doPostStringArtemis(HikvisionUtil.playbackUrls, body_playback);
        JSONObject playback_json = JSONObject.parseObject(playback_str);
        return new R(playback_str);
    }

}

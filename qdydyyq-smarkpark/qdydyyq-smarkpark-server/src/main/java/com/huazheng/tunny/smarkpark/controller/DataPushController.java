package com.huazheng.tunny.smarkpark.controller;

import com.alibaba.fastjson.JSON;
import com.huazheng.tunny.smarkpark.api.dto.Notificationlist;
import com.huazheng.tunny.smarkpark.api.entity.SkGateList;
import com.huazheng.tunny.smarkpark.api.entity.SkPersonPassRecord;
import com.huazheng.tunny.smarkpark.api.entity.SkVehicleAlarmRecord;
import com.huazheng.tunny.smarkpark.api.entity.SkVehiclePassRecord;
import com.huazheng.tunny.smarkpark.service.*;
import com.huazheng.tunny.smarkpark.enums.NotificationsEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 一卡通&通行态势数据推送
 * */
@RestController
@Slf4j
public class DataPushController {

    @Resource
    private SkPersonPassRecordService skPersonPassRecordService;
    @Resource
    private SkVehiclePassRecordService vehiclePassRecordService;
    @Resource
    private SkVehicleAlarmRecordService skVehicleAlarmService;
    @Resource
    private SkGateListService skGateListService;

    /**
     * 通行态势数据同步
     * */
    @PostMapping("/DataPush/Notifications")
    public Map<String, Object> notifications(@RequestBody Map<String, Object> body) {
        log.info("执行 - 通行态势数据同步");
        Notificationlist notificationlist = JSON.parseObject(JSON.toJSONString(body.get("NotificationlistObject")), Notificationlist.class);
        String dataType = NotificationsEnum.getBeanNameByCode(notificationlist.getDataType());

        if (dataType.equals(SkPersonPassRecord.class.getSimpleName())) {
            skPersonPassRecordService.syncSkPersonPassRecord((List<SkPersonPassRecord>) notificationlist.getData());
        } else if (dataType.equals(SkVehiclePassRecord.class.getSimpleName())) {
            vehiclePassRecordService.syncSkVehiclePassRecord((List<SkVehiclePassRecord>) notificationlist.getData());
        } else if (dataType.equals(SkVehicleAlarmRecord.class.getSimpleName())) {
            skVehicleAlarmService.syncSkVehicleAlarmRecord((List<SkVehicleAlarmRecord>) notificationlist.getData());
        } else if (dataType.equals(SkGateList.class.getSimpleName())) {
            skGateListService.syncSkGateList((List<SkGateList>) notificationlist.getData());
        }

        Map<String, Object> res = new HashMap<>();
        res.put("StatusCode", 200);
        res.put("StatusString", "ok");

        return res;
    }


}

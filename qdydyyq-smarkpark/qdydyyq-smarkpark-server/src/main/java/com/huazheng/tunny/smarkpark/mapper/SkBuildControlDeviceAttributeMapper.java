package com.huazheng.tunny.smarkpark.mapper;

import com.huazheng.tunny.smarkpark.api.entity.SkBuildControlDeviceAttribute;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 楼控设备属性表  mapper层
 *
 * <AUTHOR>
 * @date 2025-06-04 13:27:18
 */
public interface SkBuildControlDeviceAttributeMapper extends BaseMapper<SkBuildControlDeviceAttribute> {
    /**
     * 查询楼控设备属性表信息
     *
     * @param id 楼控设备属性表ID
     * @return 楼控设备属性表信息
     */
    public SkBuildControlDeviceAttribute selectSkBuildControlDeviceAttributeById(Long id);

    /**
     * 查询楼控设备属性表列表
     *
     * @param skBuildControlDeviceAttribute 楼控设备属性表信息
     * @return 楼控设备属性表集合
     */
    public List<SkBuildControlDeviceAttribute> selectSkBuildControlDeviceAttributeList(SkBuildControlDeviceAttribute skBuildControlDeviceAttribute);

    /**
     * 模糊查询楼控设备属性表列表
     *
     * @param skBuildControlDeviceAttribute 楼控设备属性表信息
     * @return 楼控设备属性表集合
     */
    public List<SkBuildControlDeviceAttribute> selectSkBuildControlDeviceAttributeListByLike(SkBuildControlDeviceAttribute skBuildControlDeviceAttribute);


    /**
     * 分页模糊查询楼控设备属性表列表
     *
     * @param skBuildControlDeviceAttribute 楼控设备属性表信息
     * @return 楼控设备属性表集合
     */
    public List<SkBuildControlDeviceAttribute> selectSkBuildControlDeviceAttributeListByLike(Query query, SkBuildControlDeviceAttribute skBuildControlDeviceAttribute);


    /**
     * 新增楼控设备属性表
     *
     * @param skBuildControlDeviceAttribute 楼控设备属性表信息
     * @return 结果
     */
    public int insertSkBuildControlDeviceAttribute(SkBuildControlDeviceAttribute skBuildControlDeviceAttribute);

    /**
     * 修改楼控设备属性表
     *
     * @param skBuildControlDeviceAttribute 楼控设备属性表信息
     * @return 结果
     */
    public int updateSkBuildControlDeviceAttribute(SkBuildControlDeviceAttribute skBuildControlDeviceAttribute);

    /**
     * 删除楼控设备属性表
     *
     * @param id 楼控设备属性表ID
     * @return 结果
     */
    public int deleteSkBuildControlDeviceAttributeById(Long id);

    /**
     * 批量删除楼控设备属性表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteSkBuildControlDeviceAttributeByIds(Integer[] ids);


    void deleteDeviceAttributeByDeviceCode(String deviceCode);

    void insertSkBuildControlDeviceAttributeList(List<SkBuildControlDeviceAttribute> list);

    void deleteDeviceAttributeByLightingDevice();
}

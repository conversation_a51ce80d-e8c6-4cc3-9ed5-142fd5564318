package com.huazheng.tunny.smarkpark.controller;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.log.annotation.SysLog;
import com.huazheng.tunny.smarkpark.api.entity.SkCockpitMapPoints;
import com.huazheng.tunny.smarkpark.service.SkCockpitMapPointsService;
import com.huazheng.tunny.smarkpark.util.SkCockitMapPointsUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 驾驶舱地图监控点位
 *
 * <AUTHOR> code generator
 * @date 2024-07-11 14:24:41
 */
@Slf4j
@RestController
@RequestMapping("/skCockpitMapPoints")
public class SkCockpitMapPointsController {

    @Autowired
    private SkCockpitMapPointsService skCockpitMapPointsService;

    /**
     * 返回厂区树形菜单集合
     */
    @SysLog("返回厂区树形菜单集合")
    @GetMapping(value = "/tree")
    public R getTree() {
        List<SkCockpitMapPoints> list = skCockpitMapPointsService.selectList(new EntityWrapper<SkCockpitMapPoints>().ne("point_type", "2"));
        for (SkCockpitMapPoints entity : list) {
            if (entity.getPointType() == 0) {
                entity.setTitle(entity.getTitle() + "(厂区图)");
            }
            if (entity.getPointType() == 1) {
                entity.setTitle(entity.getTitle() + "(厂区点位)");
            }
        }
        SkCockitMapPointsUtil skCockitMapPointsUtil = new SkCockitMapPointsUtil();
        // 构建树
        return new R<>(skCockitMapPointsUtil.buildTrees(list));
    }

    /**
     * 列表
     *
     * @return
     */
    @SysLog("驾驶舱地图监控点位列表")
    @GetMapping("/list")
    public R list(@RequestParam(value = "parentId") Integer parentId, Integer clientId) {
        SkCockpitMapPoints skCockpitMapPoints = new SkCockpitMapPoints();
        skCockpitMapPoints.setParentId(parentId);
        if (clientId == null || clientId == 0) {
            skCockpitMapPoints.setClientId(1);
        } else {
            skCockpitMapPoints.setClientId(clientId);
        }
        List<SkCockpitMapPoints> list = skCockpitMapPointsService.selectSkCockpitMapPointsList(skCockpitMapPoints);
        return new R(list);
    }

    /**
     * 分页
     *
     * @param params
     * @return
     */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  skCockpitMapPointsService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return skCockpitMapPointsService.selectSkCockpitMapPointsListByLike(new Query<>(params));
    }

    /**
     * 信息
     *
     * @param id
     * @return R
     */
    @GetMapping("/{id}")
    public R info(@PathVariable("id") Integer id) {
        SkCockpitMapPoints skCockpitMapPoints = skCockpitMapPointsService.selectById(id);
        return new R<>(skCockpitMapPoints);
    }

    /**
     * 保存
     *
     * @param skCockpitMapPoints
     * @return R
     */
    @PostMapping
    public R save(@RequestBody SkCockpitMapPoints skCockpitMapPoints) {
        skCockpitMapPointsService.insertOrUpdate(skCockpitMapPoints);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 删除
     *
     * @param indexCode
     * @return R
     */
    @GetMapping("/del/{indexCode}")
    public R delete(@PathVariable String indexCode) {
        skCockpitMapPointsService.deleteById(indexCode);
        return new R<>(Boolean.TRUE);
    }

}

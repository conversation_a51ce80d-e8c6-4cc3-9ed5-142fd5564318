package com.huazheng.tunny.smarkpark.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.smarkpark.api.entity.EspDoorDevice;

import java.util.List;
import java.util.Map;

/**
 * 【科拓】门禁设备表  mapper层
 *
 * <AUTHOR> code generator
 * @date 2025-05-07 11:43:47
 */
public interface EspDoorDeviceMapper extends BaseMapper<EspDoorDevice> {
    /**
     * 查询【科拓】门禁设备表信息
     *
     * @param id 【科拓】门禁设备表ID
     * @return 【科拓】门禁设备表信息
     */
    public EspDoorDevice selectEspDoorDeviceById(String id);

    /**
     * 查询【科拓】门禁设备表列表
     *
     * @param espDoorDevice 【科拓】门禁设备表信息
     * @return 【科拓】门禁设备表集合
     */
    public List<EspDoorDevice> selectEspDoorDeviceList(EspDoorDevice espDoorDevice);

    /**
     * 模糊查询【科拓】门禁设备表列表
     *
     * @param espDoorDevice 【科拓】门禁设备表信息
     * @return 【科拓】门禁设备表集合
     */
    public List<EspDoorDevice> selectEspDoorDeviceListByLike(EspDoorDevice espDoorDevice);


    /**
     * 分页模糊查询【科拓】门禁设备表列表
     *
     * @param espDoorDevice 【科拓】门禁设备表信息
     * @return 【科拓】门禁设备表集合
     */
    public List<EspDoorDevice> selectEspDoorDeviceListByLike(Query query, EspDoorDevice espDoorDevice);


    /**
     * 新增【科拓】门禁设备表
     *
     * @param espDoorDevice 【科拓】门禁设备表信息
     * @return 结果
     */
    public int insertEspDoorDevice(EspDoorDevice espDoorDevice);

    /**
     * 修改【科拓】门禁设备表
     *
     * @param espDoorDevice 【科拓】门禁设备表信息
     * @return 结果
     */
    public int updateEspDoorDevice(EspDoorDevice espDoorDevice);

    /**
     * 删除【科拓】门禁设备表
     *
     * @param id 【科拓】门禁设备表ID
     * @return 结果
     */
    public int deleteEspDoorDeviceById(String id);

    /**
     * 批量删除【科拓】门禁设备表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteEspDoorDeviceByIds(Integer[] ids);


    Map<String, Object> statisticsDeviceNum();

    List<Map<String, Object>> statisticsDeviceList(Query query, Map<String, Object> map);
}

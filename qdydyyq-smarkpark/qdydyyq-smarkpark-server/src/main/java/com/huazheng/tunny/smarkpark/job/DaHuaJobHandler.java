package com.huazheng.tunny.smarkpark.job;

import cn.hutool.core.date.DateUtil;
import com.huazheng.tunny.smarkpark.service.DaHuaService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 科拓智慧人行平台数据同步定时任务
 *
 * <AUTHOR>
 * @date 2025/5/6
 */
@Slf4j
@Component
public class DaHuaJobHandler {
    @Resource
    private DaHuaService daHuaService;

    /**
     * 同步大华组织架构
     * <p>
     * 每天1点同步一次
     */
    @XxlJob("cronOrganizationJobHandler")
    public ReturnT<String> cronOrganizationJobHandler(String param) throws Exception{
        log.info("同步大华组织架构开始 " + DateUtil.now());
        try {
            daHuaService.cronDeviceTree(new HashMap<>());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        log.info("同步大华组织架构成功 " + DateUtil.now());
        return ReturnT.SUCCESS;
    }

    /**
     * 大华设备同步
     * <p>
     * 每天一点30分同步一次
     */
    @XxlJob("cronDeviceJobHandler")
    public ReturnT<String> cronDeviceJobHandler(String param) throws Exception{
        log.info("全量同步大华设备开始 " + DateUtil.now());
        //设置请求参数
        try {
            daHuaService.cronDeviceList(new HashMap<>());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        log.info("全量同步大华设备成功 " + DateUtil.now());
        return ReturnT.SUCCESS;
    }

    /**
     * 大华设备状态同步
     * <p>
     * 每天5点至23点没30分钟同步一次
     */
    @XxlJob("cronDeviceStatusJobHandler")
    public ReturnT<String> cronDeviceStatusJobHandler(String param) throws Exception {
        log.info("大华设备状态同步开始 " + DateUtil.now());
        //设置请求参数
        try {
            Map<String,Object> map = new HashMap<>();
            map.put("type", "cronStatus");
            daHuaService.cronDeviceList(map);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        log.info("大华设备状态同步结束 " + DateUtil.now());
        return ReturnT.SUCCESS;
    }
}

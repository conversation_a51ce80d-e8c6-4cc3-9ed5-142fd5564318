package com.huazheng.tunny.smarkpark.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.huazheng.tunny.smarkpark.api.entity.EspDoorDevice;
import com.huazheng.tunny.smarkpark.mapper.ParkingBarrierGateMapper;
import com.huazheng.tunny.smarkpark.api.entity.ParkingBarrierGate;
import com.huazheng.tunny.smarkpark.service.EspDoorDeviceService;
import com.huazheng.tunny.smarkpark.service.ParkingBarrierGateService;
import com.huazheng.tunny.smarkpark.util.ParkingUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service("parkingBarrierGateService")
public class ParkingBarrierGateServiceImpl extends ServiceImpl<ParkingBarrierGateMapper, ParkingBarrierGate> implements ParkingBarrierGateService {

    @Autowired
    private ParkingBarrierGateMapper parkingBarrierGateMapper;

    @Autowired
    private EspDoorDeviceService espDoorDeviceService;

    /**
     * 查询停车场-车辆道闸信息
     *
     * @param channelId 停车场-车辆道闸ID
     * @return 停车场-车辆道闸信息
     */
    @Override
    public ParkingBarrierGate selectParkingBarrierGateById(Integer channelId) {
        return parkingBarrierGateMapper.selectParkingBarrierGateById(channelId);
    }

    /**
     * 查询停车场-车辆道闸列表
     *
     * @param parkingBarrierGate 停车场-车辆道闸信息
     * @return 停车场-车辆道闸集合
     */
    @Override
    public List<ParkingBarrierGate> selectParkingBarrierGateList(ParkingBarrierGate parkingBarrierGate) {
        return parkingBarrierGateMapper.selectParkingBarrierGateList(parkingBarrierGate);
    }


    /**
     * 分页模糊查询停车场-车辆道闸列表
     *
     * @return 停车场-车辆道闸集合
     */
    @Override
    public Page selectParkingBarrierGateListByLike(Query query) {
        ParkingBarrierGate parkingBarrierGate = BeanUtil.toBean(query.getCondition(), ParkingBarrierGate.class, CopyOptions.create());
        query.setRecords(parkingBarrierGateMapper.selectParkingBarrierGateListByLike(query, parkingBarrierGate));
        return query;
    }

    /**
     * 新增停车场-车辆道闸
     *
     * @param parkingBarrierGate 停车场-车辆道闸信息
     * @return 结果
     */
    @Override
    public int insertParkingBarrierGate(ParkingBarrierGate parkingBarrierGate) {
        return parkingBarrierGateMapper.insertParkingBarrierGate(parkingBarrierGate);
    }

    /**
     * 修改停车场-车辆道闸
     *
     * @param parkingBarrierGate 停车场-车辆道闸信息
     * @return 结果
     */
    @Override
    public int updateParkingBarrierGate(ParkingBarrierGate parkingBarrierGate) {
        return parkingBarrierGateMapper.updateParkingBarrierGate(parkingBarrierGate);
    }


    /**
     * 删除停车场-车辆道闸
     *
     * @param channelId 停车场-车辆道闸ID
     * @return 结果
     */
    @Override
    public int deleteParkingBarrierGateById(Integer channelId) {
        return parkingBarrierGateMapper.deleteParkingBarrierGateById(channelId);
    }


    /**
     * 批量删除停车场-车辆道闸对象
     *
     * @param channelIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteParkingBarrierGateByIds(Integer[] channelIds) {
        return parkingBarrierGateMapper.deleteParkingBarrierGateByIds(channelIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cronChannelsList(List<ParkingBarrierGate> list) {
        List<EspDoorDevice> records = new ArrayList<>();
        parkingBarrierGateMapper.truncateParkingBarrierGate();
        for (ParkingBarrierGate parkingBarrierGate : list) {
            if (parkingBarrierGate.getDeviceStatus() == null || parkingBarrierGate.getDeviceStatus() == 2) {
                parkingBarrierGate.setOnlineStatus(0);
            }else{
                parkingBarrierGate.setOnlineStatus(1);
            }
            parkingBarrierGate.setDeviceSn("parking_barrier_gate_" + parkingBarrierGate.getDeviceSn());
            parkingBarrierGateMapper.insertParkingBarrierGate(parkingBarrierGate);
            EspDoorDevice record = new EspDoorDevice();
            record.setType(2);
            record.setIp(parkingBarrierGate.getCameraIp());
            record.setId(parkingBarrierGate.getDeviceSn());
            record.setName(parkingBarrierGate.getChannelName());
            record.setStatus(parkingBarrierGate.getOnlineStatus());
            record.setSubTypeName("车辆道闸");
            record.setSn(parkingBarrierGate.getDeviceSn());
            records.add(record);
        }

        // 清空设备目录
        EspDoorDevice record = new EspDoorDevice();
        record.setType(2);
        espDoorDeviceService.delete(new EntityWrapper<>(record));
        // 插入新获取的设备信息
        espDoorDeviceService.insertOrUpdateBatch(records);
    }


}

package com.huazheng.tunny.smarkpark.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import com.huazheng.tunny.smarkpark.mapper.SkVehiclePassRecordMapper;
import com.huazheng.tunny.smarkpark.api.entity.SkVehiclePassRecord;
import com.huazheng.tunny.smarkpark.service.SkVehiclePassRecordService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import javax.annotation.Resource;
import java.util.List;

@Data
@Service("skVehiclePassRecordService")
public class SkVehiclePassRecordServiceImpl extends ServiceImpl<SkVehiclePassRecordMapper, SkVehiclePassRecord> implements SkVehiclePassRecordService {

    @Resource
    private SkVehiclePassRecordMapper skVehiclePassRecordMapper;

    /**
     * 查询一卡通同步 - 过车记录信息
     *
     * @param id 一卡通同步 - 过车记录ID
     * @return 一卡通同步 - 过车记录信息
     */
    @Override
    public SkVehiclePassRecord selectSkVehiclePassRecordById(Integer id)
    {
        return skVehiclePassRecordMapper.selectSkVehiclePassRecordById(id);
    }

    /**
     * 查询一卡通同步 - 过车记录列表
     *
     * @param skVehiclePassRecord 一卡通同步 - 过车记录信息
     * @return 一卡通同步 - 过车记录集合
     */
    @Override
    public List<SkVehiclePassRecord> selectSkVehiclePassRecordList(SkVehiclePassRecord skVehiclePassRecord)
    {
        return skVehiclePassRecordMapper.selectSkVehiclePassRecordList(skVehiclePassRecord);
    }


    /**
     * 分页模糊查询一卡通同步 - 过车记录列表
     * @return 一卡通同步 - 过车记录集合
     */
    @Override
    public Page selectSkVehiclePassRecordListByLike(Query query)
    {
        SkVehiclePassRecord skVehiclePassRecord =  BeanUtil.toBean(query.getCondition(), SkVehiclePassRecord.class, CopyOptions.create());
        query.setRecords(skVehiclePassRecordMapper.selectSkVehiclePassRecordListByLike(query,skVehiclePassRecord));
        return query;
    }

    /**
     * 新增一卡通同步 - 过车记录
     *
     * @param skVehiclePassRecord 一卡通同步 - 过车记录信息
     * @return 结果
     */
    @Override
    public int insertSkVehiclePassRecord(SkVehiclePassRecord skVehiclePassRecord)
    {
        return skVehiclePassRecordMapper.insertSkVehiclePassRecord(skVehiclePassRecord);
    }

    /**
     * 修改一卡通同步 - 过车记录
     *
     * @param skVehiclePassRecord 一卡通同步 - 过车记录信息
     * @return 结果
     */
    @Override
    public int updateSkVehiclePassRecord(SkVehiclePassRecord skVehiclePassRecord)
    {
        return skVehiclePassRecordMapper.updateSkVehiclePassRecord(skVehiclePassRecord);
    }


    /**
     * 删除一卡通同步 - 过车记录
     *
     * @param id 一卡通同步 - 过车记录ID
     * @return 结果
     */
    @Override
    public int deleteSkVehiclePassRecordById(Integer id)
    {
        return skVehiclePassRecordMapper.deleteSkVehiclePassRecordById( id);
    };


    /**
     * 批量删除一卡通同步 - 过车记录对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteSkVehiclePassRecordByIds(Integer[] ids)
    {
        return skVehiclePassRecordMapper.deleteSkVehiclePassRecordByIds( ids);
    }

    /**
     * 一卡通同步 - 过车记录
     * */
    @Override
    public void syncSkVehiclePassRecord(List<SkVehiclePassRecord> list) {
        int step = 50;
        for (int i = 0; i < list.size(); i += step) {
            skVehiclePassRecordMapper.syncSkVehiclePassRecord(list.subList(i, (i + step) < list.size() ? (i + step) : list.size()));
        }
    }

}

package com.huazheng.tunny.smarkpark.mapper;

import com.huazheng.tunny.smarkpark.api.entity.ParkingOutIntoInfo;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.huazheng.tunny.common.core.util.Query;
import java.util.List;
/**
 * 停车场-进出场记录  mapper层
 *
 * <AUTHOR>
 * @date 2025-06-10 16:32:58
 */
public interface ParkingOutIntoInfoMapper extends BaseMapper<ParkingOutIntoInfo> {
    /**
     * 查询停车场-进出场记录信息
     *
     * @param id 停车场-进出场记录ID
     * @return 停车场-进出场记录信息
     */
    public ParkingOutIntoInfo selectParkingOutIntoInfoById(Integer id);

    /**
     * 查询停车场-进出场记录列表
     *
     * @param parkingOutIntoInfo 停车场-进出场记录信息
     * @return 停车场-进出场记录集合
     */
    public List<ParkingOutIntoInfo> selectParkingOutIntoInfoList(ParkingOutIntoInfo parkingOutIntoInfo);

    /**
     * 模糊查询停车场-进出场记录列表
     *
     * @param parkingOutIntoInfo 停车场-进出场记录信息
     * @return 停车场-进出场记录集合
     */
    public List<ParkingOutIntoInfo> selectParkingOutIntoInfoListByLike(ParkingOutIntoInfo parkingOutIntoInfo);


    /**
     * 分页模糊查询停车场-进出场记录列表
     *
     * @param parkingOutIntoInfo 停车场-进出场记录信息
     * @return 停车场-进出场记录集合
     */
    public List<ParkingOutIntoInfo> selectParkingOutIntoInfoListByLike(Query query, ParkingOutIntoInfo parkingOutIntoInfo);


    /**
     * 新增停车场-进出场记录
     *
     * @param parkingOutIntoInfo 停车场-进出场记录信息
     * @return 结果
     */
    public int insertParkingOutIntoInfo(ParkingOutIntoInfo parkingOutIntoInfo);

    /**
     * 修改停车场-进出场记录
     *
     * @param parkingOutIntoInfo 停车场-进出场记录信息
     * @return 结果
     */
    public int updateParkingOutIntoInfo(ParkingOutIntoInfo parkingOutIntoInfo);

    /**
     * 删除停车场-进出场记录
     *
     * @param id 停车场-进出场记录ID
     * @return 结果
     */
    public int deleteParkingOutIntoInfoById(Integer id);

    /**
     * 批量删除停车场-进出场记录
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteParkingOutIntoInfoByIds(Integer[] ids);



}

package com.huazheng.tunny.smarkpark.util;

import com.alibaba.fastjson.JSONObject;
import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.hikvision.artemis.sdk.enums.Method;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Component
public class HikvisionUtil implements Serializable {
    private static final long serialVersionUID = 1L;

    @Value("${artemisConfig.host}")
    private String host;

    @Value("${artemisConfig.appKey}")
    private String appKey;

    @Value("${artemisConfig.appSecret}")
    private String appSecret;

    /**
     * 分页获取区域列表
     * */
    public static String regionsPage = "/api/irds/v2/region/nodesByParams";
    /**
     * 获取资源列表v2
     * */
    public static String camerasPage = "/api/irds/v2/deviceResource/resources";
    /**
     * 获取监控点在线状态
     * */
    public static String camerasOnlineList = "/api/nms/v1/online/camera/get";
    /**
     * 获取监控点预览取流URLv2
     * */
    public static String previewUrls = "/api/video/v2/cameras/previewURLs";
    /**
     * 获取监控点回放取流URLv2
     * */
    public static String playbackUrls = "/api/video/v2/cameras/playbackURLs";
    /**
     * 海康SSO单点登录
     * */
    public static String ssoUrl = "/api/cas/v1/tgt/login";


    /**
     * 按事件类型订阅事件
     * */
    public static String eventSubscriptionByEventTypes = "/api/eventService/v1/eventSubscriptionByEventTypes";
    /**
     * 按事件类型取消订阅
     * */
    public static String eventUnSubscriptionByEventTypes = "/api/eventService/v1/eventUnSubscriptionByEventTypes";
    /**
     * 根据用户查询事件订阅详情
     * */
    public static String eventSubscriptionView = "/api/eventService/v1/eventSubscriptionView";

    /**
     * 按条件查询人脸分组
     * */
    public static String faceGroupList = "/api/frs/v1/face/group";
    /**
     * 单个添加人脸分组
     * */
    public static String faceGroupAddition = "/api/frs/v1/face/group/single/addition";
    /**
     * 单个修改人脸分组
     * */
    public static String faceGroupUpdate = "/api/frs/v1/face/group/single/update";
    /**
     * 删除人脸分组
     * */
    public static String faceGroupDeletion = "/api/frs/v1/face/group/batch/deletion";
    /**
     * 按条件批量查询人脸
     * */
    public static String faceList = "/api/frs/v1/face";
    /**
     * 单个添加人脸
     * */
    public static String faceAddition = "/api/frs/v1/face/single/addition";
    /**
     * 批量删除人脸
     * */
    public static String faceDeletion = "/api/frs/v1/face/deletion";
    /**
     * 查询重点目标识别计划
     * */
    public static String blackListPlanList = "/api/frs/v1/plan/recognition/black";
    /**
     * 单个添加重点目标识别计划
     * */
    public static String blackListPlanAddition = "/api/frs/v1/plan/recognition/black/addition";
    /**
     * 删除重点目标识别计划
     * */
    public static String blackListPlanDeletion = "/api/frs/v1/plan/recognition/black/deletion";


    /**
     * get请求
     * */
    public String doGetArtemis(String apiUrl, JSONObject jsonBody) {
        return this.api(apiUrl, jsonBody, Method.GET);
    }

    /**
     * post请求
     * */
    public String doPostStringArtemis(String apiUrl, JSONObject jsonBody) {
        return this.api(apiUrl, jsonBody, Method.POST_STRING);
    }

    /**
     * api调用逻辑
     * */
    private String api(String apiUrl, JSONObject jsonBody, Method method) {

        /**
         * STEP1：设置平台参数，根据实际情况,设置host appkey appsecret 三个参数.
         */
        // 平台的ip端口  "*************:443";
        ArtemisConfig.host = host;
        // 密钥appkey  "21814338";
        ArtemisConfig.appKey = appKey;
        // 密钥appSecret  "5R2VTRe3nykahDktx0Z2";
        ArtemisConfig.appSecret = appSecret;

        /**
         * STEP2：设置OpenAPI接口的上下文
         */
        String artemisPath = "/artemis";

        /**
         * STEP3：设置接口的URI地址
         */
        //"/api/resource/v1/regions/root";
        String previewUrlsApi = artemisPath + apiUrl;
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", previewUrlsApi);//根据现场环境部署确认是http还是https
            }
        };

        /**
         * STEP4：设置参数提交方式
         */
        String contentType = "application/json";

        /**
         * STEP5：组装请求参数
         */
        if(jsonBody == null) {
            jsonBody = new JSONObject();
        }
        String body = jsonBody.toJSONString();
        /**
         * STEP6：调用接口
         */
        String result = null;
        if (method == Method.POST_STRING) {
            // post请求application/json类型参数
            result = ArtemisHttpUtil.doPostStringArtemis(path, body, null, null, contentType, null);
        }
        if (method == Method.GET) {
            // post请求application/json类型参数
            result = ArtemisHttpUtil.doGetArtemis(path, jsonBody.toJavaObject(Map.class), null, contentType, null);
        }
        return result;
    }
}
package com.huazheng.tunny.smarkpark.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import com.huazheng.tunny.smarkpark.mapper.EspDoorInfoMapper;
import com.huazheng.tunny.smarkpark.api.entity.EspDoorInfo;
import com.huazheng.tunny.smarkpark.service.EspDoorInfoService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Data
@Service("espDoorInfoService")
public class EspDoorInfoServiceImpl extends ServiceImpl<EspDoorInfoMapper, EspDoorInfo> implements EspDoorInfoService {

    @Autowired
    private EspDoorInfoMapper espDoorInfoMapper;

    /**
     * 查询门禁明细表信息
     *
     * @param id 门禁明细表ID
     * @return 门禁明细表信息
     */
    @Override
    public EspDoorInfo selectEspDoorInfoById(String id) {
        return espDoorInfoMapper.selectEspDoorInfoById(id);
    }

    /**
     * 查询门禁明细表列表
     *
     * @param espDoorInfo 门禁明细表信息
     * @return 门禁明细表集合
     */
    @Override
    public List<EspDoorInfo> selectEspDoorInfoList(EspDoorInfo espDoorInfo) {
        return espDoorInfoMapper.selectEspDoorInfoList(espDoorInfo);
    }


    /**
     * 分页模糊查询门禁明细表列表
     *
     * @return 门禁明细表集合
     */
    @Override
    public Page selectEspDoorInfoListByLike(Query query) {
        EspDoorInfo espDoorInfo = BeanUtil.toBean(query.getCondition(), EspDoorInfo.class, CopyOptions.create());
        query.setRecords(espDoorInfoMapper.selectEspDoorInfoListByLike(query, espDoorInfo));
        return query;
    }

    /**
     * 新增门禁明细表
     *
     * @param espDoorInfo 门禁明细表信息
     * @return 结果
     */
    @Override
    public int insertEspDoorInfo(EspDoorInfo espDoorInfo) {
        return espDoorInfoMapper.insertEspDoorInfo(espDoorInfo);
    }

    /**
     * 修改门禁明细表
     *
     * @param espDoorInfo 门禁明细表信息
     * @return 结果
     */
    @Override
    public int updateEspDoorInfo(EspDoorInfo espDoorInfo) {
        return espDoorInfoMapper.updateEspDoorInfo(espDoorInfo);
    }


    /**
     * 删除门禁明细表
     *
     * @param id 门禁明细表ID
     * @return 结果
     */
    @Override
    public int deleteEspDoorInfoById(String id) {
        return espDoorInfoMapper.deleteEspDoorInfoById(id);
    }

    /**
     * 批量删除门禁明细表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEspDoorInfoByIds(Integer[] ids) {
        return espDoorInfoMapper.deleteEspDoorInfoByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertEspDoorInfoList(List<EspDoorInfo> list) {
        // 清空现有数据（注意：该操作不可逆）
        espDoorInfoMapper.truncateEspDoorInfo();

        // 分批次插入数据（每批50条，避免单次SQL数据量过大）
        int step = 50;
        for (int i = 0; i < list.size(); i += step) {
            // 计算当前批次的结束索引（防止越界）
            int endIndex = (i + step) < list.size() ? (i + step) : list.size();
            espDoorInfoMapper.insertEspDoorInfoList(list.subList(i, endIndex));
        }
    }

}

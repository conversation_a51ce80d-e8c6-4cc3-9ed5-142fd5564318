package com.huazheng.tunny.smarkpark.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import com.huazheng.tunny.smarkpark.mapper.SkPersonPassRecordMapper;
import com.huazheng.tunny.smarkpark.api.entity.SkPersonPassRecord;
import com.huazheng.tunny.smarkpark.service.SkPersonPassRecordService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import javax.annotation.Resource;
import java.util.List;

@Data
@Service("skPersonPassRecordService")
public class SkPersonPassRecordServiceImpl extends ServiceImpl<SkPersonPassRecordMapper, SkPersonPassRecord> implements SkPersonPassRecordService {

    @Resource
    private SkPersonPassRecordMapper skPersonPassRecordMapper;

    /**
     * 查询一卡通同步 - 过人记录信息
     *
     * @param id 一卡通同步 - 过人记录ID
     * @return 一卡通同步 - 过人记录信息
     */
    @Override
    public SkPersonPassRecord selectSkPersonPassRecordById(Integer id)
    {
        return skPersonPassRecordMapper.selectSkPersonPassRecordById(id);
    }

    /**
     * 查询一卡通同步 - 过人记录列表
     *
     * @param skPersonPassRecord 一卡通同步 - 过人记录信息
     * @return 一卡通同步 - 过人记录集合
     */
    @Override
    public List<SkPersonPassRecord> selectSkPersonPassRecordList(SkPersonPassRecord skPersonPassRecord)
    {
        return skPersonPassRecordMapper.selectSkPersonPassRecordList(skPersonPassRecord);
    }


    /**
     * 分页模糊查询一卡通同步 - 过人记录列表
     * @return 一卡通同步 - 过人记录集合
     */
    @Override
    public Page selectSkPersonPassRecordListByLike(Query query)
    {
        SkPersonPassRecord skPersonPassRecord =  BeanUtil.toBean(query.getCondition(), SkPersonPassRecord.class, CopyOptions.create());
        query.setRecords(skPersonPassRecordMapper.selectSkPersonPassRecordListByLike(query,skPersonPassRecord));
        return query;
    }

    /**
     * 新增一卡通同步 - 过人记录
     *
     * @param skPersonPassRecord 一卡通同步 - 过人记录信息
     * @return 结果
     */
    @Override
    public int insertSkPersonPassRecord(SkPersonPassRecord skPersonPassRecord)
    {
        return skPersonPassRecordMapper.insertSkPersonPassRecord(skPersonPassRecord);
    }

    /**
     * 修改一卡通同步 - 过人记录
     *
     * @param skPersonPassRecord 一卡通同步 - 过人记录信息
     * @return 结果
     */
    @Override
    public int updateSkPersonPassRecord(SkPersonPassRecord skPersonPassRecord)
    {
        return skPersonPassRecordMapper.updateSkPersonPassRecord(skPersonPassRecord);
    }


    /**
     * 删除一卡通同步 - 过人记录
     *
     * @param id 一卡通同步 - 过人记录ID
     * @return 结果
     */
    @Override
    public int deleteSkPersonPassRecordById(Integer id)
    {
        return skPersonPassRecordMapper.deleteSkPersonPassRecordById( id);
    };


    /**
     * 批量删除一卡通同步 - 过人记录对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteSkPersonPassRecordByIds(Integer[] ids)
    {
        return skPersonPassRecordMapper.deleteSkPersonPassRecordByIds( ids);
    }

    /**
     * 一卡通同步 - 过人记录
     * */
    @Override
    public void syncSkPersonPassRecord(List<SkPersonPassRecord> list) {
        int step = 50;
        for (int i = 0; i < list.size(); i += step) {
            skPersonPassRecordMapper.syncSkPersonPassRecord(list.subList(i, (i + step) < list.size() ? (i + step) : list.size()));
        }
    }

}

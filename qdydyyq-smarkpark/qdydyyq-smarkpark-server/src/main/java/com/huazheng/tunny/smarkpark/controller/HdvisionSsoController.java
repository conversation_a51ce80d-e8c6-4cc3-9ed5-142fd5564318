package com.huazheng.tunny.smarkpark.controller;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.log.annotation.SysLog;
import com.huazheng.tunny.smarkpark.api.dto.Notificationlist;
import com.huazheng.tunny.smarkpark.api.entity.SkGateList;
import com.huazheng.tunny.smarkpark.api.entity.SkPersonPassRecord;
import com.huazheng.tunny.smarkpark.api.entity.SkVehicleAlarmRecord;
import com.huazheng.tunny.smarkpark.api.entity.SkVehiclePassRecord;
import com.huazheng.tunny.smarkpark.enums.NotificationsEnum;
import com.huazheng.tunny.smarkpark.service.SkGateListService;
import com.huazheng.tunny.smarkpark.service.SkPersonPassRecordService;
import com.huazheng.tunny.smarkpark.service.SkVehicleAlarmRecordService;
import com.huazheng.tunny.smarkpark.service.SkVehiclePassRecordService;
import com.huazheng.tunny.smarkpark.util.HikvisionUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 海康SSO单点登录
 * */
@RestController
@Slf4j
@Data
public class HdvisionSsoController {

    @Resource
    private HikvisionUtil hdvisionUtil;

    @Value("${artemisConfig.sso.userCode}")
    private String userCode;
    @Value("${artemisConfig.sso.serviceUrl}")
    private String serviceUrl;
    @Value("${artemisConfig.sso.language}")
    private String language;
    @Value("${artemisConfig.sso.jumpUrl}")
    private String jumpUrl;

    /**
     * SSO单点登录
     * */
    @SysLog("海康SSO单点登录")
    @GetMapping("/hk/ssoLogin")
    public R ssoLogin() {
        JSONObject body = new JSONObject();
        body.put("userCode", userCode);
        body.put("service", serviceUrl);
        body.put("language", language);
        String resStr = hdvisionUtil.doGetArtemis(HikvisionUtil.ssoUrl, body);
        JSONObject res = JSON.parseObject(resStr);
        String code = res.get("code").toString();
        String trueCode = "0";
        if (trueCode.equals(code)) {
            String token = res.getJSONObject("data").getString("token");
            return new R(Boolean.TRUE, null, jumpUrl + "?token=" + token + "&service=" + serviceUrl);
        } else {
            return new R(Boolean.FALSE, null, resStr);
        }
    }

}

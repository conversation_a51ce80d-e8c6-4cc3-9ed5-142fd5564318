package com.huazheng.tunny.smarkpark.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.smarkpark.api.entity.ZhoujieDevice;
import com.huazheng.tunny.smarkpark.mapper.ZhoujieDeviceMapper;
import com.huazheng.tunny.smarkpark.mapper.ZhoujieZoneMapper;
import com.huazheng.tunny.smarkpark.api.entity.ZhoujieZone;
import com.huazheng.tunny.smarkpark.service.ZhoujieZoneService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Service("zhoujieZoneService")
public class ZhoujieZoneServiceImpl extends ServiceImpl<ZhoujieZoneMapper, ZhoujieZone> implements ZhoujieZoneService {

    @Autowired
    private ZhoujieZoneMapper zhoujieZoneMapper;
    @Autowired
    private ZhoujieDeviceMapper zhoujieDeviceMapper;

    // 平台域名
    @Value("${tcp.zoneUrl:http://localhost:19991/api/v1/zhoujie/zonesList}")
    private String zoneUrl;

    /**
     * 查询周界电子围栏防区信息
     *
     * @param id 周界电子围栏防区ID
     * @return 周界电子围栏防区信息
     */
    @Override
    public ZhoujieZone selectZhoujieZoneById(Long id) {
        return zhoujieZoneMapper.selectZhoujieZoneById(id);
    }

    /**
     * 查询周界电子围栏防区列表
     *
     * @param zhoujieZone 周界电子围栏防区信息
     * @return 周界电子围栏防区集合
     */
    @Override
    public List<ZhoujieZone> selectZhoujieZoneList(ZhoujieZone zhoujieZone) {
        return zhoujieZoneMapper.selectZhoujieZoneList(zhoujieZone);
    }


    /**
     * 分页模糊查询周界电子围栏防区列表
     *
     * @return 周界电子围栏防区集合
     */
    @Override
    public Page selectZhoujieZoneListByLike(Query query) {
        ZhoujieZone zhoujieZone = BeanUtil.toBean(query.getCondition(), ZhoujieZone.class, CopyOptions.create());
        query.setRecords(zhoujieZoneMapper.selectZhoujieZoneListByLike(query, zhoujieZone));
        return query;
    }

    /**
     * 新增周界电子围栏防区
     *
     * @param zhoujieZone 周界电子围栏防区信息
     * @return 结果
     */
    @Override
    public int insertZhoujieZone(ZhoujieZone zhoujieZone) {
        return zhoujieZoneMapper.insertZhoujieZone(zhoujieZone);
    }

    /**
     * 修改周界电子围栏防区
     *
     * @param zhoujieZone 周界电子围栏防区信息
     * @return 结果
     */
    @Override
    public int updateZhoujieZone(ZhoujieZone zhoujieZone) {
        return zhoujieZoneMapper.updateZhoujieZone(zhoujieZone);
    }


    /**
     * 删除周界电子围栏防区
     *
     * @param id 周界电子围栏防区ID
     * @return 结果
     */
    @Override
    public int deleteZhoujieZoneById(Long id) {
        return zhoujieZoneMapper.deleteZhoujieZoneById(id);
    }

    /**
     * 批量删除周界电子围栏防区对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteZhoujieZoneByIds(Integer[] ids) {
        return zhoujieZoneMapper.deleteZhoujieZoneByIds(ids);
    }

    @Override
    public R cronZoneList(ZhoujieZone zhoujieZone) {
        try {
            ZhoujieDevice zhoujieDevice = new ZhoujieDevice();
            zhoujieDevice.setDeviceId(zhoujieZone.getDeviceId());
            zhoujieDevice = zhoujieDeviceMapper.selectOne(zhoujieDevice);
            if (zhoujieDevice == null) {
                return R.error("设备不存在");
            }
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("deviceId", zhoujieZone.getDeviceId());
            paramMap.put("deviceType", zhoujieDevice.getDeviceType());
            String str = HttpRequest.post(zoneUrl).body(JSONUtil.toJsonStr(paramMap)).execute().body();
            JSONObject jsonObject = JSONUtil.parseObj(str);
            String successCode = "0";
            String code = jsonObject.getStr("code");
            if (successCode.equals(code)) {
                return R.success();
            }
            return R.error(jsonObject.getStr("msg"));
        } catch (Exception e) {
            e.printStackTrace();
            return R.error("操作失败,失败原因：" + e.getMessage());
        }
    }

}

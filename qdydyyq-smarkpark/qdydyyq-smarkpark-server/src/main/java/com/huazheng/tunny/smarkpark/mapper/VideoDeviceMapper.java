package com.huazheng.tunny.smarkpark.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.smarkpark.api.dto.RegionsAndCamerasDto;
import com.huazheng.tunny.smarkpark.api.entity.SkGateList;
import com.huazheng.tunny.smarkpark.api.entity.SkHdvisionCameras;
import com.huazheng.tunny.smarkpark.api.entity.SkHdvisionCamerasOnline;
import com.huazheng.tunny.smarkpark.api.entity.SkHdvisionRegions;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 视频设备管理  mapper层
 *
 * <AUTHOR> code generator
 * @date 2024-07-08 15:47:11
 */
public interface VideoDeviceMapper {

    /**
     * 截断区域表
     * */
    void truncateRegions();

    /**
     * 插入区域表
     * */
    void syncRegions(@Param("list") List<SkHdvisionRegions> list);

    /**
     * 截断监控点表
     * */
    void truncateCameras();

    /**
     * 插入监控点表
     * */
    void syncCameras(@Param("list") List<SkHdvisionCameras> list);

    /**
     * 截断监控点表
     * */
    void truncateCamerasOnline();

    /**
     * 插入监控点表
     * */
    void syncCamerasOnline(@Param("list") List<SkHdvisionCamerasOnline> list);

    /**
     * 区域&监控点列表
     * */
    List<RegionsAndCamerasDto> regionsAndCamerasList();

    /**
     * 监控点列表
     * */
    List<Map<String, Object>> camerasList();
}

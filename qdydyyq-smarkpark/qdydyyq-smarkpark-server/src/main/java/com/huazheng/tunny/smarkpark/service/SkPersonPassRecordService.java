package com.huazheng.tunny.smarkpark.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.smarkpark.api.entity.SkPersonPassRecord;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 一卡通同步 - 过人记录 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2024-07-08 15:47:07
 */
public interface SkPersonPassRecordService extends IService<SkPersonPassRecord> {
    /**
     * 查询一卡通同步 - 过人记录信息
     *
     * @param id 一卡通同步 - 过人记录ID
     * @return 一卡通同步 - 过人记录信息
     */
    public SkPersonPassRecord selectSkPersonPassRecordById(Integer id);

    /**
     * 查询一卡通同步 - 过人记录列表
     *
     * @param skPersonPassRecord 一卡通同步 - 过人记录信息
     * @return 一卡通同步 - 过人记录集合
     */
    public List<SkPersonPassRecord> selectSkPersonPassRecordList(SkPersonPassRecord skPersonPassRecord);


    /**
     * 分页模糊查询一卡通同步 - 过人记录列表
     * @return 一卡通同步 - 过人记录集合
     */
    public Page selectSkPersonPassRecordListByLike(Query query);



    /**
     * 新增一卡通同步 - 过人记录
     *
     * @param skPersonPassRecord 一卡通同步 - 过人记录信息
     * @return 结果
     */
    public int insertSkPersonPassRecord(SkPersonPassRecord skPersonPassRecord);

    /**
     * 修改一卡通同步 - 过人记录
     *
     * @param skPersonPassRecord 一卡通同步 - 过人记录信息
     * @return 结果
     */
    public int updateSkPersonPassRecord(SkPersonPassRecord skPersonPassRecord);

    /**
     * 删除一卡通同步 - 过人记录
     *
     * @param id 一卡通同步 - 过人记录ID
     * @return 结果
     */
    public int deleteSkPersonPassRecordById(Integer id);

    /**
     * 批量删除一卡通同步 - 过人记录
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteSkPersonPassRecordByIds(Integer[] ids);

    /**
     * 一卡通同步 - 过人记录
     * */
    void syncSkPersonPassRecord(List<SkPersonPassRecord> list);
}


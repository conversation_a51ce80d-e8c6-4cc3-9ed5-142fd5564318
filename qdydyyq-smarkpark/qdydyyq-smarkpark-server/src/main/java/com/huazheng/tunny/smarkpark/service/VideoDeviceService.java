package com.huazheng.tunny.smarkpark.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.smarkpark.api.dto.RegionsAndCamerasDto;
import com.huazheng.tunny.smarkpark.api.entity.SkGateList;
import com.huazheng.tunny.smarkpark.api.entity.SkHdvisionCameras;
import com.huazheng.tunny.smarkpark.api.entity.SkHdvisionCamerasOnline;
import com.huazheng.tunny.smarkpark.api.entity.SkHdvisionRegions;

import java.util.List;
import java.util.Map;

/**
 * 视频设备管理
 *
 * <AUTHOR> code generator
 * @date 2024-07-08 15:47:11
 */
public interface VideoDeviceService {

    void syncRegions(List<SkHdvisionRegions> list);

    void syncCameras(List<SkHdvisionCameras> list);

    void syncCamerasOnline(List<SkHdvisionCamerasOnline> list);

    List<RegionsAndCamerasDto> regionsAndCamerasList();

    List<Map<String, Object>> camerasList();
}


package com.huazheng.tunny.smarkpark.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.smarkpark.api.dto.RegionsAndCamerasDto;
import com.huazheng.tunny.smarkpark.api.entity.SkGateList;
import com.huazheng.tunny.smarkpark.api.entity.SkHdvisionCameras;
import com.huazheng.tunny.smarkpark.api.entity.SkHdvisionCamerasOnline;
import com.huazheng.tunny.smarkpark.api.entity.SkHdvisionRegions;
import com.huazheng.tunny.smarkpark.mapper.SkGateListMapper;
import com.huazheng.tunny.smarkpark.mapper.VideoDeviceMapper;
import com.huazheng.tunny.smarkpark.service.SkGateListService;
import com.huazheng.tunny.smarkpark.service.VideoDeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service("videoDeviceService")
public class VideoDeviceServiceImpl implements VideoDeviceService {

    @Resource
    private VideoDeviceMapper videoDeviceMapper;

    @Override
    public void syncRegions(List<SkHdvisionRegions> list) {
        videoDeviceMapper.truncateRegions();
        int step = 50;
        for (int i = 0; i < list.size(); i += step) {
            videoDeviceMapper.syncRegions(list.subList(i, (i + step) < list.size() ? (i + step) : list.size()));
        }
    }

    @Override
    public void syncCameras(List<SkHdvisionCameras> list) {
        videoDeviceMapper.truncateCameras();
        int step = 50;
        for (int i = 0; i < list.size(); i += step) {
            videoDeviceMapper.syncCameras(list.subList(i, (i + step) < list.size() ? (i + step) : list.size()));
        }
    }

    @Override
    public void syncCamerasOnline(List<SkHdvisionCamerasOnline> list) {
        videoDeviceMapper.truncateCamerasOnline();
        int step = 50;
        for (int i = 0; i < list.size(); i += step) {
            videoDeviceMapper.syncCamerasOnline(list.subList(i, (i + step) < list.size() ? (i + step) : list.size()));
        }
    }

    @Override
    public List<RegionsAndCamerasDto> regionsAndCamerasList() {
        List<RegionsAndCamerasDto> list = videoDeviceMapper.regionsAndCamerasList();
        return this.buildTree(list);
    }

    @Override
    public List<Map<String, Object>> camerasList() {
        return videoDeviceMapper.camerasList();
    }

    private List<RegionsAndCamerasDto> buildTree(List<RegionsAndCamerasDto> allItems) {
        // 创建一个根节点列表和一个映射，用于快速查找父节点
        List<RegionsAndCamerasDto> roots = new ArrayList<>();
        Map<String, RegionsAndCamerasDto> indexCodeToNodeMap = new HashMap<>();

        // 遍历所有项，填充映射表
        for (RegionsAndCamerasDto item : allItems) {
            indexCodeToNodeMap.put(item.getIndexCode(), item);
        }

        // 为每个项找到其父节点并添加到对应的子节点列表中
        for (RegionsAndCamerasDto item : allItems) {
            RegionsAndCamerasDto parentNode = indexCodeToNodeMap.get(item.getParentIndexCode());
            if (parentNode == null) {
                // 如果没有父节点，则认为是根节点
                roots.add(item);
            } else {
                if (parentNode.getChildren() == null) {
                    parentNode.setChildren(new ArrayList<>());
                }
                // 添加到子节点列表，并确保按sort排序
                parentNode.getChildren().add(item);
                parentNode.getChildren().sort(Comparator.comparingLong(RegionsAndCamerasDto::getSort));
            }
        }

        return roots;
    }
}
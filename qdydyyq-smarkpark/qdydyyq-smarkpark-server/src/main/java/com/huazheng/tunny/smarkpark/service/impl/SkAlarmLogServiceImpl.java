package com.huazheng.tunny.smarkpark.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.StrUtil;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.smarkpark.mapper.SkAlarmLogMapper;
import com.huazheng.tunny.smarkpark.api.entity.SkAlarmLog;
import com.huazheng.tunny.smarkpark.service.SkAlarmLogService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Data
@Slf4j
@Service("skAlarmLogService")
public class SkAlarmLogServiceImpl extends ServiceImpl<SkAlarmLogMapper, SkAlarmLog> implements SkAlarmLogService {

    @Resource
    private SkAlarmLogMapper skAlarmLogMapper;

    private static final String ALARM_SOURCE_BIGSCREEN = "0";

    /**
     * 分页模糊查询告警记录列表
     *
     * @return 告警记录集合
     */
    @Override
    public Page selectSkAlarmLogListByLike(Query query) {
        SkAlarmLog skAlarmLog = BeanUtil.toBean(query.getCondition(), SkAlarmLog.class, CopyOptions.create());
        if (ALARM_SOURCE_BIGSCREEN.equals(skAlarmLog.getAlarmSource().toString()) && StrUtil.isNotBlank(skAlarmLog.getSrcIndex())
                && skAlarmLog.getSrcIndex().indexOf("-") < 0) {
            skAlarmLog.setClientType("bigScreen");
        }
        query.setRecords(skAlarmLogMapper.selectSkAlarmLogListByLike(query, skAlarmLog));
        return query;
    }


    @Override
    public void handleBatch(SkAlarmLog skAlarmLog) {
        skAlarmLogMapper.handleBatch(skAlarmLog);
    }

    @Override
    public Integer getAlarmNotifyCount(String userName) {
        return skAlarmLogMapper.getAlarmNotifyCount(userName);
    }

    @Override
    public List<Map<String, Object>> alarmClassification(Map<String, Object> params) {
        return skAlarmLogMapper.alarmClassification(params);
    }

    @Override
    public List<Map<String, Object>> alarmStatistics(Map<String, Object> params) {
        return skAlarmLogMapper.alarmStatistics(params);
    }

    @Override
    public List<Map<String, Object>> alarmTrend(int year, String alarmSource) {
        return skAlarmLogMapper.alarmTrend(year, alarmSource);
    }

    @Override
    public List<String> selectMailsByUsers(List<String> users) {
        return skAlarmLogMapper.selectMailsByUsers(users);
    }

    @Override
    public SkAlarmLog selectSkAlarmLogById(String eventId) {
        return skAlarmLogMapper.selectSkAlarmLogById(eventId);
    }

    @Override
    public String getFireControlDeviceName(String deviceCode) {
        return skAlarmLogMapper.getFireControlDeviceName(deviceCode);
    }

    /**
     * 迁移30天前的告警日志到备份表
     */
    @Override
    public void transferDataToHisTable(Integer days) {
        skAlarmLogMapper.transferDataToHisTable(days);
    }

    /**
     * 清理30天前的告警日志
     */
    @Override
    public void cleanUpAlarmLog(Integer days) {
        skAlarmLogMapper.cleanUpAlarmLog(days);
    }

    @Override
    public List<Map<String, Object>> alarmLevel(Map<String, Object> params) {
        return skAlarmLogMapper.alarmLevel(params);
    }

    @Override
    public Integer getAlarmNotifyType(String userName) {
        Integer alarmNotifyType = skAlarmLogMapper.getAlarmNotifyType(userName);
        return alarmNotifyType;
    }

}

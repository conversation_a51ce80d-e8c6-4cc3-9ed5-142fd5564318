package com.huazheng.tunny.smarkpark.mapper;

import com.huazheng.tunny.smarkpark.api.entity.SkCockpitMapPoints;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;
import java.util.Map;

/**
 * 驾驶舱地图监控点位  mapper层
 *
 * <AUTHOR> code generator
 * @date 2024-07-11 14:24:41
 */
public interface SkCockpitMapPointsMapper extends BaseMapper<SkCockpitMapPoints> {
    /**
     * 查询驾驶舱地图监控点位信息
     *
     * @param indexCode 驾驶舱地图监控点位ID
     * @return 驾驶舱地图监控点位信息
     */
    public SkCockpitMapPoints selectSkCockpitMapPointsById(String indexCode);

    /**
     * 查询驾驶舱地图监控点位列表
     *
     * @param skCockpitMapPoints 驾驶舱地图监控点位信息
     * @return 驾驶舱地图监控点位集合
     */
    public List<SkCockpitMapPoints> selectSkCockpitMapPointsList(SkCockpitMapPoints skCockpitMapPoints);

    /**
     * 模糊查询驾驶舱地图监控点位列表
     *
     * @param skCockpitMapPoints 驾驶舱地图监控点位信息
     * @return 驾驶舱地图监控点位集合
     */
    public List<SkCockpitMapPoints> selectSkCockpitMapPointsListByLike(SkCockpitMapPoints skCockpitMapPoints);


    /**
     * 分页模糊查询驾驶舱地图监控点位列表
     *
     * @param skCockpitMapPoints 驾驶舱地图监控点位信息
     * @return 驾驶舱地图监控点位集合
     */
    public List<SkCockpitMapPoints> selectSkCockpitMapPointsListByLike(Query query, SkCockpitMapPoints skCockpitMapPoints);


    /**
     * 新增驾驶舱地图监控点位
     *
     * @param skCockpitMapPoints 驾驶舱地图监控点位信息
     * @return 结果
     */
    public int insertSkCockpitMapPoints(SkCockpitMapPoints skCockpitMapPoints);

    /**
     * 修改驾驶舱地图监控点位
     *
     * @param skCockpitMapPoints 驾驶舱地图监控点位信息
     * @return 结果
     */
    public int updateSkCockpitMapPoints(SkCockpitMapPoints skCockpitMapPoints);

    /**
     * 删除驾驶舱地图监控点位
     *
     * @param indexCode 驾驶舱地图监控点位ID
     * @return 结果
     */
    public int deleteSkCockpitMapPointsById(String indexCode);

    /**
     * 批量删除驾驶舱地图监控点位
     *
     * @param indexCodes 需要删除的数据ID
     * @return 结果
     */
    public int deleteSkCockpitMapPointsByIds(Integer[] indexCodes);


    Map<String, Object> camerasStatistics();

    List<Map<String, Object>> camerasOnlineRate();

    Map<String, Object> fireFightEquipmentStatistics();

    List<Map<String, Object>> fireFightEquipmentOnlineRate();
}

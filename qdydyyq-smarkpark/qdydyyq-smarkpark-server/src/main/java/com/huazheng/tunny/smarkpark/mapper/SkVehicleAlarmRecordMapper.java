package com.huazheng.tunny.smarkpark.mapper;

import com.huazheng.tunny.smarkpark.api.entity.SkVehicleAlarmRecord;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.huazheng.tunny.common.core.util.Query;
import java.util.List;
/**
 * 一卡通同步 - 车辆告警记录  mapper层
 *
 * <AUTHOR> code generator
 * @date 2024-07-08 15:47:03
 */
public interface SkVehicleAlarmRecordMapper extends BaseMapper<SkVehicleAlarmRecord> {
    /**
     * 查询一卡通同步 - 车辆告警记录信息
     *
     * @param id 一卡通同步 - 车辆告警记录ID
     * @return 一卡通同步 - 车辆告警记录信息
     */
    public SkVehicleAlarmRecord selectSkVehicleAlarmRecordById(Integer id);

    /**
     * 查询一卡通同步 - 车辆告警记录列表
     *
     * @param skVehicleAlarmRecord 一卡通同步 - 车辆告警记录信息
     * @return 一卡通同步 - 车辆告警记录集合
     */
    public List<SkVehicleAlarmRecord> selectSkVehicleAlarmRecordList(SkVehicleAlarmRecord skVehicleAlarmRecord);

    /**
     * 模糊查询一卡通同步 - 车辆告警记录列表
     *
     * @param skVehicleAlarmRecord 一卡通同步 - 车辆告警记录信息
     * @return 一卡通同步 - 车辆告警记录集合
     */
    public List<SkVehicleAlarmRecord> selectSkVehicleAlarmRecordListByLike(SkVehicleAlarmRecord skVehicleAlarmRecord);


    /**
     * 分页模糊查询一卡通同步 - 车辆告警记录列表
     *
     * @param skVehicleAlarmRecord 一卡通同步 - 车辆告警记录信息
     * @return 一卡通同步 - 车辆告警记录集合
     */
    public List<SkVehicleAlarmRecord> selectSkVehicleAlarmRecordListByLike(Query query, SkVehicleAlarmRecord skVehicleAlarmRecord);


    /**
     * 新增一卡通同步 - 车辆告警记录
     *
     * @param skVehicleAlarmRecord 一卡通同步 - 车辆告警记录信息
     * @return 结果
     */
    public int insertSkVehicleAlarmRecord(SkVehicleAlarmRecord skVehicleAlarmRecord);

    /**
     * 修改一卡通同步 - 车辆告警记录
     *
     * @param skVehicleAlarmRecord 一卡通同步 - 车辆告警记录信息
     * @return 结果
     */
    public int updateSkVehicleAlarmRecord(SkVehicleAlarmRecord skVehicleAlarmRecord);

    /**
     * 删除一卡通同步 - 车辆告警记录
     *
     * @param id 一卡通同步 - 车辆告警记录ID
     * @return 结果
     */
    public int deleteSkVehicleAlarmRecordById(Integer id);

    /**
     * 批量删除一卡通同步 - 车辆告警记录
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteSkVehicleAlarmRecordByIds(Integer[] ids);

    /**
     * 一卡通同步 - 车辆告警记录
     * */
    void syncSkVehicleAlarmRecord(List<SkVehicleAlarmRecord> list);
}

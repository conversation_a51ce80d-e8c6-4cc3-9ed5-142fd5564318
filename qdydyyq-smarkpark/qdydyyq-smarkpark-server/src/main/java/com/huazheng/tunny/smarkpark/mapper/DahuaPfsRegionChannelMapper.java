package com.huazheng.tunny.smarkpark.mapper;

import com.huazheng.tunny.smarkpark.api.entity.DahuaPfsRegionChannel;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.huazheng.tunny.common.core.util.Query;
import java.util.List;
/**
 * 大华客流统计区域配置-通道表  mapper层
 *
 * <AUTHOR>
 * @date 2025-05-20 17:44:55
 */
public interface DahuaPfsRegionChannelMapper extends BaseMapper<DahuaPfsRegionChannel> {
    /**
     * 查询大华客流统计区域配置-通道表信息
     *
     * @param id 大华客流统计区域配置-通道表ID
     * @return 大华客流统计区域配置-通道表信息
     */
    public DahuaPfsRegionChannel selectDahuaPfsRegionChannelById(Long id);

    /**
     * 查询大华客流统计区域配置-通道表列表
     *
     * @param dahuaPfsRegionChannel 大华客流统计区域配置-通道表信息
     * @return 大华客流统计区域配置-通道表集合
     */
    public List<DahuaPfsRegionChannel> selectDahuaPfsRegionChannelList(DahuaPfsRegionChannel dahuaPfsRegionChannel);

    /**
     * 模糊查询大华客流统计区域配置-通道表列表
     *
     * @param dahuaPfsRegionChannel 大华客流统计区域配置-通道表信息
     * @return 大华客流统计区域配置-通道表集合
     */
    public List<DahuaPfsRegionChannel> selectDahuaPfsRegionChannelListByLike(DahuaPfsRegionChannel dahuaPfsRegionChannel);


    /**
     * 分页模糊查询大华客流统计区域配置-通道表列表
     *
     * @param dahuaPfsRegionChannel 大华客流统计区域配置-通道表信息
     * @return 大华客流统计区域配置-通道表集合
     */
    public List<DahuaPfsRegionChannel> selectDahuaPfsRegionChannelListByLike(Query query, DahuaPfsRegionChannel dahuaPfsRegionChannel);


    /**
     * 新增大华客流统计区域配置-通道表
     *
     * @param dahuaPfsRegionChannel 大华客流统计区域配置-通道表信息
     * @return 结果
     */
    public int insertDahuaPfsRegionChannel(DahuaPfsRegionChannel dahuaPfsRegionChannel);

    /**
     * 修改大华客流统计区域配置-通道表
     *
     * @param dahuaPfsRegionChannel 大华客流统计区域配置-通道表信息
     * @return 结果
     */
    public int updateDahuaPfsRegionChannel(DahuaPfsRegionChannel dahuaPfsRegionChannel);

    /**
     * 删除大华客流统计区域配置-通道表
     *
     * @param id 大华客流统计区域配置-通道表ID
     * @return 结果
     */
    public int deleteDahuaPfsRegionChannelById(Long id);

    /**
     * 批量删除大华客流统计区域配置-通道表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteDahuaPfsRegionChannelByIds(Integer[] ids);


    void truncateDahuaPfsRegionChannel();

    void insertDahuaPfsRegionChannelList(List<DahuaPfsRegionChannel> dahuaPfsRegionChannels);
}

package com.huazheng.tunny.smarkpark.job;

import cn.hutool.core.date.DateUtil;
import com.huazheng.tunny.smarkpark.service.SkBuildControlDeviceService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class LinYunJobHandler {
    @Resource
    private SkBuildControlDeviceService skBuildControlDeviceService;

    /**
     * 同步邻元设备状态
     */
    @XxlJob("syncLinYunDeviceStatusJobHandler")
    public ReturnT<String> syncLinYunDeviceStatusJobHandler(String param) throws Exception {
        log.info("同步邻元设备状态开始 " + DateUtil.now());
        skBuildControlDeviceService.syncDeviceStatus();
        log.info("同步邻元设备状态结束 " + DateUtil.now());
        return ReturnT.SUCCESS;
    }

    /**
     * 同步照明设备
     */
    @XxlJob("syncLightingDeviceJobHandler")
    public ReturnT<String> syncLightingDeviceJobHandler(String param) throws Exception {
        log.info("同步照明设备开始 " + DateUtil.now());
        skBuildControlDeviceService.syncLightingDevice();
        log.info("同步照明设备结束 " + DateUtil.now());
        return ReturnT.SUCCESS;
    }

    /**
     * 同步邻元设备事件
     */
    @XxlJob("syncLinYunDeviceEventJobHandler")
    public ReturnT<String> syncLinYunDeviceEventJobHandler(String param) throws Exception {
        log.info("同步邻元设备事件开始 " + DateUtil.now());
        skBuildControlDeviceService.syncDeviceEvent();
        log.info("同步邻元设备事件结束 " + DateUtil.now());
        return ReturnT.SUCCESS;
    }
}

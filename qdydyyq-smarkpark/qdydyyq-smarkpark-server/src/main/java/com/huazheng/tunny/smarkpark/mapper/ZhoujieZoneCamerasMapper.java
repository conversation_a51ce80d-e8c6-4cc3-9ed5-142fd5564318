package com.huazheng.tunny.smarkpark.mapper;

import com.huazheng.tunny.smarkpark.api.dto.DaHuaCamerasDTO;
import com.huazheng.tunny.smarkpark.api.entity.ZhoujieZoneCameras;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.huazheng.tunny.common.core.util.Query;
import java.util.List;
/**
 * 周界电子围栏设备防区关联摄像头  mapper层
 *
 * <AUTHOR>
 * @date 2025-05-21 16:12:28
 */
public interface ZhoujieZoneCamerasMapper extends BaseMapper<ZhoujieZoneCameras> {
    /**
     * 查询周界电子围栏设备防区关联摄像头信息
     *
     * @param id 周界电子围栏设备防区关联摄像头ID
     * @return 周界电子围栏设备防区关联摄像头信息
     */
    public ZhoujieZoneCameras selectZhoujieZoneCamerasById(Long id);

    /**
     * 查询周界电子围栏设备防区关联摄像头列表
     *
     * @param zhoujieZoneCameras 周界电子围栏设备防区关联摄像头信息
     * @return 周界电子围栏设备防区关联摄像头集合
     */
    public List<ZhoujieZoneCameras> selectZhoujieZoneCamerasList(ZhoujieZoneCameras zhoujieZoneCameras);

    /**
     * 模糊查询周界电子围栏设备防区关联摄像头列表
     *
     * @param zhoujieZoneCameras 周界电子围栏设备防区关联摄像头信息
     * @return 周界电子围栏设备防区关联摄像头集合
     */
    public List<ZhoujieZoneCameras> selectZhoujieZoneCamerasListByLike(ZhoujieZoneCameras zhoujieZoneCameras);


    /**
     * 分页模糊查询周界电子围栏设备防区关联摄像头列表
     *
     * @param zhoujieZoneCameras 周界电子围栏设备防区关联摄像头信息
     * @return 周界电子围栏设备防区关联摄像头集合
     */
    public List<DaHuaCamerasDTO> selectZhoujieZoneCamerasListByLike(Query query, DaHuaCamerasDTO zhoujieZoneCameras);


    /**
     * 新增周界电子围栏设备防区关联摄像头
     *
     * @param zhoujieZoneCameras 周界电子围栏设备防区关联摄像头信息
     * @return 结果
     */
    public int insertZhoujieZoneCameras(ZhoujieZoneCameras zhoujieZoneCameras);

    /**
     * 修改周界电子围栏设备防区关联摄像头
     *
     * @param zhoujieZoneCameras 周界电子围栏设备防区关联摄像头信息
     * @return 结果
     */
    public int updateZhoujieZoneCameras(ZhoujieZoneCameras zhoujieZoneCameras);

    /**
     * 删除周界电子围栏设备防区关联摄像头
     *
     * @param id 周界电子围栏设备防区关联摄像头ID
     * @return 结果
     */
    public int deleteZhoujieZoneCamerasById(Long id);

    /**
     * 批量删除周界电子围栏设备防区关联摄像头
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteZhoujieZoneCamerasByIds(Integer[] ids);


    void deleteZhoujieZoneCameras(ZhoujieZoneCameras zhoujieZoneCameras);
}

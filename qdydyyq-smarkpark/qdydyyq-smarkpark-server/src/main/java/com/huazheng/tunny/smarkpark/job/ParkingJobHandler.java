package com.huazheng.tunny.smarkpark.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.huazheng.tunny.smarkpark.api.entity.ParkingBarrierGate;
import com.huazheng.tunny.smarkpark.api.entity.ParkingVehicleRecord;
import com.huazheng.tunny.smarkpark.service.ParkingBarrierGateService;
import com.huazheng.tunny.smarkpark.service.ParkingSourceService;
import com.huazheng.tunny.smarkpark.service.ParkingVehicleRecordService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

/**
 * 科拓智慧人行平台数据同步定时任务
 *
 * <AUTHOR>
 * @date 2025/5/6
 */
@Slf4j
@Component
public class ParkingJobHandler {
    @Resource
    private ParkingSourceService parkingSourceService;
    @Resource
    private ParkingBarrierGateService parkingBarrierGateService;
    @Resource
    private ParkingVehicleRecordService parkingVehicleRecordService;

    /**
     * 同步车辆闸机信息
     * <p>
     * 每一小时同步一次
     */
    @XxlJob("syncParkingBarrierGateJobHandler")
    public ReturnT<String> syncParkingBarrierGateJobHandler(String param) throws Exception {
        log.info("同步停车场通道信息开始 " + DateUtil.now());
        List<ParkingBarrierGate> list = parkingSourceService.cronParkingBarrierGate(new HashMap<>());
        if (CollUtil.isNotEmpty(list)) {
            parkingBarrierGateService.cronChannelsList(list);
        }
        parkingSourceService.selectParkingSpacesNum();
        log.info("同步停车场通道信息成功 " + DateUtil.now());
        return ReturnT.SUCCESS;
    }

    /**
     * 车辆通行记录同步
     * <p>
     * 每5分钟同步一次
     */
    @XxlJob("syncVehicleRecordJobHandler")
    public ReturnT<String> syncVehicleRecordJobHandler(String param) throws Exception {
        log.info("同步车辆通行记录开始 " + DateUtil.now());
        List<ParkingVehicleRecord> list = parkingSourceService.cronVehicleRecord(new HashMap<>());
        parkingVehicleRecordService.conVehicleRecord(list);
        log.info("同步车辆通行记录成功 " + DateUtil.now());
        return ReturnT.SUCCESS;
    }
}

package com.huazheng.tunny.smarkpark.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import com.huazheng.tunny.smarkpark.api.entity.EspDoorDevice;
import com.huazheng.tunny.smarkpark.mapper.EspDoorDeviceMapper;
import com.huazheng.tunny.smarkpark.service.EspDoorDeviceService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Data
@Service("espDoorDeviceService")
public class EspDoorDeviceServiceImpl extends ServiceImpl<EspDoorDeviceMapper, EspDoorDevice> implements EspDoorDeviceService {

    @Autowired
    private EspDoorDeviceMapper espDoorDeviceMapper;

    /**
     * 查询【科拓】门禁设备表信息
     *
     * @param id 【科拓】门禁设备表ID
     * @return 【科拓】门禁设备表信息
     */
    @Override
    public EspDoorDevice selectEspDoorDeviceById(String id) {
        return espDoorDeviceMapper.selectEspDoorDeviceById(id);
    }

    /**
     * 查询【科拓】门禁设备表列表
     *
     * @param espDoorDevice 【科拓】门禁设备表信息
     * @return 【科拓】门禁设备表集合
     */
    @Override
    public List<EspDoorDevice> selectEspDoorDeviceList(EspDoorDevice espDoorDevice) {
        return espDoorDeviceMapper.selectEspDoorDeviceList(espDoorDevice);
    }


    /**
     * 分页模糊查询【科拓】门禁设备表列表
     *
     * @return 【科拓】门禁设备表集合
     */
    @Override
    public Page selectEspDoorDeviceListByLike(Query query) {
        EspDoorDevice espDoorDevice = BeanUtil.toBean(query.getCondition(), EspDoorDevice.class, CopyOptions.create());
        query.setRecords(espDoorDeviceMapper.selectEspDoorDeviceListByLike(query, espDoorDevice));
        return query;
    }

    /**
     * 新增【科拓】门禁设备表
     *
     * @param espDoorDevice 【科拓】门禁设备表信息
     * @return 结果
     */
    @Override
    public int insertEspDoorDevice(EspDoorDevice espDoorDevice) {
        return espDoorDeviceMapper.insertEspDoorDevice(espDoorDevice);
    }

    /**
     * 修改【科拓】门禁设备表
     *
     * @param espDoorDevice 【科拓】门禁设备表信息
     * @return 结果
     */
    @Override
    public int updateEspDoorDevice(EspDoorDevice espDoorDevice) {
        return espDoorDeviceMapper.updateEspDoorDevice(espDoorDevice);
    }


    /**
     * 删除【科拓】门禁设备表
     *
     * @param id 【科拓】门禁设备表ID
     * @return 结果
     */
    @Override
    public int deleteEspDoorDeviceById(String id) {
        return espDoorDeviceMapper.deleteEspDoorDeviceById(id);
    }


    /**
     * 批量删除【科拓】门禁设备表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEspDoorDeviceByIds(Integer[] ids) {
        return espDoorDeviceMapper.deleteEspDoorDeviceByIds(ids);
    }

}

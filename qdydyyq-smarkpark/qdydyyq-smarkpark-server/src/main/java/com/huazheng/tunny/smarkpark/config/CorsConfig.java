//package com.huazheng.tunny.smarkpark.config;
//
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.web.servlet.config.annotation.CorsRegistry;
//import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
//
///**
// * 用于修复跨域资源共享漏洞
// * */
//@Configuration
//public class CorsConfig implements WebMvcConfigurer {
//    @Value("${corsConfig.origins}")
//    private String[] origins;
//    @Value("${corsConfig.methods}")
//    private String[] methods;
//    @Override
//    public void addCorsMappings(CorsRegistry registry) {
//        registry.addMapping("/**")
//                .allowedOrigins(origins) //用于修复跨域资源共享漏洞，可改为自己项目的域名
//                .allowedMethods(methods)
//                .allowedHeaders("*");
//    }
//}

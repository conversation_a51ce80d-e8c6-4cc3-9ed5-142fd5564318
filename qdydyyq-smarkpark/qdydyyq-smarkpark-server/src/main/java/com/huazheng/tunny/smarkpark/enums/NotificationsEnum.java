package com.huazheng.tunny.smarkpark.enums;

/**
 * <AUTHOR>
 * @date 2024/7/5
 */
public enum NotificationsEnum {
    PERSON_PASS_RECORD("01", "过人记录", "SkPersonPassRecord"),
    VEHICLE_PASS_RECORD("02", "过车记录", "SkVehiclePassRecord"),
    VEHICLE_ALARM_RECORD("03", "车辆告警记录", "SkVehicleAlarmRecord"),
    GATE_LIST("04", "闸机列表：包含人车道闸、测速点", "SkGateList");

    //编码
    private String code;
    //名称
    private String name;
    //对应实体类类名
    private String beanName;

    NotificationsEnum(String code, String name, String beanName) {
        this.code = code;
        this.name = name;
        this.beanName = beanName;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getBeanName() {
        return beanName;
    }

    // 通过code查找对应实体类类名
    public static String getBeanNameByCode(String code) {
        for (NotificationsEnum notificationsEnum : NotificationsEnum.values()) {
            if (notificationsEnum.getCode().equals(code)) {
                return notificationsEnum.getBeanName();
            }
        }
        return null;
    }

}

package com.huazheng.tunny.smarkpark.mapper;

import com.huazheng.tunny.smarkpark.api.entity.DahuaPfsRegion;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.huazheng.tunny.common.core.util.Query;
import java.util.List;
/**
 * 大华客流统计区域配置表  mapper层
 *
 * <AUTHOR>
 * @date 2025-05-20 17:44:47
 */
public interface DahuaPfsRegionMapper extends BaseMapper<DahuaPfsRegion> {
    /**
     * 查询大华客流统计区域配置表信息
     *
     * @param id 大华客流统计区域配置表ID
     * @return 大华客流统计区域配置表信息
     */
    public DahuaPfsRegion selectDahuaPfsRegionById(Long id);

    /**
     * 查询大华客流统计区域配置表列表
     *
     * @param dahuaPfsRegion 大华客流统计区域配置表信息
     * @return 大华客流统计区域配置表集合
     */
    public List<DahuaPfsRegion> selectDahuaPfsRegionList(DahuaPfsRegion dahuaPfsRegion);

    /**
     * 模糊查询大华客流统计区域配置表列表
     *
     * @param dahuaPfsRegion 大华客流统计区域配置表信息
     * @return 大华客流统计区域配置表集合
     */
    public List<DahuaPfsRegion> selectDahuaPfsRegionListByLike(DahuaPfsRegion dahuaPfsRegion);


    /**
     * 分页模糊查询大华客流统计区域配置表列表
     *
     * @param dahuaPfsRegion 大华客流统计区域配置表信息
     * @return 大华客流统计区域配置表集合
     */
    public List<DahuaPfsRegion> selectDahuaPfsRegionListByLike(Query query, DahuaPfsRegion dahuaPfsRegion);


    /**
     * 新增大华客流统计区域配置表
     *
     * @param dahuaPfsRegion 大华客流统计区域配置表信息
     * @return 结果
     */
    public int insertDahuaPfsRegion(DahuaPfsRegion dahuaPfsRegion);

    /**
     * 修改大华客流统计区域配置表
     *
     * @param dahuaPfsRegion 大华客流统计区域配置表信息
     * @return 结果
     */
    public int updateDahuaPfsRegion(DahuaPfsRegion dahuaPfsRegion);

    /**
     * 删除大华客流统计区域配置表
     *
     * @param id 大华客流统计区域配置表ID
     * @return 结果
     */
    public int deleteDahuaPfsRegionById(Long id);

    /**
     * 批量删除大华客流统计区域配置表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteDahuaPfsRegionByIds(Integer[] ids);


    void truncateDahuaPfsRegion();

    void insertDahuaPfsRegionList(List<DahuaPfsRegion> dahuaPfsRegions);
}

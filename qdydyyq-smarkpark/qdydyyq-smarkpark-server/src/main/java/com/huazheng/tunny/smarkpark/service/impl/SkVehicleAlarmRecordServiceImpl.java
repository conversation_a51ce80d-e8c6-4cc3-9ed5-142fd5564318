package com.huazheng.tunny.smarkpark.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import com.huazheng.tunny.smarkpark.mapper.SkVehicleAlarmRecordMapper;
import com.huazheng.tunny.smarkpark.api.entity.SkVehicleAlarmRecord;
import com.huazheng.tunny.smarkpark.service.SkVehicleAlarmRecordService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import javax.annotation.Resource;
import java.util.List;

@Data
@Service("skVehicleAlarmRecordService")
public class SkVehicleAlarmRecordServiceImpl extends ServiceImpl<SkVehicleAlarmRecordMapper, SkVehicleAlarmRecord> implements SkVehicleAlarmRecordService {

    @Resource
    private SkVehicleAlarmRecordMapper skVehicleAlarmRecordMapper;

    /**
     * 查询一卡通同步 - 车辆告警记录信息
     *
     * @param id 一卡通同步 - 车辆告警记录ID
     * @return 一卡通同步 - 车辆告警记录信息
     */
    @Override
    public SkVehicleAlarmRecord selectSkVehicleAlarmRecordById(Integer id)
    {
        return skVehicleAlarmRecordMapper.selectSkVehicleAlarmRecordById(id);
    }

    /**
     * 查询一卡通同步 - 车辆告警记录列表
     *
     * @param skVehicleAlarmRecord 一卡通同步 - 车辆告警记录信息
     * @return 一卡通同步 - 车辆告警记录集合
     */
    @Override
    public List<SkVehicleAlarmRecord> selectSkVehicleAlarmRecordList(SkVehicleAlarmRecord skVehicleAlarmRecord)
    {
        return skVehicleAlarmRecordMapper.selectSkVehicleAlarmRecordList(skVehicleAlarmRecord);
    }


    /**
     * 分页模糊查询一卡通同步 - 车辆告警记录列表
     * @return 一卡通同步 - 车辆告警记录集合
     */
    @Override
    public Page selectSkVehicleAlarmRecordListByLike(Query query)
    {
        SkVehicleAlarmRecord skVehicleAlarmRecord =  BeanUtil.toBean(query.getCondition(), SkVehicleAlarmRecord.class, CopyOptions.create());
        query.setRecords(skVehicleAlarmRecordMapper.selectSkVehicleAlarmRecordListByLike(query,skVehicleAlarmRecord));
        return query;
    }

    /**
     * 新增一卡通同步 - 车辆告警记录
     *
     * @param skVehicleAlarmRecord 一卡通同步 - 车辆告警记录信息
     * @return 结果
     */
    @Override
    public int insertSkVehicleAlarmRecord(SkVehicleAlarmRecord skVehicleAlarmRecord)
    {
        return skVehicleAlarmRecordMapper.insertSkVehicleAlarmRecord(skVehicleAlarmRecord);
    }

    /**
     * 修改一卡通同步 - 车辆告警记录
     *
     * @param skVehicleAlarmRecord 一卡通同步 - 车辆告警记录信息
     * @return 结果
     */
    @Override
    public int updateSkVehicleAlarmRecord(SkVehicleAlarmRecord skVehicleAlarmRecord)
    {
        return skVehicleAlarmRecordMapper.updateSkVehicleAlarmRecord(skVehicleAlarmRecord);
    }


    /**
     * 删除一卡通同步 - 车辆告警记录
     *
     * @param id 一卡通同步 - 车辆告警记录ID
     * @return 结果
     */
    @Override
    public int deleteSkVehicleAlarmRecordById(Integer id)
    {
        return skVehicleAlarmRecordMapper.deleteSkVehicleAlarmRecordById( id);
    };


    /**
     * 批量删除一卡通同步 - 车辆告警记录对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteSkVehicleAlarmRecordByIds(Integer[] ids)
    {
        return skVehicleAlarmRecordMapper.deleteSkVehicleAlarmRecordByIds( ids);
    }

    /**
     * 一卡通同步 - 车辆告警记录
     * */
    @Override
    public void syncSkVehicleAlarmRecord(List<SkVehicleAlarmRecord> list) {
        int step = 50;
        for (int i = 0; i < list.size(); i += step) {
            skVehicleAlarmRecordMapper.syncSkVehicleAlarmRecord(list.subList(i, (i + step) < list.size() ? (i + step) : list.size()));
        }
    }

}

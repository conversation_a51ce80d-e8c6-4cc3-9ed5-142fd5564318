package com.huazheng.tunny.smarkpark.mapper;

import com.huazheng.tunny.smarkpark.api.entity.ZhoujieZone;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.huazheng.tunny.common.core.util.Query;
import java.util.List;
/**
 * 周界电子围栏防区  mapper层
 *
 * <AUTHOR>
 * @date 2025-05-13 15:22:38
 */
public interface ZhoujieZoneMapper extends BaseMapper<ZhoujieZone> {
    /**
     * 查询周界电子围栏防区信息
     *
     * @param id 周界电子围栏防区ID
     * @return 周界电子围栏防区信息
     */
    public ZhoujieZone selectZhoujieZoneById(Long id);

    /**
     * 查询周界电子围栏防区列表
     *
     * @param zhoujieZone 周界电子围栏防区信息
     * @return 周界电子围栏防区集合
     */
    public List<ZhoujieZone> selectZhoujieZoneList(ZhoujieZone zhoujieZone);

    /**
     * 模糊查询周界电子围栏防区列表
     *
     * @param zhoujieZone 周界电子围栏防区信息
     * @return 周界电子围栏防区集合
     */
    public List<ZhoujieZone> selectZhoujieZoneListByLike(ZhoujieZone zhoujieZone);


    /**
     * 分页模糊查询周界电子围栏防区列表
     *
     * @param zhoujieZone 周界电子围栏防区信息
     * @return 周界电子围栏防区集合
     */
    public List<ZhoujieZone> selectZhoujieZoneListByLike(Query query, ZhoujieZone zhoujieZone);


    /**
     * 新增周界电子围栏防区
     *
     * @param zhoujieZone 周界电子围栏防区信息
     * @return 结果
     */
    public int insertZhoujieZone(ZhoujieZone zhoujieZone);

    /**
     * 修改周界电子围栏防区
     *
     * @param zhoujieZone 周界电子围栏防区信息
     * @return 结果
     */
    public int updateZhoujieZone(ZhoujieZone zhoujieZone);

    /**
     * 删除周界电子围栏防区
     *
     * @param id 周界电子围栏防区ID
     * @return 结果
     */
    public int deleteZhoujieZoneById(Long id);

    /**
     * 批量删除周界电子围栏防区
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteZhoujieZoneByIds(Integer[] ids);



}

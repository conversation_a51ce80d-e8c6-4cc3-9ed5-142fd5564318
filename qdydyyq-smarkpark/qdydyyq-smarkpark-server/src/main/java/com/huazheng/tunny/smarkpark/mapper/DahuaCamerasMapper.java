package com.huazheng.tunny.smarkpark.mapper;

import com.huazheng.tunny.smarkpark.api.dto.DaHuaCamerasDTO;
import com.huazheng.tunny.smarkpark.api.dto.RegionsAndCamerasDto;
import com.huazheng.tunny.smarkpark.api.entity.DahuaCameras;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.huazheng.tunny.common.core.util.Query;
import java.util.List;
import java.util.Map;

/**
 * 大华同步-监控设备  mapper层
 *
 * <AUTHOR>
 * @date 2025-04-29 11:45:57
 */
public interface DahuaCamerasMapper extends BaseMapper<DahuaCameras> {
    /**
     * 查询大华同步-监控设备信息
     *
     * @param deviceSn 大华同步-监控设备ID
     * @return 大华同步-监控设备信息
     */
    public DahuaCameras selectDahuaCamerasById(String deviceSn);

    /**
     * 查询大华同步-监控设备列表
     *
     * @param dahuaCameras 大华同步-监控设备信息
     * @return 大华同步-监控设备集合
     */
    public List<DahuaCameras> selectDahuaCamerasList(DahuaCameras dahuaCameras);

    /**
     * 模糊查询大华同步-监控设备列表
     *
     * @param dahuaCameras 大华同步-监控设备信息
     * @return 大华同步-监控设备集合
     */
    public List<DahuaCameras> selectDahuaCamerasListByLike(DahuaCameras dahuaCameras);


    /**
     * 分页模糊查询大华同步-监控设备列表
     *
     * @param dahuaCameras 大华同步-监控设备信息
     * @return 大华同步-监控设备集合
     */
    public List<DahuaCameras> selectDahuaCamerasListByLike(Query query, DahuaCameras dahuaCameras);


    /**
     * 新增大华同步-监控设备
     *
     * @param dahuaCameras 大华同步-监控设备信息
     * @return 结果
     */
    public int insertDahuaCameras(DahuaCameras dahuaCameras);

    /**
     * 修改大华同步-监控设备
     *
     * @param dahuaCameras 大华同步-监控设备信息
     * @return 结果
     */
    public int updateDahuaCameras(DahuaCameras dahuaCameras);

    /**
     * 删除大华同步-监控设备
     *
     * @param deviceSn 大华同步-监控设备ID
     * @return 结果
     */
    public int deleteDahuaCamerasById(String deviceSn);

    /**
     * 批量删除大华同步-监控设备
     *
     * @param deviceSns 需要删除的数据ID
     * @return 结果
     */
    public int deleteDahuaCamerasByIds(Integer[] deviceSns);

    /**
     * 清空大华同步-监控设备表
     */
    void truncateDahuaCameras();
    /**
     * 批量新增大华同步-监控设备表
     *
     * @param list 大华同步-监控设备表列表
     */
    void insertDahuaCamerasList(List<DahuaCameras> list);

    List<RegionsAndCamerasDto> regionsAndCamerasList();

    /**
     * 监控数量统计
     *
     * @return Map<String, Object>
     * */
    Map<String, Object> statisticsCamerasNum();

    void updateDahuaCamerasState(DahuaCameras dahuaCameras);

    DahuaCameras selectDahuaCamerasByChannelCode(String channelCode);

    List<DaHuaCamerasDTO> camerasPage(Query<Object> query, DahuaCameras dahuaCameras);

    Map<String, Object> statisticsKeLiuDeviceNum();

    List<Map<String, Object>> statisticsKeLiuDeviceList(Query query, Map<String, Object> map);
}

package com.huazheng.tunny.smarkpark.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.smarkpark.api.entity.ZhoujieDevice;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 周界电子围栏设备 服务接口层
 *
 * <AUTHOR>
 * @date 2025-05-13 15:22:32
 */
public interface ZhoujieDeviceService extends IService<ZhoujieDevice> {
    /**
     * 查询周界电子围栏设备信息
     *
     * @param id 周界电子围栏设备ID
     * @return 周界电子围栏设备信息
     */
    public ZhoujieDevice selectZhoujieDeviceById(Long id);

    /**
     * 查询周界电子围栏设备列表
     *
     * @param zhoujieDevice 周界电子围栏设备信息
     * @return 周界电子围栏设备集合
     */
    public List<ZhoujieDevice> selectZhoujieDeviceList(ZhoujieDevice zhoujieDevice);


    /**
     * 分页模糊查询周界电子围栏设备列表
     * @return 周界电子围栏设备集合
     */
    public Page selectZhoujieDeviceListByLike(Query query);



    /**
     * 新增周界电子围栏设备
     *
     * @param zhoujieDevice 周界电子围栏设备信息
     * @return 结果
     */
    public int insertZhoujieDevice(ZhoujieDevice zhoujieDevice);

    /**
     * 修改周界电子围栏设备
     *
     * @param zhoujieDevice 周界电子围栏设备信息
     * @return 结果
     */
    public int updateZhoujieDevice(ZhoujieDevice zhoujieDevice);

    /**
     * 删除周界电子围栏设备
     *
     * @param id 周界电子围栏设备ID
     * @return 结果
     */
    public int deleteZhoujieDeviceById(Long id);

    /**
     * 批量删除周界电子围栏设备
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteZhoujieDeviceByIds(Integer[] ids);

    R cronDeviceList();
}


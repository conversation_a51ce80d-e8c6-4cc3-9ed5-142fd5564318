package com.huazheng.tunny.smarkpark.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.smarkpark.api.entity.SkCockpitMapPoints;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;
import java.util.Map;

/**
 * 驾驶舱地图监控点位 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2024-07-11 14:24:41
 */
public interface SkCockpitMapPointsService extends IService<SkCockpitMapPoints> {
    /**
     * 查询驾驶舱地图监控点位信息
     *
     * @param indexCode 驾驶舱地图监控点位ID
     * @return 驾驶舱地图监控点位信息
     */
    public SkCockpitMapPoints selectSkCockpitMapPointsById(String indexCode);

    /**
     * 查询驾驶舱地图监控点位列表
     *
     * @param skCockpitMapPoints 驾驶舱地图监控点位信息
     * @return 驾驶舱地图监控点位集合
     */
    public List<SkCockpitMapPoints> selectSkCockpitMapPointsList(SkCockpitMapPoints skCockpitMapPoints);


    /**
     * 分页模糊查询驾驶舱地图监控点位列表
     * @return 驾驶舱地图监控点位集合
     */
    public Page selectSkCockpitMapPointsListByLike(Query query);



    /**
     * 新增驾驶舱地图监控点位
     *
     * @param skCockpitMapPoints 驾驶舱地图监控点位信息
     * @return 结果
     */
    public int insertSkCockpitMapPoints(SkCockpitMapPoints skCockpitMapPoints);

    /**
     * 修改驾驶舱地图监控点位
     *
     * @param skCockpitMapPoints 驾驶舱地图监控点位信息
     * @return 结果
     */
    public int updateSkCockpitMapPoints(SkCockpitMapPoints skCockpitMapPoints);

    /**
     * 删除驾驶舱地图监控点位
     *
     * @param indexCode 驾驶舱地图监控点位ID
     * @return 结果
     */
    public int deleteSkCockpitMapPointsById(String indexCode);

    /**
     * 批量删除驾驶舱地图监控点位
     *
     * @param indexCodes 需要删除的数据ID
     * @return 结果
     */
    public int deleteSkCockpitMapPointsByIds(Integer[] indexCodes);

    Map<String, Object> camerasStatistics();

    List<Map<String, Object>> camerasOnlineRate();

    Map<String, Object> fireFightEquipmentStatistics();

    List<Map<String, Object>> fireFightEquipmentOnlineRate();

    /**
     * 监控数量统计
     *
     * @return Map<String, Object>
     * */
    Map<String, Object> statisticsCamerasNum();

    /**
     * 设备在线率统计
     *
     * @return Map<String, Object>
     * */
    Map<String, Object> statisticsDeviceOnlineRate();

    List<Map<String, Object>> trafficEquipmentSituation();

    List<Map<String, Object>> vehicleStatistics();

    R trafficEquipmentInfo(Query query);

    /**
     * 楼控设备按类型统计
     *
     * @return List<Map<String, Object>>
     * */
    List<Map<String, Object>> buildingControlDeviceByType();
    /**
     * 楼控设备状态
     *
     * @return List<Map < String, Object>>
     */
    Map<String, Object> buildingControlDeviceState();

    R parkEnvironment();

    R energyTrends(Map<String, Object> params);
}


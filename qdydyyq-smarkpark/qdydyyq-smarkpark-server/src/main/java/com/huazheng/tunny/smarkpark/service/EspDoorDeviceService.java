package com.huazheng.tunny.smarkpark.service;

import com.baomidou.mybatisplus.service.IService;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.smarkpark.api.entity.EspDoorDevice;

import java.util.List;

/**
 * 【科拓】门禁设备表 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2025-05-07 11:43:47
 */
public interface EspDoorDeviceService extends IService<EspDoorDevice> {
    /**
     * 查询【科拓】门禁设备表信息
     *
     * @param id 【科拓】门禁设备表ID
     * @return 【科拓】门禁设备表信息
     */
    public EspDoorDevice selectEspDoorDeviceById(String id);

    /**
     * 查询【科拓】门禁设备表列表
     *
     * @param espDoorDevice 【科拓】门禁设备表信息
     * @return 【科拓】门禁设备表集合
     */
    public List<EspDoorDevice> selectEspDoorDeviceList(EspDoorDevice espDoorDevice);


    /**
     * 分页模糊查询【科拓】门禁设备表列表
     *
     * @return 【科拓】门禁设备表集合
     */
    public Page selectEspDoorDeviceListByLike(Query query);


    /**
     * 新增【科拓】门禁设备表
     *
     * @param espDoorDevice 【科拓】门禁设备表信息
     * @return 结果
     */
    public int insertEspDoorDevice(EspDoorDevice espDoorDevice);

    /**
     * 修改【科拓】门禁设备表
     *
     * @param espDoorDevice 【科拓】门禁设备表信息
     * @return 结果
     */
    public int updateEspDoorDevice(EspDoorDevice espDoorDevice);

    /**
     * 删除【科拓】门禁设备表
     *
     * @param id 【科拓】门禁设备表ID
     * @return 结果
     */
    public int deleteEspDoorDeviceById(String id);

    /**
     * 批量删除【科拓】门禁设备表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteEspDoorDeviceByIds(Integer[] ids);

}


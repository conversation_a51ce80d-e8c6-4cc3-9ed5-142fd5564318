package com.huazheng.tunny.smarkpark.util;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PlateType {
    SMALL_CAR(1, "小型车"),
    LARFE_VEHICLES(2, "大型车"),
    OVERSIZED_CARS(3, "超大型车"),
    MOTORCYCLES(4, "摩托车"),
    ELECTRO_SCOOTER(5, "电瓶车"),
    TRICYCLES(6, "三轮车"),
    OTHER_MODELS(7, "其他车型");
    private final int code;
    private final String name;

    /**
     * 根据编码获取枚举
     */
    public static String getNameByCode(String codeStr) {
        if (StrUtil.isBlank(codeStr)) {
            return null;
        }
        int code = Integer.parseInt(codeStr);
        for (PlateType type : values()) {
            if (type.code == code) {
                return type.name;
            }
        }
        return OTHER_MODELS.name;
    }

}

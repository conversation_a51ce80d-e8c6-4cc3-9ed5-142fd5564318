package com.huazheng.tunny.smarkpark.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.huazheng.tunny.smarkpark.api.entity.SkBuildControlDevice;
import com.huazheng.tunny.smarkpark.api.entity.SkBuildControlDeviceAttribute;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class LightingUtil {
    public static String openApi = "/soap/BrowseTag";

    public List<SkBuildControlDevice> queryBrowseTag(String host, String node) {
        List<SkBuildControlDevice> list = new ArrayList<>();
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("node", node);
        try {
            String str = HttpRequest.post(host + openApi).body(JSONUtil.toJsonStr(paramMap)).execute().body();
            JSONObject jsonObject = JSONUtil.parseObj(str);
            if (StrUtil.isNotBlank(jsonObject.getStr("drivers"))) {
                List<JSONObject> driversList = JSONUtil.toList(jsonObject.getJSONArray("drivers"), JSONObject.class);
                for (JSONObject driver : driversList) {
                    if (StrUtil.isNotBlank(driver.getStr("channels"))) {
                        List<JSONObject> channelsList = JSONUtil.toList(driver.getJSONArray("channels"), JSONObject.class);
                        for (JSONObject channel : channelsList) {
                            if (StrUtil.isNotBlank(channel.getStr("devices"))) {
                                List<JSONObject> devicesList = JSONUtil.toList(channel.getJSONArray("devices"), JSONObject.class);
                                for (JSONObject devices : devicesList) {
                                    List<JSONObject> tagsList = JSONUtil.toList(devices.getJSONArray("tags"), JSONObject.class);
                                    for (JSONObject tags : tagsList) {
                                        if (tags.getStr("n").endsWith("状态")) {
                                            SkBuildControlDevice skBuildControlDevice = new SkBuildControlDevice();
                                            String name = tags.getStr("n").replace("状态", "");
                                            skBuildControlDevice.setType(5);
                                            skBuildControlDevice.setDeviceCode(name);
                                            skBuildControlDevice.setDeviceName(name);
                                            skBuildControlDevice.setDeviceAlias(name);
                                            skBuildControlDevice.setRemark(driver.getStr("name") + "-" + name);
                                            skBuildControlDevice.setRunState("1");
                                            list.add(skBuildControlDevice);
                                            //设置属性值
                                            List<SkBuildControlDeviceAttribute> attributeList = new ArrayList<>();
                                            SkBuildControlDeviceAttribute attribute = new SkBuildControlDeviceAttribute();
                                            attribute.setDeviceCode(name);
                                            attribute.setAttributeCode("status");
                                            attribute.setAttributeName("状态");
                                            attribute.setAttributeVal(tags.getStr("v").equals("0")?"关灯":"开灯");
                                            attribute.setReportTime(LocalDateTime.now());
                                            attributeList.add(attribute);
                                            skBuildControlDevice.setAttributeList(attributeList);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }
}

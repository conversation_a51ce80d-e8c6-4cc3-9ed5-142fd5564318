package com.huazheng.tunny.smarkpark.mapper;

import com.huazheng.tunny.smarkpark.api.entity.SkBuildControlDeviceConfig;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.huazheng.tunny.common.core.util.Query;
import java.util.List;
/**
 * 楼控设备属性配置表  mapper层
 *
 * <AUTHOR>
 * @date 2025-06-18 16:20:49
 */
public interface SkBuildControlDeviceConfigMapper extends BaseMapper<SkBuildControlDeviceConfig> {
    /**
     * 查询楼控设备属性配置表信息
     *
     * @param id 楼控设备属性配置表ID
     * @return 楼控设备属性配置表信息
     */
    public SkBuildControlDeviceConfig selectSkBuildControlDeviceConfigById(Long id);

    /**
     * 查询楼控设备属性配置表列表
     *
     * @param skBuildControlDeviceConfig 楼控设备属性配置表信息
     * @return 楼控设备属性配置表集合
     */
    public List<SkBuildControlDeviceConfig> selectSkBuildControlDeviceConfigList(SkBuildControlDeviceConfig skBuildControlDeviceConfig);

    /**
     * 模糊查询楼控设备属性配置表列表
     *
     * @param skBuildControlDeviceConfig 楼控设备属性配置表信息
     * @return 楼控设备属性配置表集合
     */
    public List<SkBuildControlDeviceConfig> selectSkBuildControlDeviceConfigListByLike(SkBuildControlDeviceConfig skBuildControlDeviceConfig);


    /**
     * 分页模糊查询楼控设备属性配置表列表
     *
     * @param skBuildControlDeviceConfig 楼控设备属性配置表信息
     * @return 楼控设备属性配置表集合
     */
    public List<SkBuildControlDeviceConfig> selectSkBuildControlDeviceConfigListByLike(Query query, SkBuildControlDeviceConfig skBuildControlDeviceConfig);


    /**
     * 新增楼控设备属性配置表
     *
     * @param skBuildControlDeviceConfig 楼控设备属性配置表信息
     * @return 结果
     */
    public int insertSkBuildControlDeviceConfig(SkBuildControlDeviceConfig skBuildControlDeviceConfig);

    /**
     * 修改楼控设备属性配置表
     *
     * @param skBuildControlDeviceConfig 楼控设备属性配置表信息
     * @return 结果
     */
    public int updateSkBuildControlDeviceConfig(SkBuildControlDeviceConfig skBuildControlDeviceConfig);

    /**
     * 删除楼控设备属性配置表
     *
     * @param id 楼控设备属性配置表ID
     * @return 结果
     */
    public int deleteSkBuildControlDeviceConfigById(Long id);

    /**
     * 批量删除楼控设备属性配置表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteSkBuildControlDeviceConfigByIds(Integer[] ids);



}

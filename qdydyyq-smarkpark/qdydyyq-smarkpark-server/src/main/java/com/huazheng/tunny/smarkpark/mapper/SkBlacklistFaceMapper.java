package com.huazheng.tunny.smarkpark.mapper;

import com.huazheng.tunny.smarkpark.api.entity.SkBlacklistFace;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 人员布控人脸信息  mapper层
 *
 * <AUTHOR> code generator
 * @date 2024-07-18 18:42:05
 */
public interface SkBlacklistFaceMapper extends BaseMapper<SkBlacklistFace> {
    /**
     * 查询人员布控人脸信息信息
     *
     * @param indexCode 人员布控人脸信息ID
     * @return 人员布控人脸信息信息
     */
    public SkBlacklistFace selectSkBlacklistFaceById(String indexCode);

    /**
     * 查询人员布控人脸信息列表
     *
     * @param skBlacklistFace 人员布控人脸信息信息
     * @return 人员布控人脸信息集合
     */
    public List<SkBlacklistFace> selectSkBlacklistFaceList(SkBlacklistFace skBlacklistFace);

    /**
     * 模糊查询人员布控人脸信息列表
     *
     * @param skBlacklistFace 人员布控人脸信息信息
     * @return 人员布控人脸信息集合
     */
    public List<SkBlacklistFace> selectSkBlacklistFaceListByLike(SkBlacklistFace skBlacklistFace);


    /**
     * 分页模糊查询人员布控人脸信息列表
     *
     * @param skBlacklistFace 人员布控人脸信息信息
     * @return 人员布控人脸信息集合
     */
    public List<SkBlacklistFace> selectSkBlacklistFaceListByLike(Query query, SkBlacklistFace skBlacklistFace);


    /**
     * 新增人员布控人脸信息
     *
     * @param skBlacklistFace 人员布控人脸信息信息
     * @return 结果
     */
    public int insertSkBlacklistFace(SkBlacklistFace skBlacklistFace);

    /**
     * 修改人员布控人脸信息
     *
     * @param skBlacklistFace 人员布控人脸信息信息
     * @return 结果
     */
    public int updateSkBlacklistFace(SkBlacklistFace skBlacklistFace);

    /**
     * 删除人员布控人脸信息
     *
     * @param indexCode 人员布控人脸信息ID
     * @return 结果
     */
    public int deleteSkBlacklistFaceById(String indexCode);

    /**
     * 批量删除人员布控人脸信息
     *
     * @param indexCodes 需要删除的数据ID
     * @return 结果
     */
    public int deleteSkBlacklistFaceByIds(Integer[] indexCodes);


}

package com.huazheng.tunny.smarkpark.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.smarkpark.api.entity.ParkingOutIntoInfo;
import com.huazheng.tunny.smarkpark.mapper.ParkingOutIntoInfoMapper;
import com.huazheng.tunny.smarkpark.mapper.ParkingVehicleRecordMapper;
import com.huazheng.tunny.smarkpark.api.entity.ParkingVehicleRecord;
import com.huazheng.tunny.smarkpark.service.ParkingVehicleRecordService;
import com.huazheng.tunny.smarkpark.util.PlateType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("parkingVehicleRecordService")
public class ParkingVehicleRecordServiceImpl extends ServiceImpl<ParkingVehicleRecordMapper, ParkingVehicleRecord> implements ParkingVehicleRecordService {

    @Autowired
    private ParkingVehicleRecordMapper parkingVehicleRecordMapper;
    @Autowired
    private ParkingOutIntoInfoMapper parkingOutIntoInfoMapper;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 查询停车场-车辆记录信息
     *
     * @param id 停车场-车辆记录ID
     * @return 停车场-车辆记录信息
     */
    @Override
    public ParkingVehicleRecord selectParkingVehicleRecordById(String id) {
        return parkingVehicleRecordMapper.selectParkingVehicleRecordById(id);
    }

    /**
     * 查询停车场-车辆记录列表
     *
     * @param parkingVehicleRecord 停车场-车辆记录信息
     * @return 停车场-车辆记录集合
     */
    @Override
    public List<ParkingVehicleRecord> selectParkingVehicleRecordList(ParkingVehicleRecord parkingVehicleRecord) {
        return parkingVehicleRecordMapper.selectParkingVehicleRecordList(parkingVehicleRecord);
    }

    @Override
    public R selectParkingOutIntoListByLike(Query query) {
        ParkingOutIntoInfo parkingOutIntoInfo = BeanUtil.toBean(query.getCondition(), ParkingOutIntoInfo.class, CopyOptions.create());
        query.setRecords(parkingOutIntoInfoMapper.selectParkingOutIntoInfoListByLike(query, parkingOutIntoInfo));
        return R.success(query);
    }


    /**
     * 分页模糊查询停车场-车辆记录列表
     *
     * @return 停车场-车辆记录集合
     */
    @Override
    public R selectParkingVehicleRecordListByLike(Query query) {
        ParkingVehicleRecord parkingVehicleRecord = BeanUtil.toBean(query.getCondition(), ParkingVehicleRecord.class, CopyOptions.create());
        query.setRecords(parkingVehicleRecordMapper.selectParkingVehicleRecordListByLike(query, parkingVehicleRecord));
        return R.success(query);
    }

    /**
     * 新增停车场-车辆记录
     *
     * @param map 停车场-车辆记录信息
     * @return 结果
     */
    @Override
    public Map<String, Object> insertParkingVehicleRecord(Map<String, Object> map) {
        return null;
    }


    /**
     * 修改停车场-车辆记录
     *
     * @param parkingVehicleRecord 停车场-车辆记录信息
     * @return 结果
     */
    @Override
    public int updateParkingVehicleRecord(ParkingVehicleRecord parkingVehicleRecord) {
        return parkingVehicleRecordMapper.updateParkingVehicleRecord(parkingVehicleRecord);
    }


    /**
     * 删除停车场-车辆记录
     *
     * @param id 停车场-车辆记录ID
     * @return 结果
     */
    @Override
    public int deleteParkingVehicleRecordById(String id) {
        return parkingVehicleRecordMapper.deleteParkingVehicleRecordById(id);
    }


    /**
     * 批量删除停车场-车辆记录对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteParkingVehicleRecordByIds(Integer[] ids) {
        return parkingVehicleRecordMapper.deleteParkingVehicleRecordByIds(ids);
    }

    @Override
    public void conVehicleRecord(List<ParkingVehicleRecord> list) {
        if(CollUtil.isNotEmpty(list)){
            for (ParkingVehicleRecord vehicleRecord : list) {
                ParkingVehicleRecord oldParkingVehicleRecord = parkingVehicleRecordMapper.selectById(vehicleRecord.getId());
                vehicleRecord.setPlateType(PlateType.getNameByCode(vehicleRecord.getPlateType()));
                if (oldParkingVehicleRecord == null) {
                    parkingVehicleRecordMapper.insertParkingVehicleRecord(vehicleRecord);
                } else {
                    parkingVehicleRecordMapper.updateParkingVehicleRecord(vehicleRecord);
                }
                this.insertParkingOutIntoInfo(vehicleRecord);
            }
        }
        String time = getTenMinuteAgoDate();
        redisTemplate.opsForValue().set("cronVehicleRecordTime", time);

        int count = parkingVehicleRecordMapper.selectInParkingVehicleRecordCount();
        redisTemplate.opsForValue().set("parkingSpacesInto", count + "");
    }


    private void insertParkingOutIntoInfo(ParkingVehicleRecord parkingVehicleRecord) {
        String parkInOutOrder = parkingVehicleRecord.getId();
        parkingVehicleRecord.setId(null);
        //添加入场数据
        insertParkingIntoInfo(parkingVehicleRecord, parkInOutOrder);
        //检验是否为出场记录
        if (parkingVehicleRecord.getOutWay() != null) {
            //添加出场数据
            insertParkingOutInfo(parkingVehicleRecord, parkInOutOrder);
        }
    }

    private void insertParkingIntoInfo(ParkingVehicleRecord parkingVehicleRecord, String parkInOutOrder) {
        ParkingOutIntoInfo parkingOutIntoInfo = new ParkingOutIntoInfo();
        parkingOutIntoInfo.setWayType(0);
        parkingOutIntoInfo.setParkInOutOrder(parkInOutOrder);
        List<ParkingOutIntoInfo> list = parkingOutIntoInfoMapper.selectParkingOutIntoInfoList(parkingOutIntoInfo);
        if (CollUtil.isNotEmpty(list)) {
            return;
        }
        BeanUtil.copyProperties(parkingVehicleRecord, parkingOutIntoInfo);
        parkingOutIntoInfo.setParkInOutOrder(parkInOutOrder);
        parkingOutIntoInfo.setOutInTime(parkingVehicleRecord.getInParkTime());
        parkingOutIntoInfo.setParkChannelNumber(parkingVehicleRecord.getInParkChannelNumber());
        parkingOutIntoInfo.setParkChannelName(parkingVehicleRecord.getInParkChannelName());
        parkingOutIntoInfo.setParkChannelUser(parkingVehicleRecord.getInParkChannelUser());
        parkingOutIntoInfo.setParkChannelImageUrl(parkingVehicleRecord.getInParkChannelImageUrl());
        parkingOutIntoInfo.setImageUrl(parkingVehicleRecord.getInImageUrl());
        parkingOutIntoInfoMapper.insertParkingOutIntoInfo(parkingOutIntoInfo);
    }

    private void insertParkingOutInfo(ParkingVehicleRecord parkingVehicleRecord, String parkInOutOrder) {
        ParkingOutIntoInfo parkingOutIntoInfo = new ParkingOutIntoInfo();
        parkingOutIntoInfo.setWayType(1);
        parkingOutIntoInfo.setParkInOutOrder(parkInOutOrder);
        List<ParkingOutIntoInfo> list = parkingOutIntoInfoMapper.selectParkingOutIntoInfoList(parkingOutIntoInfo);
        if (CollUtil.isNotEmpty(list)) {
            return;
        }
        BeanUtil.copyProperties(parkingVehicleRecord, parkingOutIntoInfo);
        parkingOutIntoInfo.setId(null);
        parkingOutIntoInfo.setParkInOutOrder(parkInOutOrder);
        parkingOutIntoInfo.setOutInTime(parkingVehicleRecord.getOutParkTime());
        parkingOutIntoInfo.setParkChannelNumber(parkingVehicleRecord.getOutParkChannelNumber());
        parkingOutIntoInfo.setParkChannelName(parkingVehicleRecord.getOutParkChannelName());
        parkingOutIntoInfo.setParkChannelUser(parkingVehicleRecord.getOutParkChannelUser());
        parkingOutIntoInfo.setParkChannelImageUrl(parkingVehicleRecord.getOutParkChannelImageUrl());
        parkingOutIntoInfo.setImageUrl(parkingVehicleRecord.getInImageUrl());
        parkingOutIntoInfoMapper.insertParkingOutIntoInfo(parkingOutIntoInfo);
    }

    public String getTenMinuteAgoDate() {
        // 获取当前日期
        DateTime now = DateUtil.date();
        // 计算三个月前日期（自动处理跨年）
        DateTime threeMonthsAgo = DateUtil.offsetMinute(now, -10);
        // 格式化为字符串（默认yyyy-MM-dd格式）
        String result = DateUtil.format(threeMonthsAgo, "yyyy-MM-dd hh:mm:ss");
        System.out.println("三个月前日期：" + result);
        return result;
    }
}

package com.huazheng.tunny.smarkpark.controller;

import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.smarkpark.api.entity.SkEmergencyEvacuation;
import com.huazheng.tunny.smarkpark.service.SkEmergencyEvacuationService;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.core.listener.EasyExcelListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 紧急疏散管理
 *
 * <AUTHOR> code generator
 * @date 2024-07-31 16:45:34
 */
@Slf4j
@RestController
@RequestMapping("/skEmergencyEvacuation")
public class SkEmergencyEvacuationController {

    @Autowired
    private SkEmergencyEvacuationService skEmergencyEvacuationService;

    /**
     * 列表
     *
     * @param params
     * @return
     */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //对象模糊查询
        return skEmergencyEvacuationService.selectSkEmergencyEvacuationListByLike(new Query<>(params));
    }

    /**
     * 信息
     *
     * @param id
     * @return R
     */
    @GetMapping("/{id}")
    public R info(@PathVariable("id") Integer id) {
        SkEmergencyEvacuation skEmergencyEvacuation = skEmergencyEvacuationService.selectSkEmergencyEvacuationById(id);
        return new R<>(skEmergencyEvacuation);
    }

    /**
     * 保存
     *
     * @param skEmergencyEvacuation
     * @return R
     */
    @PostMapping
    public R save(@RequestBody SkEmergencyEvacuation skEmergencyEvacuation) {
        skEmergencyEvacuation.setCreateTime(LocalDateTime.now());
        skEmergencyEvacuation.setCreateBy(SecurityUtils.getUserInfo().getUserName());
        skEmergencyEvacuationService.insertSkEmergencyEvacuation(skEmergencyEvacuation);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     *
     * @param skEmergencyEvacuation
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody SkEmergencyEvacuation skEmergencyEvacuation) {
        skEmergencyEvacuation.setUpdateTime(LocalDateTime.now());
        skEmergencyEvacuation.setUpdateBy(SecurityUtils.getUserInfo().getUserName());
        skEmergencyEvacuationService.updateSkEmergencyEvacuation(skEmergencyEvacuation);
        return new R<>(Boolean.TRUE);
    }


    /**
     * 删除
     *
     * @param id
     * @return R
     */
    @GetMapping("/del/{id}")
    public R delete(@PathVariable Integer id) {
        skEmergencyEvacuationService.deleteSkEmergencyEvacuationById(id);
        return new R<>(Boolean.TRUE);
    }

}

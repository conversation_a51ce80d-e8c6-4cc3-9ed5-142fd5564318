package com.huazheng.tunny.smarkpark.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import com.huazheng.tunny.smarkpark.mapper.SkEmergencyEvacuationMapper;
import com.huazheng.tunny.smarkpark.api.entity.SkEmergencyEvacuation;
import com.huazheng.tunny.smarkpark.service.SkEmergencyEvacuationService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Data
@Service("skEmergencyEvacuationService")
public class SkEmergencyEvacuationServiceImpl extends ServiceImpl<SkEmergencyEvacuationMapper, SkEmergencyEvacuation> implements SkEmergencyEvacuationService {

    @Autowired
    private SkEmergencyEvacuationMapper skEmergencyEvacuationMapper;

    /**
     * 查询紧急疏散管理信息
     *
     * @param id 紧急疏散管理ID
     * @return 紧急疏散管理信息
     */
    @Override
    public SkEmergencyEvacuation selectSkEmergencyEvacuationById(Integer id) {
        return skEmergencyEvacuationMapper.selectSkEmergencyEvacuationById(id);
    }

    /**
     * 查询紧急疏散管理列表
     *
     * @param skEmergencyEvacuation 紧急疏散管理信息
     * @return 紧急疏散管理集合
     */
    @Override
    public List<SkEmergencyEvacuation> selectSkEmergencyEvacuationList(SkEmergencyEvacuation skEmergencyEvacuation) {
        return skEmergencyEvacuationMapper.selectSkEmergencyEvacuationList(skEmergencyEvacuation);
    }


    /**
     * 分页模糊查询紧急疏散管理列表
     *
     * @return 紧急疏散管理集合
     */
    @Override
    public Page selectSkEmergencyEvacuationListByLike(Query query) {
        SkEmergencyEvacuation skEmergencyEvacuation = BeanUtil.toBean(query.getCondition(), SkEmergencyEvacuation.class, CopyOptions.create());
        query.setRecords(skEmergencyEvacuationMapper.selectSkEmergencyEvacuationListByLike(query, skEmergencyEvacuation));
        return query;
    }

    /**
     * 新增紧急疏散管理
     *
     * @param skEmergencyEvacuation 紧急疏散管理信息
     * @return 结果
     */
    @Override
    public int insertSkEmergencyEvacuation(SkEmergencyEvacuation skEmergencyEvacuation) {
        return skEmergencyEvacuationMapper.insertSkEmergencyEvacuation(skEmergencyEvacuation);
    }

    /**
     * 修改紧急疏散管理
     *
     * @param skEmergencyEvacuation 紧急疏散管理信息
     * @return 结果
     */
    @Override
    public int updateSkEmergencyEvacuation(SkEmergencyEvacuation skEmergencyEvacuation) {
        return skEmergencyEvacuationMapper.updateSkEmergencyEvacuation(skEmergencyEvacuation);
    }


    /**
     * 删除紧急疏散管理
     *
     * @param id 紧急疏散管理ID
     * @return 结果
     */
    @Override
    public int deleteSkEmergencyEvacuationById(Integer id) {
        return skEmergencyEvacuationMapper.deleteSkEmergencyEvacuationById(id);
    }

    ;


    /**
     * 批量删除紧急疏散管理对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteSkEmergencyEvacuationByIds(Integer[] ids) {
        return skEmergencyEvacuationMapper.deleteSkEmergencyEvacuationByIds(ids);
    }

}

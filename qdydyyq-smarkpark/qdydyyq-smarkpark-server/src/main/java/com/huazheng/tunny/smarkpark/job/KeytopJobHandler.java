package com.huazheng.tunny.smarkpark.job;

import cn.hutool.core.date.DateUtil;
import com.huazheng.tunny.smarkpark.util.KeytopUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 科拓智慧人行平台数据同步定时任务
 *
 * <AUTHOR>
 * @date 2025/5/6
 */
@Slf4j
@Component
public class KeytopJobHandler {
    @Resource
    private KeytopUtil keytopUtil;

    /**
     * 同步门禁设备列表
     * <p>
     * 定时调用时按每小时同步一次
     */
    @XxlJob("syncDoorDevicePageJobHandler")
    public ReturnT<String> syncDoorDevicePage(String param) throws Exception {
        log.info("同步门禁设备列表开始 " + DateUtil.now());
        //设置请求参数
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("current", 1);
        paramMap.put("size", 100);
        keytopUtil.syncDoorDevicePage(paramMap, keytopUtil.getToken(false));

        log.info("同步门禁设备列表成功 " + DateUtil.now());
        return ReturnT.SUCCESS;
    }

    /**
     * 同步人员通行记录
     * <p>
     * 定时调用时按每1分钟同步一次，同步5分钟内的数据
     */
    @XxlJob("syncDoorOutIntoInfoJobHandler")
    public ReturnT<String> syncDoorOutIntoInfo(String param) throws Exception {
        log.info("同步人员通行记录开始 " + DateUtil.now());
        //设置请求参数
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("current", 1);
        paramMap.put("size", 100000);
        paramMap.put("startTime", DateUtil.offsetMinute(DateUtil.date(), -65).toString());
        paramMap.put("endTime", DateUtil.now());

        keytopUtil.syncDoorOutIntoInfo(paramMap, keytopUtil.getToken(false));

        log.info("同步人员通行记录成功 " + DateUtil.now());
        return ReturnT.SUCCESS;
    }

    /**
     * 同步门禁信心
     * <p>
     * 定时调用时按每10分钟同步一次
     */
    @XxlJob("syncDoorInfoInfoJobHandler")
    public ReturnT<String> syncDoorInfoInfo(String param) throws Exception {
        log.info("同步门禁开始 " + DateUtil.now());
        //设置请求参数
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("buildingId", "");
        paramMap.put("deviceStatus", "");
        paramMap.put("doorName", "");
        paramMap.put("status", "");

        keytopUtil.syncDoorInfoInfo(paramMap, keytopUtil.getToken(false));

        log.info("同步门禁结束 " + DateUtil.now());
        return ReturnT.SUCCESS;
    }

    /**
     * 同步门禁状态
     * <p>
     * 定时调用时按每1分钟同步一次，同步5分钟内的数据
     */
    @XxlJob("syncDoorStatusJobHandler")
    public ReturnT<String> syncDoorStatus(String param) throws Exception {
        log.info("同步门禁开门状态开始 " + DateUtil.now());
        keytopUtil.syncDoorStatus(keytopUtil.getToken(false));
        log.info("同步门禁开门状态结束 " + DateUtil.now());
        return ReturnT.SUCCESS;
    }

}

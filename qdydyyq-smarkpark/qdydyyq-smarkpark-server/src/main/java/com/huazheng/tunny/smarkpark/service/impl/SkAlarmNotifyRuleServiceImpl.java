package com.huazheng.tunny.smarkpark.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import com.huazheng.tunny.admin.api.entity.SysDict;
import com.huazheng.tunny.admin.api.entity.SysUser;
import com.huazheng.tunny.smarkpark.mapper.SkAlarmNotifyRuleMapper;
import com.huazheng.tunny.smarkpark.api.entity.SkAlarmNotifyRule;
import com.huazheng.tunny.smarkpark.service.SkAlarmNotifyRuleService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;
import java.util.Map;

@Data
@Service("skAlarmNotifyRuleService")
public class SkAlarmNotifyRuleServiceImpl extends ServiceImpl<SkAlarmNotifyRuleMapper, SkAlarmNotifyRule> implements SkAlarmNotifyRuleService {

    @Autowired
    private SkAlarmNotifyRuleMapper skAlarmNotifyRuleMapper;

    /**
     * 查询安防联动管理信息
     *
     * @param id 安防联动管理ID
     * @return 安防联动管理信息
     */
    @Override
    public SkAlarmNotifyRule selectSkAlarmNotifyRuleById(Integer id) {
        return skAlarmNotifyRuleMapper.selectSkAlarmNotifyRuleById(id);
    }

    /**
     * 查询安防联动管理列表
     *
     * @param skAlarmNotifyRule 安防联动管理信息
     * @return 安防联动管理集合
     */
    @Override
    public List<SkAlarmNotifyRule> selectSkAlarmNotifyRuleList(SkAlarmNotifyRule skAlarmNotifyRule) {
        return skAlarmNotifyRuleMapper.selectSkAlarmNotifyRuleList(skAlarmNotifyRule);
    }


    /**
     * 分页模糊查询安防联动管理列表
     *
     * @return 安防联动管理集合
     */
    @Override
    public Page selectSkAlarmNotifyRuleListByLike(Query query) {
        SkAlarmNotifyRule skAlarmNotifyRule = BeanUtil.toBean(query.getCondition(), SkAlarmNotifyRule.class, CopyOptions.create());
        query.setRecords(skAlarmNotifyRuleMapper.selectSkAlarmNotifyRuleListByLike(query, skAlarmNotifyRule));
        return query;
    }

    /**
     * 新增安防联动管理
     *
     * @param skAlarmNotifyRule 安防联动管理信息
     * @return 结果
     */
    @Override
    public int insertSkAlarmNotifyRule(SkAlarmNotifyRule skAlarmNotifyRule) {
        return skAlarmNotifyRuleMapper.insertSkAlarmNotifyRule(skAlarmNotifyRule);
    }

    /**
     * 修改安防联动管理
     *
     * @param skAlarmNotifyRule 安防联动管理信息
     * @return 结果
     */
    @Override
    public int updateSkAlarmNotifyRule(SkAlarmNotifyRule skAlarmNotifyRule) {
        return skAlarmNotifyRuleMapper.updateSkAlarmNotifyRule(skAlarmNotifyRule);
    }


    /**
     * 删除安防联动管理
     *
     * @param id 安防联动管理ID
     * @return 结果
     */
    @Override
    public int deleteSkAlarmNotifyRuleById(Integer id) {
        return skAlarmNotifyRuleMapper.deleteSkAlarmNotifyRuleById(id);
    }

    ;


    /**
     * 批量删除安防联动管理对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteSkAlarmNotifyRuleByIds(Integer[] ids) {
        return skAlarmNotifyRuleMapper.deleteSkAlarmNotifyRuleByIds(ids);
    }

    @Override
    public List<Map<String, Object>> getAlarmEventTypeList(String alarmTypeCode, String eventTypeName) {
        return skAlarmNotifyRuleMapper.getAlarmEventTypeList(alarmTypeCode, eventTypeName);
    }

    @Override
    public List<SysUser> getPersonList(String userRealname) {
        return skAlarmNotifyRuleMapper.getPersonList(userRealname);
    }

}

package com.huazheng.tunny.smarkpark.mapper;

import com.huazheng.tunny.smarkpark.api.entity.DahuaOrganization;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 大华同步-组织架构  mapper层
 *
 * <AUTHOR>
 * @date 2025-04-29 11:46:06
 */
public interface DahuaOrganizationMapper extends BaseMapper<DahuaOrganization> {
    /**
     * 查询大华同步-组织架构信息
     *
     * @param orgSn 大华同步-组织架构ID
     * @return 大华同步-组织架构信息
     */
    public DahuaOrganization selectDahuaOrganizationById(String orgSn);

    /**
     * 查询大华同步-组织架构列表
     *
     * @param dahuaOrganization 大华同步-组织架构信息
     * @return 大华同步-组织架构集合
     */
    public List<DahuaOrganization> selectDahuaOrganizationList(DahuaOrganization dahuaOrganization);

    /**
     * 模糊查询大华同步-组织架构列表
     *
     * @param dahuaOrganization 大华同步-组织架构信息
     * @return 大华同步-组织架构集合
     */
    public List<DahuaOrganization> selectDahuaOrganizationListByLike(DahuaOrganization dahuaOrganization);


    /**
     * 分页模糊查询大华同步-组织架构列表
     *
     * @param dahuaOrganization 大华同步-组织架构信息
     * @return 大华同步-组织架构集合
     */
    public List<DahuaOrganization> selectDahuaOrganizationListByLike(Query query, DahuaOrganization dahuaOrganization);


    /**
     * 新增大华同步-组织架构
     *
     * @param dahuaOrganization 大华同步-组织架构信息
     * @return 结果
     */
    public int insertDahuaOrganization(DahuaOrganization dahuaOrganization);

    /**
     * 修改大华同步-组织架构
     *
     * @param dahuaOrganization 大华同步-组织架构信息
     * @return 结果
     */
    public int updateDahuaOrganization(DahuaOrganization dahuaOrganization);

    /**
     * 删除大华同步-组织架构
     *
     * @param orgSn 大华同步-组织架构ID
     * @return 结果
     */
    public int deleteDahuaOrganizationById(String orgSn);

    /**
     * 批量删除大华同步-组织架构
     *
     * @param orgSns 需要删除的数据ID
     * @return 结果
     */
    public int deleteDahuaOrganizationByIds(Integer[] orgSns);

    /**
     * 清除大华同步-组织架构
     *
     * @return 结果
     */
    void truncateDahuaOrganization();

    /**
     * 批量新增大华同步-组织架构
     *
     * @param list 组织架构列表
     * @return 结果
     */
    void insertDahuaOrganizationList(List<DahuaOrganization> list);

}

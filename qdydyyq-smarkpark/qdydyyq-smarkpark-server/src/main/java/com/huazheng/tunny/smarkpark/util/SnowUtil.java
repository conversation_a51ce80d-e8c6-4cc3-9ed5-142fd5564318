package com.huazheng.tunny.smarkpark.util;

import cn.hutool.core.util.IdUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * @Description: 生成雪花 id;
 * @Author: wx
 * @Date: 2021/5/27 14:51
 */

@Slf4j
@Data
@Component
public class SnowUtil {

    @Value("${com.snow.machine-id:10}")
    private long machineId;
    @Value("${com.snow.data-center-id:10}")
    private long dataCenterId;

    private static long MACHINEID;
    private static long DATACENTERID;

    @PostConstruct
    public void getParam() {
        MACHINEID = machineId;
        DATACENTERID = dataCenterId;
    }

    public static long nextId() {
        return SnowUtil.nextId(MACHINEID, DATACENTERID);
    }

    public static long nextId(long machineId, long dataCenterId) {
        log.info("MACHINEID::" + MACHINEID + ":::" + "DATACENTERID::" + DATACENTERID);
        return IdUtil.getSnowflake(machineId, dataCenterId).nextId();
    }
}

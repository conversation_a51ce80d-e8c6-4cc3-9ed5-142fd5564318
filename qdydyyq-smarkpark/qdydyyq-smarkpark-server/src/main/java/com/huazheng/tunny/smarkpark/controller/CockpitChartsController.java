package com.huazheng.tunny.smarkpark.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.smarkpark.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 园区驾驶舱 - 图表
 */
@RestController
@Slf4j
public class CockpitChartsController {

    @Resource
    private SkCockpitMapPointsService skCockpitMapPointsService;
    @Autowired
    private SkAlarmLogService skAlarmLogService;
    @Autowired
    private DaHuaService daHuaService;
    @Autowired
    private ParkingVehicleRecordService parkingVehicleRecordService;

    /**
     * 图表 - 监控设备
     */
    @PostMapping("/charts/camerasStatistics")
    public R camerasStatistics() {
        return new R(skCockpitMapPointsService.statisticsCamerasNum());
    }

    /**
     * 图表 - 安防设备在线率
     */
    @PostMapping("/charts/camerasOnlineRate")
    public R camerasOnlineRate() {
        Map<String, Object> data = skCockpitMapPointsService.statisticsDeviceOnlineRate();
        return new R(data);
    }

    /**
     * 图表 - 告警分类
     */
    @PostMapping("/charts/alarmClassification")
    public R alarmClassification(@RequestBody Map<String, Object> params) {
        Map<String, Object> data = new HashMap<>();
        List<Map<String, Object>> list = skAlarmLogService.alarmClassification(params);
        int total = 0;
        for (Map<String, Object> map : list) {
            total += Integer.parseInt(map.get("total").toString());
        }
        for (Map<String, Object> map : list) {
            if (total > 0) {
                map.put("value", NumberUtil.roundStr(Double.parseDouble(map.get("total").toString()) / total * 100, 2));
            } else {
                map.put("value", 0);
            }
        }
        data.put("list", list);
        data.put("value", total);
        return new R(data);
    }

    /**
     * 图表 - 告警分类
     */
    @PostMapping("/charts/alarmLevel")
    public R alarmLevel(@RequestBody Map<String, Object> params) {
        Map<String, Object> data = new HashMap<>();
        List<Map<String, Object>> list = skAlarmLogService.alarmLevel(params);
        int total = 0;
        for (Map<String, Object> map : list) {
            total += Integer.parseInt(map.get("total").toString());
        }
        for (Map<String, Object> map : list) {
            if (total > 0) {
                map.put("value", NumberUtil.roundStr(Double.parseDouble(map.get("total").toString()) / total * 100, 2));
            } else {
                map.put("value", 0);
            }
        }
        data.put("list", list);
        data.put("value", total);
        return new R(data);
    }


    /**
     * 图表 - 告警统计
     */
    @PostMapping("/charts/alarmStatistics")
    public R alarmStatistics(@RequestBody Map<String, Object> params) {
        Map<String, Object> data = new HashMap<>();
        List<Map<String, Object>> list = skAlarmLogService.alarmStatistics(params);
        Integer total = 0;
        for (Map<String, Object> map : list) {
            total += Integer.parseInt(map.get("value").toString());
        }
        data.put("list", list);
        data.put("total", total);
        return new R(data);
    }

    /**
     * 图表 - 告警趋势
     */
    @PostMapping("/charts/alarmTrend")
    public R alarmTrend(@RequestBody Map<String, Object> params) {
        Map<String, Object> data = new HashMap<>();
        data.put("lastYear", skAlarmLogService.alarmTrend(DateUtil.thisYear() - 1, String.valueOf(params.get("alarmSource"))).stream().map(item -> item.get("total")).collect(Collectors.toList()));
        data.put("thisYear", skAlarmLogService.alarmTrend(DateUtil.thisYear(), String.valueOf(params.get("alarmSource"))).stream().map(item -> item.get("total")).collect(Collectors.toList()));
        return new R(data);
    }

    /**
     * 图表 - 告警列表
     */
    @PostMapping("/charts/alarmLogList")
    public Page alarmLogList(@RequestParam Map<String, Object> params) {
        params.put("page", params.get("page") == null ? 1 : params.get("page"));
        params.put("limit", params.get("limit") == null ? 20 : params.get("limit"));
        params.put("handleStatus", params.get("handleStatus") == null ? 0 : params.get("handleStatus"));
        return skAlarmLogService.selectSkAlarmLogListByLike(new Query<>(params));
    }


    /**
     * 图表 - 监控设备
     */
    @PostMapping("/charts/fireFightEquipmentStatistics")
    public R fireFightEquipmentStatistics() {
        return new R(skCockpitMapPointsService.fireFightEquipmentStatistics());
    }

    /**
     * 图表 - 消防设备在线率
     */
    @PostMapping("/charts/fireFightEquipmentOnlineRate")
    public R fireFightEquipmentOnlineRate() {
        Map<String, Object> data = new HashMap<>();
        List<Map<String, Object>> list = skCockpitMapPointsService.fireFightEquipmentOnlineRate();
        Double total = 0d;
        Double online = 0d;
        for (Map<String, Object> map : list) {
            total += Double.parseDouble(map.get("value").toString());
            if ("在线".equals(map.get("name"))) {
                online = Double.parseDouble(map.get("value").toString());
            }
        }
        data.put("list", list);
        if (total > 0) {
            data.put("ratio", NumberUtil.roundStr(online / total * 100, 2));
        } else {
            data.put("ratio", 0.00);
        }

        return new R(data);
    }

    /**
     * 图表 - 通行设备态势
     */
    @PostMapping("/charts/trafficEquipmentSituation")
    public R trafficEquipmentSituation() {
        Map<String, Object> data = new HashMap<>();
        List<Map<String, Object>> list = skCockpitMapPointsService.trafficEquipmentSituation();
        data.put("deviceList", list);
        return new R(data);
    }

    /**
     * 图表 - 通行设备态势明细
     */
    @GetMapping("/charts/trafficEquipmentInfo")
    public R trafficEquipmentInfo(@RequestParam Map<String, Object> params) {
        return skCockpitMapPointsService.trafficEquipmentInfo(new Query<>(params));
    }

    /**
     * 图表 - 通行车辆统计
     */
    @PostMapping("/charts/vehicleStatistics")
    public R vehicleStatistics() {
        Map<String, Object> data = new HashMap<>();
        List<Map<String, Object>> list = skCockpitMapPointsService.vehicleStatistics();
        data.put("carCountList", list);
        return new R(data);
    }

    /**
     * 图表 - 通行车辆列表
     */
    @PostMapping("/charts/vehicleList")
    public R vehicleList(@RequestParam Map<String, Object> params) {
        params.put("page", params.get("page") == null ? 1 : params.get("page"));
        params.put("limit", params.get("limit") == null ? 20 : params.get("limit"));
        params.put("inoutType", "into");
        return parkingVehicleRecordService.selectParkingVehicleRecordListByLike(new Query<>(params));
    }

    /**
     * 图表 - 客流分布
     */
    @PostMapping("/charts/passengerDistribution")
    public R passengerDistribution() {
        Map<String, Object> data = new HashMap<>();
        List<Map<String, Object>> list = daHuaService.passengerDistribution();
        Integer total = 0;
        for (Map<String, Object> map : list) {
            total += Integer.parseInt(map.get("value").toString());
        }
        data.put("list", list);
        data.put("total", total);
        return new R(data);
    }

    /**
     * 图表 - 楼控设备按类型统计
     */
    @PostMapping("/charts/buildingControlDeviceType")
    public R buildingControlDeviceByType() {
        Map<String, Object> data = new HashMap<>();
        List<Map<String, Object>> list = skCockpitMapPointsService.buildingControlDeviceByType();
        Integer total = 0;
        for (Map<String, Object> map : list) {
            total += Integer.parseInt(map.get("value").toString());
        }
        data.put("list", list);
        data.put("total", total);
        return new R(data);
    }

    /**
     * 图表 - 楼控设备状态
     */
    @PostMapping("/charts/buildingControlDeviceOnlineRate")
    public R buildingControlDeviceState() {
        Map<String, Object> map = skCockpitMapPointsService.buildingControlDeviceState();
        return new R(map);
    }
    /**
     * 图表 - 园区环境
     */
    @PostMapping("/charts/parkEnvironment")
    public R parkEnvironment() {
        return skCockpitMapPointsService.parkEnvironment();
    }

    /**
     * 图表 - 用能趋势
     */
    @PostMapping("/charts/energyTrends")
    public R energyTrends(@RequestBody Map<String, Object> params) {
        return skCockpitMapPointsService.energyTrends(params);
    }


}

package com.huazheng.tunny.smarkpark;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;


@EnableDiscoveryClient
@SpringBootApplication
@EnableFeignClients({"com.huazheng.tunny.smarkpark.api.feign"})
public class TunnySmarkParkApplication {

	public static void main(String[] args) {
		SpringApplication.run(TunnySmarkParkApplication.class, args);
	}
}

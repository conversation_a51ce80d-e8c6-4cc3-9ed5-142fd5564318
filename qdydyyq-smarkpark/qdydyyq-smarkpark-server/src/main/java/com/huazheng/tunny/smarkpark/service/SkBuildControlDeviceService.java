package com.huazheng.tunny.smarkpark.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.smarkpark.api.entity.SkBuildControlDevice;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 楼控设备表 服务接口层
 *
 * <AUTHOR>
 * @date 2025-06-04 09:16:07
 */
public interface SkBuildControlDeviceService extends IService<SkBuildControlDevice> {
    /**
     * 查询楼控设备表信息
     *
     * @param id 楼控设备表ID
     * @return 楼控设备表信息
     */
    public SkBuildControlDevice selectSkBuildControlDeviceById(Long id);

    /**
     * 查询楼控设备表列表
     *
     * @param skBuildControlDevice 楼控设备表信息
     * @return 楼控设备表集合
     */
    public List<SkBuildControlDevice> selectSkBuildControlDeviceList(SkBuildControlDevice skBuildControlDevice);


    /**
     * 分页模糊查询楼控设备表列表
     * @return 楼控设备表集合
     */
    public R selectSkBuildControlDeviceListByLike(Query query);



    /**
     * 新增楼控设备表
     *
     * @param skBuildControlDevice 楼控设备表信息
     * @return 结果
     */
    public int insertSkBuildControlDevice(SkBuildControlDevice skBuildControlDevice);

    /**
     * 修改楼控设备表
     *
     * @param skBuildControlDevice 楼控设备表信息
     * @return 结果
     */
    public int updateSkBuildControlDevice(SkBuildControlDevice skBuildControlDevice);

    /**
     * 删除楼控设备表
     *
     * @param id 楼控设备表ID
     * @return 结果
     */
    public int deleteSkBuildControlDeviceById(Long id);

    /**
     * 批量删除楼控设备表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteSkBuildControlDeviceByIds(Integer[] ids);

    SkBuildControlDevice selectSkBuildControlDeviceByCode(String deviceCode);

    void syncDeviceStatus();

    void syncLightingDevice();

    void syncDeviceEvent();
}


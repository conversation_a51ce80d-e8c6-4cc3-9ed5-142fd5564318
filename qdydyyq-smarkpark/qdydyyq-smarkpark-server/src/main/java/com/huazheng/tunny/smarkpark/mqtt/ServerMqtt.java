package com.huazheng.tunny.smarkpark.mqtt;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @date 2020/3/30 13:39
 */
@Slf4j
public class ServerMqtt {

    //tcp://MQTT安装的服务器地址:MQTT定义的端口号
    public static String host;
    //定义MQTT的ID，可以在MQTT服务配置中指定
    private static String clientid = "clientIot";
    private static String userName = "adminIot";
    private static String passWord = "public";
    private static MqttClient client;
    private static Map<String, MqttTopic> topicMap = new HashMap<String, MqttTopic>();
    private static MqttMessage message;

    /**
     * 用来连接服务器
     */
    public static void connect(String host, String id) throws MqttException {
        host = host;
        if (StrUtil.isNotBlank(id)) {
            clientid = id;
        }
        connect();
    }

    /**
     * 用来连接服务器
     */
    public static void connect() throws MqttException {
        // MemoryPersistence设置clientid的保存形式，默认为以内存保存
        client = new MqttClient(host, clientid, new MemoryPersistence());

        MqttConnectOptions options = new MqttConnectOptions();
        options.setCleanSession(false);
        options.setUserName(userName);
        options.setPassword(passWord.toCharArray());
        try {
            client.setCallback(new Callback());
            client.connect(options);
            log.info("MQTT 链接创建成功:" + host);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 用来连接服务器
     */
    public static void publish(String topicStr, String date, int qos) throws MqttException {
        if (message == null) {
            message = new MqttMessage();
        }
        // 保证消息能到达一次
        message.setQos(qos);
        message.setRetained(true);
        message.setPayload(date.getBytes());
        MqttTopic topic = getTopic(topicStr);
        publish(topic, message);
        log.info("MQTT 发送状态:" + message.isRetained());
    }

    /**
     * @throws MqttPersistenceException
     * @throws MqttException
     */
    private static MqttTopic getTopic(String topicStr) throws MqttException {
        if (topicMap.get(topicStr) != null) {
            return topicMap.get(topicStr);
        }
        if (client == null) {
            connect();
        }
        MqttTopic topic = client.getTopic(topicStr);
        topicMap.put("MQTT 发送 Topic:", topic);
        return topic;
    }

    /**
     * @param topic
     * @param message
     * @throws MqttPersistenceException
     * @throws MqttException
     */
    private static void publish(MqttTopic topic, MqttMessage message) throws MqttPersistenceException,
            MqttException {

        MqttDeliveryToken token = topic.publish(message);
        log.info("ServerMQTT publish token " + System.currentTimeMillis() + " " + message.toString());
        log.info("ServerMQTT publish end " + System.currentTimeMillis() + " " + message.toString());
    }

    /**
     * 订阅某个主题 qos默认为1
     *
     * @param topic
     */
    public static void subscribe(String topic) {
        log.info("MQTT 监听:" + topic);
        subscribe(topic, 0);
    }

    /**
     * 订阅某个主题
     *
     * @param topic
     * @param qos
     */
    private static void subscribe(String topic, int qos) {
        try {
            client.subscribe(topic, qos);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}

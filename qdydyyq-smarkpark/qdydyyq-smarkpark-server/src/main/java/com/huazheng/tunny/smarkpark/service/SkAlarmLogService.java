package com.huazheng.tunny.smarkpark.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.smarkpark.api.entity.SkAlarmLog;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;
import java.util.Map;

/**
 * 告警记录 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2024-07-16 15:25:31
 */
public interface SkAlarmLogService extends IService<SkAlarmLog> {

    /**
     * 分页模糊查询告警记录列表
     *
     * @return 告警记录集合
     */
    public Page selectSkAlarmLogListByLike(Query query);


    void handleBatch(SkAlarmLog skAlarmLog);

    Integer getAlarmNotifyCount(String userName);

    List<Map<String, Object>> alarmClassification(Map<String, Object> params);

    List<Map<String, Object>> alarmStatistics(Map<String, Object> params);

    List<Map<String, Object>> alarmTrend(int year, String alarmSource);

    List<String> selectMailsByUsers(List<String> users);

    SkAlarmLog selectSkAlarmLogById(String eventId);

    String getFireControlDeviceName(String deviceCode);

    /**迁移30天前的告警日志到备份表*/
    void transferDataToHisTable(Integer days);

    /**清理30天前的告警日志*/
    void cleanUpAlarmLog(Integer days);

    List<Map<String, Object>> alarmLevel(Map<String, Object> params);

    Integer getAlarmNotifyType(String userName);
}


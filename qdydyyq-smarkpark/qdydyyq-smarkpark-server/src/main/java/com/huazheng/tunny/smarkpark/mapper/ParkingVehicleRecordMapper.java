package com.huazheng.tunny.smarkpark.mapper;

import com.huazheng.tunny.smarkpark.api.entity.ParkingVehicleRecord;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.huazheng.tunny.common.core.util.Query;
import java.util.List;
/**
 * 停车场-车辆记录  mapper层
 *
 * <AUTHOR>
 * @date 2025-06-10 13:30:26
 */
public interface ParkingVehicleRecordMapper extends BaseMapper<ParkingVehicleRecord> {
    /**
     * 查询停车场-车辆记录信息
     *
     * @param id 停车场-车辆记录ID
     * @return 停车场-车辆记录信息
     */
    public ParkingVehicleRecord selectParkingVehicleRecordById(String id);

    /**
     * 查询停车场-车辆记录列表
     *
     * @param parkingVehicleRecord 停车场-车辆记录信息
     * @return 停车场-车辆记录集合
     */
    public List<ParkingVehicleRecord> selectParkingVehicleRecordList(ParkingVehicleRecord parkingVehicleRecord);

    /**
     * 模糊查询停车场-车辆记录列表
     *
     * @param parkingVehicleRecord 停车场-车辆记录信息
     * @return 停车场-车辆记录集合
     */
    public List<ParkingVehicleRecord> selectParkingVehicleRecordListByLike(ParkingVehicleRecord parkingVehicleRecord);


    /**
     * 分页模糊查询停车场-车辆记录列表
     *
     * @param parkingVehicleRecord 停车场-车辆记录信息
     * @return 停车场-车辆记录集合
     */
    public List<ParkingVehicleRecord> selectParkingVehicleRecordListByLike(Query query, ParkingVehicleRecord parkingVehicleRecord);


    /**
     * 新增停车场-车辆记录
     *
     * @param parkingVehicleRecord 停车场-车辆记录信息
     * @return 结果
     */
    public int insertParkingVehicleRecord(ParkingVehicleRecord parkingVehicleRecord);

    /**
     * 修改停车场-车辆记录
     *
     * @param parkingVehicleRecord 停车场-车辆记录信息
     * @return 结果
     */
    public int updateParkingVehicleRecord(ParkingVehicleRecord parkingVehicleRecord);

    /**
     * 删除停车场-车辆记录
     *
     * @param id 停车场-车辆记录ID
     * @return 结果
     */
    public int deleteParkingVehicleRecordById(String id);

    /**
     * 批量删除停车场-车辆记录
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteParkingVehicleRecordByIds(Integer[] ids);


    int selectInParkingVehicleRecordCount();
}

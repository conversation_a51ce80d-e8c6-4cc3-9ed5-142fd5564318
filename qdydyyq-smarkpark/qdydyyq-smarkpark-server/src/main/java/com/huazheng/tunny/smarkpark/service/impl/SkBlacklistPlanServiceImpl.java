package com.huazheng.tunny.smarkpark.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import com.huazheng.tunny.smarkpark.mapper.SkBlacklistPlanMapper;
import com.huazheng.tunny.smarkpark.api.entity.SkBlacklistPlan;
import com.huazheng.tunny.smarkpark.service.SkBlacklistPlanService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Data
@Service("skBlacklistPlanService")
public class SkBlacklistPlanServiceImpl extends ServiceImpl<SkBlacklistPlanMapper, SkBlacklistPlan> implements SkBlacklistPlanService {

    @Autowired
    private SkBlacklistPlanMapper skBlacklistPlanMapper;

    /**
     * 查询人员布控计划&分组信息
     *
     * @param planIndexCode 人员布控计划&分组ID
     * @return 人员布控计划&分组信息
     */
    @Override
    public SkBlacklistPlan selectSkBlacklistPlanById(String planIndexCode) {
        return skBlacklistPlanMapper.selectSkBlacklistPlanById(planIndexCode);
    }

    /**
     * 查询人员布控计划&分组列表
     *
     * @param skBlacklistPlan 人员布控计划&分组信息
     * @return 人员布控计划&分组集合
     */
    @Override
    public List<SkBlacklistPlan> selectSkBlacklistPlanList(SkBlacklistPlan skBlacklistPlan) {
        return skBlacklistPlanMapper.selectSkBlacklistPlanList(skBlacklistPlan);
    }


    /**
     * 分页模糊查询人员布控计划&分组列表
     *
     * @return 人员布控计划&分组集合
     */
    @Override
    public Page selectSkBlacklistPlanListByLike(Query query) {
        SkBlacklistPlan skBlacklistPlan = BeanUtil.toBean(query.getCondition(), SkBlacklistPlan.class, CopyOptions.create());
        query.setRecords(skBlacklistPlanMapper.selectSkBlacklistPlanListByLike(query, skBlacklistPlan));
        return query;
    }

    /**
     * 新增人员布控计划&分组
     *
     * @param skBlacklistPlan 人员布控计划&分组信息
     * @return 结果
     */
    @Override
    public int insertSkBlacklistPlan(SkBlacklistPlan skBlacklistPlan) {
        return skBlacklistPlanMapper.insertSkBlacklistPlan(skBlacklistPlan);
    }

    /**
     * 修改人员布控计划&分组
     *
     * @param skBlacklistPlan 人员布控计划&分组信息
     * @return 结果
     */
    @Override
    public int updateSkBlacklistPlan(SkBlacklistPlan skBlacklistPlan) {
        return skBlacklistPlanMapper.updateSkBlacklistPlan(skBlacklistPlan);
    }


    /**
     * 删除人员布控计划&分组
     *
     * @param planIndexCode 人员布控计划&分组ID
     * @return 结果
     */
    @Override
    public int deleteSkBlacklistPlanById(String planIndexCode) {
        return skBlacklistPlanMapper.deleteSkBlacklistPlanById(planIndexCode);
    }

    ;


    /**
     * 批量删除人员布控计划&分组对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteSkBlacklistPlanByIds(Integer[] planIndexCodes) {
        return skBlacklistPlanMapper.deleteSkBlacklistPlanByIds(planIndexCodes);
    }

}

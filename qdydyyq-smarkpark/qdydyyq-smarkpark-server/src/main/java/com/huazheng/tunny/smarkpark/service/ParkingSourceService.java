package com.huazheng.tunny.smarkpark.service;

import com.huazheng.tunny.smarkpark.api.entity.ParkingBarrierGate;
import com.huazheng.tunny.smarkpark.api.entity.ParkingVehicleRecord;

import java.util.HashMap;
import java.util.List;

/**
 * 停车场-车辆道闸 服务接口层
 *
 * <AUTHOR>
 * @date 2025-06-10 13:30:19
 */
public interface ParkingSourceService {
    List<ParkingBarrierGate> cronParkingBarrierGate(HashMap<Object, Object> objectObjectHashMap);

    List<ParkingVehicleRecord> cronVehicleRecord(HashMap<Object, Object> objectObjectHashMap);

    void selectParkingSpacesNum();
}


package com.huazheng.tunny.smarkpark.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.smarkpark.api.entity.SkAlarmLogRaw;
import com.huazheng.tunny.smarkpark.mapper.SkAlarmLogRawMapper;
import com.huazheng.tunny.smarkpark.service.SkAlarmLogRawService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service("skAlarmLogRawService")
public class SkAlarmLogRawServiceImpl extends ServiceImpl<SkAlarmLogRawMapper, SkAlarmLogRaw> implements SkAlarmLogRawService {

}

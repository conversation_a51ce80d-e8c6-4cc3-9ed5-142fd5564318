package com.huazheng.tunny.smarkpark.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.smarkpark.api.dto.DaHuaCamerasDTO;
import com.huazheng.tunny.smarkpark.api.dto.RegionsAndCamerasDto;
import com.huazheng.tunny.smarkpark.api.entity.*;
import com.huazheng.tunny.smarkpark.mapper.DahuaCamerasMapper;
import com.huazheng.tunny.smarkpark.mapper.DahuaOrganizationMapper;
import com.huazheng.tunny.smarkpark.mapper.DahuaPfsRegionChannelMapper;
import com.huazheng.tunny.smarkpark.mapper.DahuaPfsRegionMapper;
import com.huazheng.tunny.smarkpark.service.DaHuaService;
import com.huazheng.tunny.smarkpark.util.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @Description: Service
 * @Author: zhaohaoran
 * @Date: 2023-10-24 10:30:03
 */
@Service
@Slf4j
public class DahuaServiceImpl extends ServiceImpl<DahuaCamerasMapper, DahuaCameras> implements DaHuaService {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private DahuaOrganizationMapper dahuaOrganizationMapper;

    @Autowired
    private DahuaCamerasMapper dahuaCamerasMapper;
    @Autowired
    private DahuaPfsRegionMapper dahuaPfsRegionMapper;
    @Autowired
    private DahuaPfsRegionChannelMapper dahuaPfsRegionChannelMapper;

    @Value("${dahua.http:}")
    private String http;

    @Value("${dahua.clientId:}")
    private String clientId;

    @Value("${dahua.clientSecret:}")
    private String clientSecret;

    @Value("${dahua.userName:}")
    private String userName;

    @Value("${dahua.password:}")
    private String password;

    @Value("${dahua.sso.redirect:}")
    private String redirectUrl;

    @Value("${dahua.tokenKey:bearer}")
    private String tokenKey;

    public static String getRequestBody(HttpServletRequest request) {
        BufferedReader br = null;
        StringBuilder sb = new StringBuilder();
        String str = "";
        try {
            br = request.getReader();
            while ((str = br.readLine()) != null) {
                sb.append(str);
            }
            br.close();
        } catch (IOException e) {
            e.printStackTrace();
            if (br != null) {
                try {
                    br.close();
                } catch (IOException eo) {
                    eo.printStackTrace();
                }
            }
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return sb.toString();
    }

    /**
     * @Description: 获取大华token
     * @Param:
     * @Return: String
     * @Author: zhaohaoran
     * @Date: 2023-10-24 10:30:03
     */
    @Override
    public String getToken() throws Exception {
        //获取大华token
        String token;
        //先查询redis内是否存储大华token，否则请求大华接口获取token
        String redisToken = redisTemplate.opsForValue().get("dh_access_token");
        if (StrUtil.isNotEmpty(redisToken)) {
            token = redisToken;
        } else {
            token = HttpUtils.getToken(redisTemplate, http, userName, password, clientId, clientSecret);
        }
        return token;
    }

    @Override
    public String eventSubscribe(HttpServletRequest req) throws Exception {
        String token = getToken();
        String mqinfo = getRequestBody(req);
        // 接收事件地址
//        String mqinfo ="{\"param\": {\"monitors\": [{\"monitor\": \"http://*************:10172/park/pass/saveParkPass\",\"monitorType\": \"url\",\"events\": [{\"category\": \"alarm\",\"subscribeAll\": 1,\"domainSubscribe\": 2}]}],\"subsystem\": {\"subsystemType\": 0,\"name\": \"*************_10172\",\"magic\": \"*************_10172\"}}}";

        HttpRequest request = HttpRequest.post(http + "/evo-apigw/evo-event/1.0.0/subscribe/mqinfo").header("Content-Type", "application/json").header("Authorization", "Bearer " + token).body(mqinfo);
        return request.execute().body();
        // - ----结束执行----事件订阅------返回报文:{"success":true,"data":{},"code":"0","errMsg":""}
    }

    /**
     * @Description: 全量同步组织架构
     * @Param:
     * @Return: String
     * @Author: sj
     * @Date: 2023-10-24 10:30:03
     */
    @Override
    public R cronOrganizationList(Map<String, Object> param) throws Exception {
        List<DahuaOrganization> list = new ArrayList<>();
        String token = getToken();
        int pageNo = 1;
        int pageSize = 100;
        int totalPage = 0;
        do {
            param.put("pageNum", pageNo);
            param.put("pageSize", pageSize);
            param.put("maxRangeId", 100000);
            String url = http + "/evo-apigw/evo-brm/1.2.1/organization/page";
            String result = HttpRequest.get(url).header("Authorization", tokenKey + " " + token).form(param).execute().body();
            log.info("获取大华组织列表接口{}:{}", url, JSONUtil.toJsonStr(result));
            JSONObject object = JSONUtil.parseObj(result);
            if ("0".equals(object.getStr("code"))) {
                JSONObject dataObject = JSONUtil.parseObj(object.getStr("data"));
                totalPage = dataObject.getInt("totalPage");
                List<JSONObject> deviceList = JSONUtil.toList(JSONUtil.parseArray(dataObject.getStr("pageData")), JSONObject.class);
                if (CollectionUtil.isNotEmpty(deviceList)) {
                    for (JSONObject deviceObject : deviceList) {
                        DahuaOrganization organization = JSONUtil.toBean(deviceObject, DahuaOrganization.class);
                        organization.setUpdateTime(LocalDateTime.now());
                        if (organization.getOrgCode().length() > 3) {
                            organization.setParentCode(organization.getOrgCode().substring(0, organization.getOrgCode().length() - 3));
                        } else {
                            organization.setParentCode("-1");
                        }
                        list.add(organization);
                    }
                }
            }
        } while (pageNo++ < totalPage);

        //批量插入大华组织机构数据
        if (CollUtil.isNotEmpty(list)) {
            this.insertDahuaOrganization(list);
        }
        return R.success();
    }

    /**
     * 批量插入大华组织机构数据
     * 方法会先清空表数据，然后分批次插入新数据，每批最多插入50条记录
     *
     * @param list 需要插入的组织机构数据列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertDahuaOrganization(List<DahuaOrganization> list) {
        // 清空现有数据（注意：该操作不可逆）
        dahuaOrganizationMapper.truncateDahuaOrganization();

        // 分批次插入数据（每批50条，避免单次SQL数据量过大）
        int step = 50;
        for (int i = 0; i < list.size(); i += step) {
            // 计算当前批次的结束索引（防止越界）
            int endIndex = (i + step) < list.size() ? (i + step) : list.size();
            dahuaOrganizationMapper.insertDahuaOrganizationList(list.subList(i, endIndex));
        }
    }


    /**
     * @Description: 全量同步设备
     * @Param:
     * @Return: String
     * @Author: sj
     * @Date: 2023-10-24 10:30:03
     */
    @Override
    public R cronDeviceList(Map<String, Object> param) throws Exception {
        List<DahuaCameras> list = new ArrayList<>();
        String token = getToken();
        int pageNo = 1;
        int pageSize = 100;
        int totalPage = 0;
        do {
            param.put("pageNum", pageNo);
            param.put("pageSize", pageSize);
            param.put("maxRangeId", 100000);
            param.put("syncCascadeData", 0);
            param.put("syncVirtualData", 0);
            List<Map<String, Object>> deviceExtendConditionList = new ArrayList<>();
            Map<String, Object> condition = new HashMap<>();
            //condition.put("categorys", Arrays.asList("1", "8", "30", "35", "36"));
            deviceExtendConditionList.add(condition);
            param.put("deviceExtendConditionList", deviceExtendConditionList);
            String url = http + "/evo-apigw/evo-brm/1.2.1/device/list-page";
            //log.info("token:{}", token);
            String result = HttpRequest.post(url).header("Authorization", tokenKey + " " + token).body(JSONUtil.toJsonStr(param)).execute().body();
            log.info("获取大华设备列表接口{}:{}", url, JSONUtil.toJsonStr(result));
            JSONObject object = JSONUtil.parseObj(result);
            if ("0".equals(object.getStr("code"))) {
                JSONObject dataObject = JSONUtil.parseObj(object.getStr("data"));
                totalPage = dataObject.getInt("totalPage");
                List<JSONObject> deviceList = JSONUtil.toList(JSONUtil.parseArray(dataObject.getStr("pageData")), JSONObject.class);
                if (CollectionUtil.isNotEmpty(deviceList)) {
                    for (JSONObject deviceObject : deviceList) {
                        deviceObject.remove("updateTime");
                        //通道编码
                        List<JSONObject> unitList = JSONUtil.toList(JSONUtil.parseArray(deviceObject.getStr("units")), JSONObject.class);
                        if (CollectionUtil.isNotEmpty(unitList)) {
                            List<JSONObject> channels = JSONUtil.toList(JSONUtil.parseArray(unitList.get(0).getStr("channels")), JSONObject.class);
                            if (CollectionUtil.isNotEmpty(channels)) {
                                for (JSONObject channelJson : channels) {
                                    DahuaCameras device = JSONUtil.toBean(deviceObject, DahuaCameras.class);
                                    if (device.getDeviceName().indexOf("解码器") >= 0) {
                                        continue;
                                    }
                                    device.setOwnerCode(channelJson.getStr("ownerCode"));
                                    device.setChannelCode(channelJson.getStr("channelCode"));
                                    device.setChannelName(channelJson.getStr("channelName"));
                                    device.setCameraType(channelJson.getStr("cameraType"));
                                    device.setChannelState(channelJson.getInt("isOnline"));
                                    device.setChannelStat(channelJson.getInt("stat"));
                                    device.setTreeSort(channelJson.getLong("treeSort"));
                                    device.setChannelCapability(channelJson.getStr("capability"));
                                    if (channelJson.get("chExt") != null && JSONUtil.isTypeJSONObject(channelJson.getStr("chExt"))) {
                                        JSONObject chExt = JSONUtil.parseObj(channelJson.getStr("chExt"));
                                        device.setChannelDeviceIp(chExt.getStr("channelDeviceIp"));
                                    }
                                    device.setUpdateTime(LocalDateTime.now());
                                    list.add(device);
                                }
                            }
                        }

                    }
                }
            }
        } while (pageNo++ < totalPage);

        //批量插入大华摄像头数据
        if (CollUtil.isNotEmpty(list)) {
            if (param.get("type") != null && "cronStatus".equals(param.get("type").toString())) {
                this.updateDahuaCameras(list);
            } else {
                this.insertDahuaCameras(list);
            }
        }
        return R.success();
    }

    /**
     * 批量插入大华摄像头数据
     * <p>
     * 方法流程：
     * 1. 清空目标表所有现有数据
     * 2. 将输入列表按固定步长分批次插入（避免单次SQL过大）
     *
     * @param list 待插入的大华摄像头数据集合（非空）
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertDahuaCameras(List<DahuaCameras> list) {
        // 清空表数据（DDL操作，自动提交禁用）
        dahuaCamerasMapper.truncateDahuaCameras();

        // 分批次插入配置（每批50条记录）
        int step = 50;
        for (int i = 0; i < list.size(); i += step) {
            // 计算当前分片的结束边界（防止越界）
            int endIndex = Math.min(i + step, list.size());
            // 执行当前分片的批量插入
            dahuaCamerasMapper.insertDahuaCamerasList(list.subList(i, endIndex));
        }
    }

    private void updateDahuaCamerasList(List<DahuaCameras> camerasList) {
        for (DahuaCameras dahuaCameras : camerasList) {
            String camerasInfo = redisTemplate.opsForValue().get("cameras:" + dahuaCameras.getChannelCode());
            if (StrUtil.isBlank(camerasInfo)) {
                DahuaCameras oldCameras = dahuaCamerasMapper.selectDahuaCamerasByChannelCode(dahuaCameras.getChannelCode());
                if (oldCameras == null) {
                    dahuaCamerasMapper.insertDahuaCameras(dahuaCameras);
                    redisTemplate.opsForValue().set("cameras:" + dahuaCameras.getChannelCode(), JSONUtil.toJsonStr(dahuaCameras));
                }
            } else {
                JSONObject json = JSONUtil.parseObj(camerasInfo);
                json.remove("updateTime");
                DahuaCameras cameras = JSONUtil.toBean(json, DahuaCameras.class);
                cameras.setOwnerCode(dahuaCameras.getOwnerCode());
                cameras.setChannelState(dahuaCameras.getChannelState());
                dahuaCamerasMapper.updateDahuaCamerasState(cameras);
                redisTemplate.opsForValue().set("cameras:" + dahuaCameras.getChannelCode(), JSONUtil.toJsonStr(cameras));
            }
        }
    }

    private void updateDahuaCameras(List<DahuaCameras> list) {
        for (DahuaCameras dahuaCameras : list) {
            String camerasInfo = redisTemplate.opsForValue().get("cameras:" + dahuaCameras.getChannelCode());
            if (StrUtil.isBlank(camerasInfo)) {
                DahuaCameras oldCameras = dahuaCamerasMapper.selectDahuaCamerasByChannelCode(dahuaCameras.getChannelCode());
                if (oldCameras == null) {
                    dahuaCamerasMapper.insertDahuaCameras(dahuaCameras);
                }
            } else {
                JSONObject json = JSONUtil.parseObj(camerasInfo);
                json.remove("updateTime");
                DahuaCameras cameras = JSONUtil.toBean(json, DahuaCameras.class);
                if (!cameras.getChannelState().equals(dahuaCameras.getChannelState())) {
                    dahuaCamerasMapper.updateDahuaCamerasState(dahuaCameras);
                }
            }
            redisTemplate.opsForValue().set("cameras:" + dahuaCameras.getChannelCode(), JSONUtil.toJsonStr(dahuaCameras));
        }
    }

    /**
     * @Description: 查询大华事件列表
     * @Param:
     * @Return: String
     * @Author: sj
     * @Date: 2023-10-24 10:30:03
     */
    @Override
    public R subscribeList(Map<String, Object> param) throws Exception {
        String token = getToken();
        String url = http + "/evo-apigw/evo-event/1.0.0/subscribe/subscribe-list?monitorType=url&category=" + param.get("type");
        String result = HttpRequest.get(url).header("Authorization", "Bearer " + token).execute().body();
        log.info("获取大华事件列表接口:{}", JSONUtil.toJsonStr(result));
        return R.success(result);
    }

    /**
     * @Description: 查询大华车场列表
     * @Param:
     * @Return: String
     * @Author: sj
     * @Date: 2023-10-24 10:30:03
     */
    @Override
    public Page queryParking(Query query) throws Exception {
        Map param = query.getCondition();
        String token = getToken();
        String url = http + "/evo-apigw/ipms/parkinglot/query?pageNum=" + query.getCurrent() + "&pageSize=" + query.getLimit() + "&returnPage=1";
        if (param.get("parkingLotFuzzy") != null && !"".equals(param.get("parkingLotFuzzy").toString())) {
            url = url + "&parkingLotFuzzy=" + param.get("parkingLotFuzzy");
        }
        log.info("请求大华停车场列表入参++++++++:" + url);
        String result = HttpRequest.get(url).header("Authorization", "Bearer " + token).execute().body();
        log.info("获取大华停车场列表接口:{}", JSONUtil.toJsonStr(result));
        JSONObject object = JSONUtil.parseObj(result);
        if ("200".equals(object.getStr("code"))) {
            JSONObject dataObject = JSONUtil.parseObj(object.getStr("data"));
            List<JSONObject> list = JSONUtil.toList(JSONUtil.parseArray(dataObject.getStr("pageData")), JSONObject.class);
            Iterator<JSONObject> iterator = list.iterator();
            while (iterator.hasNext()) {
                JSONObject park = iterator.next();
                String parkingLot = park.getStr("parkingLot");
                if ("园区地下车库".equals(parkingLot) || "车辆段地上停车场".equals(parkingLot) || "集团西门".equals(parkingLot)) {
                    iterator.remove();
                }
            }
            query.setRecords(list);
            query.setTotal(list.size());
        }
        return query;
    }

    /**
     * @Description: 请求大华post接口
     * @Param:
     * @Return: String
     * @Author: zhaohaoran
     * @Date: 2023-10-24 10:30:03
     */
    @Override
    public JSONObject httpPostDh(Map<String, Object> param, String url) throws Exception {
        log.info("请求大华接口入参=====================================>");
        log.info("请求大华接口入参++++++++++:{}", param);
        String token = getToken();
        // 创建一个信任所有证书的SSLContext
        SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, (certificate, authType) -> true).build();
        // 创建一个不验证主机名的HostnameVerifier
        HostnameVerifier hostnameVerifier = NoopHostnameVerifier.INSTANCE;
        // 使用上面创建的SSLContext和HostnameVerifier来创建一个SSLConnectionSocketFactory
        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext, hostnameVerifier);
        // 连接超时时间，单位毫秒;读取超时时间，单位毫秒
        RequestConfig config = RequestConfig.custom().setConnectTimeout(30000).setSocketTimeout(30000).build();
        CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();
        HttpPost httpPost = new HttpPost(http + url);
        httpPost.setConfig(config);
        // 设置请求头
        httpPost.setHeader("Authorization", "bearer " + token);
        httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");
        // 设置请求体
        StringEntity entity = new StringEntity(JSONUtil.toJsonStr(param), "UTF-8");
        httpPost.setEntity(entity);
        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            HttpEntity responseEntity = response.getEntity();
            if (responseEntity != null) {
                String result = EntityUtils.toString(responseEntity, "UTF-8");
                log.info("大华接口返回--------------------------------:{}", result);
                // 保证实体内容被完全消费并关闭流
                EntityUtils.consume(responseEntity);
                return JSONUtil.parseObj(result);
            } else {
                log.info("大华接口报错了！！Response entity is null!");
                throw new IOException("Response entity is null");
            }
        }
    }

    @Override
    public List<RegionsAndCamerasDto> regionsAndCamerasList() {
        List<RegionsAndCamerasDto> list = dahuaCamerasMapper.regionsAndCamerasList();
        return this.buildTree(list);
    }

    @Override
    public R ssoLogin() throws Exception {
        //先查询redis内是否存储大华token，否则请求大华接口获取token
        String redisToken = redisTemplate.opsForValue().get("dh_access_token");
        String refreshToken = redisTemplate.opsForValue().get("dh_refresh_token");
        if (StrUtil.isEmpty(redisToken) || StrUtil.isEmpty(refreshToken)) {
            redisToken = HttpUtils.getToken(redisTemplate, http, userName, password, clientId, clientSecret);
            refreshToken = redisTemplate.opsForValue().get("dh_refresh_token");
        }
        String fullEncoded = URLEncoder.encode(redirectUrl, StandardCharsets.UTF_8.name());

        StringBuilder url = new StringBuilder(http);
        url.append("?access_token=").append(redisToken).append("&refresh_token=").append(refreshToken).append("&redirect=").append(fullEncoded).append("#/thirdAccess");
        return R.success(url);
    }

    @Override
    public R cronRegionConfigList(Map<String, Object> param) throws Exception {
        List<DahuaPfsRegion> list = new ArrayList<>();
        List<DahuaPfsRegionChannel> channelList = new ArrayList<>();
        String token = getToken();
        int pageNo = 1;
        int pageSize = 100;
        int totalPage = 0;
        do {
            param.put("pageNum", pageNo);
            param.put("pageSize", pageSize);
            String url = http + "/evo-apigw/evo-passengerflow/1.0.0/statistics/regionConfig/page";
            //log.info("token:{}", token);
            String result = HttpRequest.post(url).header("Authorization", tokenKey + " " + token).body(JSONUtil.toJsonStr(param)).execute().body();
            log.info("分页查询区域配置列表接口{}:{}", url, JSONUtil.toJsonStr(result));
            JSONObject object = JSONUtil.parseObj(result);
            if ("200".equals(object.getStr("code"))) {
                JSONObject dataObject = JSONUtil.parseObj(object.getStr("data"));
                totalPage = dataObject.getInt("totalPage");
                List<JSONObject> regionList = JSONUtil.toList(JSONUtil.parseArray(dataObject.getStr("pageData")), JSONObject.class);
                if (CollectionUtil.isNotEmpty(regionList)) {
                    for (JSONObject regionObject : regionList) {
                        regionObject.remove("createTime");
                        regionObject.remove("updateTime");
                        DahuaPfsRegion dahuaPfsRegion = JSONUtil.toBean(regionObject, DahuaPfsRegion.class);
                        list.add(dahuaPfsRegion);
                        cronRegionChannelList(channelList, dahuaPfsRegion.getId() + "");
                    }
                }
            }
        } while (pageNo++ < totalPage);

        insertDahuaPfsRegion(list);
        insertDahuaPfsRegionChannel(channelList);
        return R.success();
    }

    @Override
    public Page camerasPage(Query query) {
        DahuaCameras dahuaCameras = BeanUtil.toBean(query.getCondition(), DahuaCameras.class, CopyOptions.create());
        query.setRecords(dahuaCamerasMapper.camerasPage(query, dahuaCameras));
        return query;
    }

    private void cronRegionChannelList(List<DahuaPfsRegionChannel> channelList, String regionCode) {
        try {
            int index = channelList.size() + 1;
            String token = getToken();
            //Map<String, Object> param = new HashMap<>();
            //param.put("regionCode", regionCode);
            String url = http + "/evo-apigw/evo-passengerflow/1.0.0/region/" + regionCode;
            //log.info("token:{}", token);
            String result = HttpRequest.get(url).header("Authorization", tokenKey + " " + token).execute().body();
            log.info("根据区域查询通道列表接口{}:{}", url, JSONUtil.toJsonStr(result));
            JSONObject object = JSONUtil.parseObj(result);
            if ("200".equals(object.getStr("code"))) {
                JSONObject dataJson = JSONUtil.parseObj(object.getStr("data"));
                List<JSONObject> regionList = JSONUtil.toList(JSONUtil.parseArray(dataJson.getStr("regionChannelBinds")), JSONObject.class);
                if (CollectionUtil.isNotEmpty(regionList)) {
                    for (JSONObject regionObject : regionList) {
                        DahuaPfsRegionChannel regionChannel = new DahuaPfsRegionChannel();
                        regionChannel.setId((long) index);
                        regionChannel.setRegionCode(regionObject.getStr("regionCode"));
                        regionChannel.setChannelCode(regionObject.getStr("channelCode"));
                        channelList.add(regionChannel);
                        index++;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public R cronDeviceTree(Map<String, Object> param) {
        List<DahuaCameras> camerasList = new ArrayList<>();
        List<DahuaOrganization> orgList = new ArrayList<>();
        this.getDeviceTree("L02", null, orgList, camerasList);
        //批量插入大华组织机构数据
        if (CollUtil.isNotEmpty(orgList)) {
            this.insertDahuaOrganization(orgList);
        }
        //批量插入大华摄像头数据
        if (CollUtil.isNotEmpty(camerasList)) {
            this.updateDahuaCamerasList(camerasList);
        }
        return R.success();
    }

    public void getDeviceTree(String type, String id, List<DahuaOrganization> orgList, List<DahuaCameras> camerasList) {
        try {
            Map<String, Object> param = new HashMap<>();
            String token = getToken();
            param.put("type", type+";;1");
            if(StrUtil.isNotEmpty(id)){
                param.put("id", id);
            }
            param.put("menu", 0);
            param.put("checkStat", 1);
            param.put("checkNodes", new ArrayList<>());
            String url = http + "/evo-apigw/admin/API/org_dev/tree";
            String result = HttpRequest.post(url).header("Authorization", tokenKey + " " + token).body(JSONUtil.toJsonStr(param)).execute().body();
            log.info("获取大华设备列表接口{}:{}", url, JSONUtil.toJsonStr(result));
            JSONObject object = JSONUtil.parseObj(result);
            if ("1000".equals(object.getStr("code"))) {
                JSONObject dataObject = JSONUtil.parseObj(object.getStr("data"));
                List<JSONObject> deviceList = JSONUtil.toList(JSONUtil.parseArray(dataObject.getStr("value")), JSONObject.class);
                if (CollectionUtil.isNotEmpty(deviceList)) {
                    for (JSONObject deviceObject : deviceList) {
                        if (StrUtil.isBlank(deviceObject.getStr("channelCode"))) {
                            DahuaOrganization organization = new DahuaOrganization();
                            organization.setOrgCode(deviceObject.getStr("id"));
                            organization.setOrgName(deviceObject.getStr("name"));
                            organization.setOrgSn(deviceObject.getStr("id"));
                            organization.setOrgType(deviceObject.getStr("type"));
                            organization.setParentCode(deviceObject.getStr("pId"));
                            organization.setService(deviceObject.getStr("type"));
                            organization.setSort(deviceObject.getInt("sort"));
                            orgList.add(organization);
                            if (!type.equals(organization.getOrgCode())) {
                                this.getDeviceTree(type, organization.getOrgCode(), orgList, camerasList);
                            }
                        } else {
                            DahuaCameras device = new DahuaCameras();
                            device.setDeviceCode(deviceObject.getStr("deviceCode"));
                            device.setOwnerCode(deviceObject.getStr("ownerCode"));
                            device.setChannelCode(deviceObject.getStr("channelCode"));
                            device.setChannelName(deviceObject.getStr("name"));
                            device.setCameraType(deviceObject.getStr("cameraType"));
                            device.setChannelState(deviceObject.getInt("isOnline"));
                            device.setTreeSort(deviceObject.getLong("sort"));
                            camerasList.add(device);
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 批量插入大华客流量统计区域配置列表
     * 方法会先清空表数据，然后分批次插入新数据，每批最多插入50条记录
     *
     * @param list 需要大华客流量统计区域配置列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertDahuaPfsRegion(List<DahuaPfsRegion> list) {
        // 清空现有数据（注意：该操作不可逆）
        dahuaPfsRegionMapper.truncateDahuaPfsRegion();

        // 分批次插入数据（每批50条，避免单次SQL数据量过大）
        int step = 50;
        for (int i = 0; i < list.size(); i += step) {
            // 计算当前批次的结束索引（防止越界）
            int endIndex = (i + step) < list.size() ? (i + step) : list.size();
            dahuaPfsRegionMapper.insertDahuaPfsRegionList(list.subList(i, endIndex));
        }
    }

    /**
     * 批量插入大华组织机构数据
     * 方法会先清空表数据，然后分批次插入新数据，每批最多插入50条记录
     *
     * @param list 需要插入的组织机构数据列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertDahuaPfsRegionChannel(List<DahuaPfsRegionChannel> list) {
        // 清空现有数据（注意：该操作不可逆）
        dahuaPfsRegionChannelMapper.truncateDahuaPfsRegionChannel();
        log.info("insertDahuaPfsRegionChannel:{}", JSONUtil.toJsonStr(list));
        // 分批次插入数据（每批50条，避免单次SQL数据量过大）
        int step = 50;
        for (int i = 0; i < list.size(); i += step) {
            // 计算当前批次的结束索引（防止越界）
            int endIndex = (i + step) < list.size() ? (i + step) : list.size();
            dahuaPfsRegionChannelMapper.insertDahuaPfsRegionChannelList(list.subList(i, endIndex));
        }
    }

    @Override
    public List<Map<String, Object>> passengerDistribution() {
        List<Map<String, Object>> list = new ArrayList<>();
        try {
            String token = getToken();
            List<Long> regionIds = new ArrayList<>();
            List<Integer> regionTypes = new ArrayList<>();
            DahuaPfsRegion dahuaPfsRegion = new DahuaPfsRegion();
            List<DahuaPfsRegion> regionInfoList = dahuaPfsRegionMapper.selectDahuaPfsRegionList(dahuaPfsRegion);
            for (DahuaPfsRegion region : regionInfoList) {
                regionIds.add(region.getId());
                regionTypes.add(region.getRegionType());
            }
            regionTypes = CollUtil.distinct(regionTypes);
            for (Integer regionType : regionTypes) {
                int pageNo = 1;
                int pageSize = 1000;
                int totalPage = 0;
                do {
                    String dateStr = DateUtil.formatDate(new Date());
                    Map<String, Object> param = new HashMap<>();
                    param.put("pageNum", pageNo);
                    param.put("pageSize", pageSize);
                    param.put("regionType", regionType);
                    param.put("regionIds", regionIds);
                    param.put("reportTime", dateStr + " 23:59:59");
                    param.put("reportBeginTime", dateStr + " 00:00:00");
                    param.put("reportType", 1);
                    param.put("reportModel", 1);
                    String url = http + "/evo-apigw/evo-passengerflow/1.0.0/region/report/distribution";
                    //log.info("token:{}", token);
                    log.info("分页查询区域配置列表接口{}:{}", url, JSONUtil.toJsonStr(param));
                    String result = HttpRequest.post(url).header("Authorization", tokenKey + " " + token).body(JSONUtil.toJsonStr(param)).execute().body();
                    log.info("分页查询区域配置列表接口{}:{}", url, JSONUtil.toJsonStr(result));
                    JSONObject object = JSONUtil.parseObj(result);
                    if ("200".equals(object.getStr("code"))) {
                        JSONObject dataObject = JSONUtil.parseObj(object.getStr("data"));
                        totalPage = dataObject.getInt("totalPage");
                        List<JSONObject> regionList = JSONUtil.toList(JSONUtil.parseArray(dataObject.getStr("pageData")), JSONObject.class);
                        if (CollectionUtil.isNotEmpty(regionList)) {
                            for (JSONObject regionObject : regionList) {
                                Map<String, Object> map = new HashMap<>();
                                map.put("name", regionObject.getStr("regionName"));
                                long enterNumber = regionObject.getLong("enterNumber");
                                long outNumber = regionObject.getLong("outNumber");
                                long chaNum = enterNumber - outNumber;
                                if (chaNum > 0) {
                                    map.put("value", chaNum);
                                } else {
                                    map.put("value", 0);
                                }
                                list.add(map);
                            }
                        }
                    }
                } while (pageNo++ < totalPage);
            }


            for (DahuaPfsRegion region : regionInfoList) {
                boolean faly = true;
                for (Map<String, Object> map : list) {
                    if (region.getRegionName().equals(map.get("name").toString())) {
                        faly = false;
                        continue;
                    }
                }
                if (faly) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("name", region.getRegionName());
                    map.put("value", 0);
                    list.add(map);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }

    private List<RegionsAndCamerasDto> buildTree(List<RegionsAndCamerasDto> allItems) {
        // 创建一个根节点列表和一个映射，用于快速查找父节点
        List<RegionsAndCamerasDto> returnRoot = new ArrayList<>();
        List<RegionsAndCamerasDto> roots = new ArrayList<>();
        Map<String, RegionsAndCamerasDto> indexCodeToNodeMap = new HashMap<>();

        // 遍历所有项，填充映射表
        for (RegionsAndCamerasDto item : allItems) {
            indexCodeToNodeMap.put(item.getIndexCode(), item);
        }

        // 为每个项找到其父节点并添加到对应的子节点列表中
        for (RegionsAndCamerasDto item : allItems) {
            RegionsAndCamerasDto parentNode = indexCodeToNodeMap.get(item.getParentIndexCode());
            if (parentNode == null) {
                // 如果没有父节点，则认为是根节点
                roots.add(item);
            } else {
                if (parentNode.getChildren() == null) {
                    parentNode.setChildren(new ArrayList<>());
                }
                // 添加到子节点列表，并确保按sort排序
                parentNode.getChildren().add(item);
                parentNode.getChildren().sort(Comparator.comparingLong(RegionsAndCamerasDto::getSort));
            }
        }

        //统计在线状态
        for (RegionsAndCamerasDto space : roots) {
            getChildrenStatus(space);
        }
        return roots;
    }

    private static void getChildrenStatus(RegionsAndCamerasDto space) {
        Integer onNum = 0;
        Integer totalNum = 0;
        if (CollUtil.isNotEmpty(space.getChildren())) {
            List<RegionsAndCamerasDto> newChildren = new ArrayList<>();
            List<RegionsAndCamerasDto> children = space.getChildren();
            for (RegionsAndCamerasDto node : children) {
                if ("1".equals(node.getNodeType())) {
                    totalNum += 1;
                    if ("1".equals(node.getOnline())) {
                        onNum += 1;
                    }
                } else {
                    getChildrenStatus(node);
                    onNum += node.getOnlineNum();
                    totalNum += node.getTotalNum();
                }
                if (CollUtil.isNotEmpty(node.getChildren()) || "1".equals(node.getNodeType())) {
                    newChildren.add(node);
                }
            }
            space.setChildren(newChildren);
        }
        space.setOnlineNum(onNum);
        space.setTotalNum(totalNum);
    }
}
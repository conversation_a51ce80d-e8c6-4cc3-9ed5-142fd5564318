package com.huazheng.tunny.smarkpark.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.smarkpark.api.entity.EspDoorDevice;
import com.huazheng.tunny.smarkpark.api.entity.ParkingBarrierGate;
import com.huazheng.tunny.smarkpark.service.EspDoorDeviceService;
import com.huazheng.tunny.smarkpark.service.ParkingBarrierGateService;
import com.huazheng.tunny.smarkpark.service.ParkingSourceService;
import com.huazheng.tunny.smarkpark.util.KeytopUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 门禁设备管理
 *
 * <AUTHOR> code generator
 * @date 2025-05-07 11:43:47
 */
@Slf4j
@RestController
@RequestMapping("/espDoorDevice")
public class EspDoorDeviceController {

    @Resource
    private EspDoorDeviceService espDoorDeviceService;
    @Resource
    private ParkingSourceService parkingSourceService;
    @Resource
    private ParkingBarrierGateService parkingBarrierGateService;
    @Resource
    private KeytopUtil keytopUtil;

    /**
     * 手动同步门禁设备列表
     */
    @GetMapping("/syncDoorDevicePage")
    public R syncDoorOutIntoInfo() {
        log.info("手动手动同步门禁设备列表开始 " + DateUtil.now());
        //设置请求参数
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("current", 1);
        paramMap.put("size", 100000);

        keytopUtil.syncDoorDevicePage(paramMap, keytopUtil.getToken(true));
        log.info("手动手动同步门禁设备列表成功 " + DateUtil.now());
        log.info("同步停车场通道信息开始 " + DateUtil.now());
        try {
            List<ParkingBarrierGate> list = parkingSourceService.cronParkingBarrierGate(new HashMap<>());
            if (CollUtil.isNotEmpty(list)) {
                parkingBarrierGateService.cronChannelsList(list);
            }
            parkingSourceService.selectParkingSpacesNum();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        log.info("同步停车场通道信息成功 " + DateUtil.now());
        return new R<>(Boolean.TRUE);
    }

    /**
     * 列表
     *
     * @param params
     * @return
     */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //对象模糊查询
        return espDoorDeviceService.selectEspDoorDeviceListByLike(new Query<>(params));
    }

    /**
     * 信息
     *
     * @param id
     * @return R
     */
    @GetMapping("/{id}")
    public R info(@PathVariable("id") String id) {
        EspDoorDevice espDoorDevice = espDoorDeviceService.selectById(id);
        return new R<>(espDoorDevice);
    }

    /**
     * 保存
     *
     * @param espDoorDevice
     * @return R
     */
    @PostMapping
    public R save(@RequestBody EspDoorDevice espDoorDevice) {
        espDoorDeviceService.insert(espDoorDevice);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     *
     * @param espDoorDevice
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody EspDoorDevice espDoorDevice) {
        espDoorDeviceService.updateById(espDoorDevice);
        return new R<>(Boolean.TRUE);
    }


    /**
     * 删除
     *
     * @param id
     * @return R
     */
    @GetMapping("/del/{id}")
    public R delete(@PathVariable String id) {
        espDoorDeviceService.deleteById(id);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody List<String> ids) {
        espDoorDeviceService.deleteBatchIds(ids);
        return new R<>(Boolean.TRUE);
    }

}

package com.huazheng.tunny.smarkpark.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.smarkpark.api.entity.SkBuildControlDevice;
import com.huazheng.tunny.smarkpark.api.entity.SkBuildControlDeviceAttribute;
import com.huazheng.tunny.smarkpark.mapper.*;
import com.huazheng.tunny.smarkpark.api.entity.SkCockpitMapPoints;
import com.huazheng.tunny.smarkpark.service.SkBuildControlDeviceService;
import com.huazheng.tunny.smarkpark.service.SkCockpitMapPointsService;
import com.huazheng.tunny.smarkpark.util.ParkingUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service("skCockpitMapPointsService")
public class SkCockpitMapPointsServiceImpl extends ServiceImpl<SkCockpitMapPointsMapper, SkCockpitMapPoints> implements SkCockpitMapPointsService {

    @Value("${parkEnvironment:SW01-TH}")
    private String parkEnvironment;

    @Autowired
    private DahuaCamerasMapper dahuaCamerasMapper;
    @Autowired
    private EspDoorDeviceMapper espDoorDeviceMapper;
    @Autowired
    private SkCockpitMapPointsMapper skCockpitMapPointsMapper;
    @Autowired
    private SkBuildControlDeviceMapper skBuildControlDeviceMapper;
    @Autowired
    private SkBuildControlDeviceAttributeMapper skBuildControlDeviceAttributeMapper;
    @Autowired
    private ParkingBarrierGateMapper parkingBarrierGateMapper;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private SkBuildControlDeviceService skBuildControlDeviceService;

    /**
     * 查询驾驶舱地图监控点位信息
     *
     * @param indexCode 驾驶舱地图监控点位ID
     * @return 驾驶舱地图监控点位信息
     */
    @Override
    public SkCockpitMapPoints selectSkCockpitMapPointsById(String indexCode) {
        return skCockpitMapPointsMapper.selectSkCockpitMapPointsById(indexCode);
    }

    /**
     * 查询驾驶舱地图监控点位列表
     *
     * @param skCockpitMapPoints 驾驶舱地图监控点位信息
     * @return 驾驶舱地图监控点位集合
     */
    @Override
    public List<SkCockpitMapPoints> selectSkCockpitMapPointsList(SkCockpitMapPoints skCockpitMapPoints) {
        return skCockpitMapPointsMapper.selectSkCockpitMapPointsList(skCockpitMapPoints);
    }


    /**
     * 分页模糊查询驾驶舱地图监控点位列表
     *
     * @return 驾驶舱地图监控点位集合
     */
    @Override
    public Page selectSkCockpitMapPointsListByLike(Query query) {
        SkCockpitMapPoints skCockpitMapPoints = BeanUtil.toBean(query.getCondition(), SkCockpitMapPoints.class, CopyOptions.create());
        query.setRecords(skCockpitMapPointsMapper.selectSkCockpitMapPointsListByLike(query, skCockpitMapPoints));
        return query;
    }

    /**
     * 新增驾驶舱地图监控点位
     *
     * @param skCockpitMapPoints 驾驶舱地图监控点位信息
     * @return 结果
     */
    @Override
    public int insertSkCockpitMapPoints(SkCockpitMapPoints skCockpitMapPoints) {
        return skCockpitMapPointsMapper.insertSkCockpitMapPoints(skCockpitMapPoints);
    }

    /**
     * 修改驾驶舱地图监控点位
     *
     * @param skCockpitMapPoints 驾驶舱地图监控点位信息
     * @return 结果
     */
    @Override
    public int updateSkCockpitMapPoints(SkCockpitMapPoints skCockpitMapPoints) {
        return skCockpitMapPointsMapper.updateSkCockpitMapPoints(skCockpitMapPoints);
    }


    /**
     * 删除驾驶舱地图监控点位
     *
     * @param indexCode 驾驶舱地图监控点位ID
     * @return 结果
     */
    @Override
    public int deleteSkCockpitMapPointsById(String indexCode) {
        return skCockpitMapPointsMapper.deleteSkCockpitMapPointsById(indexCode);
    }


    /**
     * 批量删除驾驶舱地图监控点位对象
     *
     * @param indexCodes 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteSkCockpitMapPointsByIds(Integer[] indexCodes) {
        return skCockpitMapPointsMapper.deleteSkCockpitMapPointsByIds(indexCodes);
    }

    @Override
    public Map<String, Object> camerasStatistics() {
        return skCockpitMapPointsMapper.camerasStatistics();
    }

    @Override
    public List<Map<String, Object>> camerasOnlineRate() {
        return skCockpitMapPointsMapper.camerasOnlineRate();
    }

    @Override
    public Map<String, Object> fireFightEquipmentStatistics() {
        return skCockpitMapPointsMapper.fireFightEquipmentStatistics();
    }

    @Override
    public List<Map<String, Object>> fireFightEquipmentOnlineRate() {
        return skCockpitMapPointsMapper.fireFightEquipmentOnlineRate();
    }

    /**
     * 监控数量统计
     *
     * @return Map<String, Object>
     */
    @Override
    public Map<String, Object> statisticsCamerasNum() {
        Map<String, Object> returnMap = new HashMap<>();
        Map<String, Object> map = dahuaCamerasMapper.statisticsCamerasNum();
        int onlineNum = 0;
        int offlineNum = 0;
        int totalNum = 0;
        if (map != null) {
            if (map.get("onlineNum") != null) {
                onlineNum = Integer.parseInt(map.get("onlineNum").toString());
            }
            if (map.get("offlineNum") != null) {
                offlineNum = Integer.parseInt(map.get("offlineNum").toString());
            }
            totalNum = onlineNum + offlineNum;
        }
        returnMap.put("onlineCount", onlineNum);
        returnMap.put("offlineCount", offlineNum);
        returnMap.put("total", totalNum);
        return returnMap;
    }

    /**
     * 设备在线率统计
     *
     * @return Map<String, Object>
     */
    @Override
    public Map<String, Object> statisticsDeviceOnlineRate() {
        Map<String, Object> returnMap = new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();
        Map<String, Object> map = dahuaCamerasMapper.statisticsCamerasNum();
        chengeOnlineRate(returnMap, list, map);
        return returnMap;
    }

    @Override
    public List<Map<String, Object>> trafficEquipmentSituation() {
        List<Map<String, Object>> list = new ArrayList<>();
        // 闸机在线状态
        Map<String, Object> zhajiMap = this.getTrafficEquipmentMap("闸机在线状态", "u154.png", "1");
        list.add(zhajiMap);
        // 车道闸在线状态
        Map<String, Object> cheMap = this.getTrafficEquipmentMap("车道闸在线状态", "u163.png", "2");
        list.add(cheMap);
        // 门禁在线状态
        Map<String, Object> menjinMap = this.getTrafficEquipmentMap("门禁在线状态", "u177.png", "3");
        list.add(menjinMap);
        // 客流设备在线状态
        Map<String, Object> keliuMap = this.getTrafficEquipmentMap("客流设备在线状态", "u178.png", "4");
        list.add(keliuMap);
        return list;
    }

    @Override
    public List<Map<String, Object>> vehicleStatistics() {
        List<Map<String, Object>> list = new ArrayList<>();
        String parkingSpacesTotal = redisTemplate.opsForValue().get("parkingSpacesTotal");
        String parkingSpacesInto = redisTemplate.opsForValue().get("parkingSpacesInto");
        int totalNum = 0;
        int intoNum = 0;

        if (StrUtil.isNotBlank(parkingSpacesTotal)) {
            totalNum = Integer.parseInt(parkingSpacesTotal);
        }
        if (StrUtil.isNotBlank(parkingSpacesInto)) {
            intoNum = Integer.parseInt(parkingSpacesInto);
        }
        int outNum = totalNum - intoNum;
        if (outNum < 0) {
            outNum = 0;
        }
        list.add(setVehicleStatisticsMap("总车位", totalNum));
        list.add(setVehicleStatisticsMap("已用车位", intoNum));
        list.add(setVehicleStatisticsMap("剩余车位", outNum));
        return list;
    }

    private Map<String, Object> setVehicleStatisticsMap(String name, Integer num) {
        Map<String, Object> map = new HashMap<>();
        map.put("label", name);
        map.put("value", num);
        return map;
    }

    private Map<String, Object> getTrafficEquipmentMap(String title, String imgName, String type) {
        Map<String, Object> map = new HashMap<>();
        map.put("type", type);
        map.put("title", title);
        map.put("imgName", imgName);
        List<Map<String, Object>> list = new ArrayList<>();
        int onlineNum = 0;
        int offlineNum = 0;
        Map<String, Object> dataMap = new HashMap<>();
        if ("2".equals(type)) {
            dataMap = parkingBarrierGateMapper.statisticsDeviceNum();
        } else if ("3".equals(type)) {
            dataMap = espDoorDeviceMapper.statisticsDeviceNum();
        } else if ("4".equals(type)) {
            dataMap = dahuaCamerasMapper.statisticsKeLiuDeviceNum();
        }
        if (dataMap == null) {
            dataMap = new HashMap<>();
        }
        Map<String, Object> map1 = new HashMap<>();
        if (dataMap.get("onlineNum") != null) {
            onlineNum = Integer.parseInt(dataMap.get("onlineNum").toString());
        }
        map1.put("label", "在线");
        map1.put("value", onlineNum);
        list.add(map1);
        Map<String, Object> map2 = new HashMap<>();
        if (dataMap.get("offlineNum") != null) {
            offlineNum = Integer.parseInt(dataMap.get("offlineNum").toString());
        }
        map2.put("label", "离线");
        map2.put("value", offlineNum);
        list.add(map2);
        map.put("data", list);
        return map;
    }

    @Override
    public R trafficEquipmentInfo(Query query) {
        Map<String, Object> map = query.getCondition();
        if (map == null || map.get("type") == null) {
            return R.error("请输入需要查询的设备类别");
        }
        List<Map<String, Object>> list = new ArrayList<>();
        if ("2".equals(map.get("type").toString())) {
            list = parkingBarrierGateMapper.statisticsBarrierGateList(query, map);
        }
        if ("3".equals(map.get("type").toString())) {
            list = espDoorDeviceMapper.statisticsDeviceList(query, map);
        }
        if ("4".equals(map.get("type").toString())) {
            list = dahuaCamerasMapper.statisticsKeLiuDeviceList(query, map);
        }
        query.setRecords(list);
        return R.success(query);
    }

    @Override
    public List<Map<String, Object>> buildingControlDeviceByType() {
        List<Map<String, Object>> list = new ArrayList<>();
        List<Map<String, Object>> dataList = skBuildControlDeviceMapper.buildingControlDeviceByType();
        list.add(getDateMap("1", "能源", dataList));
        list.add(getDateMap("2", "暖通空调", dataList));
        list.add(getDateMap("3", "给排水", dataList));
        list.add(getDateMap("4", "环境设备", dataList));
        list.add(getDateMap("5", "照明", dataList));
        return list;
    }

    private Map<String, Object> getDateMap(String type, String title, List<Map<String, Object>> dataList) {
        Map<String, Object> map = new HashMap<>();
        map.put("type", type);
        map.put("name", title);
        int total = 0;
        for (Map<String, Object> dataMap : dataList) {
            String dataType = dataMap.get("type").toString();
            if (type.equals(dataType)) {
                total = Integer.parseInt(dataMap.get("total").toString());
                continue;
            }
        }
        map.put("value", total);
        return map;
    }

    @Override
    public Map<String, Object> buildingControlDeviceState() {
        Map<String, Object> returnMap = new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();
        List<Map<String, Object>> mapList = skBuildControlDeviceMapper.buildingControlDeviceState();
        int totalNum = 0;
        if (CollUtil.isNotEmpty(mapList)) {
            totalNum = setDeviceStateMap("1", "正常", totalNum, list, mapList);
            totalNum = setDeviceStateMap("2", "停止", totalNum, list, mapList);
            totalNum = setDeviceStateMap("3", "故障", totalNum, list, mapList);
        }
        returnMap.put("list", list);
        returnMap.put("total", totalNum);
        return returnMap;
    }

    private int setDeviceStateMap(String type, String title, int totalNum, List<Map<String, Object>> list, List<Map<String, Object>> mapList) {
        Map<String, Object> map = new HashMap<>();
        map.put("runState", type);
        map.put("name", title);
        int total = 0;
        for (Map<String, Object> dataMap : mapList) {
            String runState = dataMap.get("runState").toString();
            if (runState.equals(type)) {
                total = Integer.parseInt(dataMap.get("total").toString());
                totalNum = totalNum + total;
                continue;
            }
        }
        map.put("value", total);
        list.add(map);
        return totalNum;
    }

    @Override
    public R parkEnvironment() {
        List<Map<String, Object>> returnMap = new ArrayList<>();
        SkBuildControlDevice device = skBuildControlDeviceService.selectSkBuildControlDeviceByCode(parkEnvironment);
        returnMap.add(setParkEnvironmentMap("temperature", "温度", "℃", device.getAttributeList()));
        returnMap.add(setParkEnvironmentMap("humidity", "湿度", "%", device.getAttributeList()));
        returnMap.add(setParkEnvironmentMap("PM2.5", "PM2.5", "μg/m3", device.getAttributeList()));
        returnMap.add(setParkEnvironmentMap("CO2", "二氧化碳浓度", "ppm", device.getAttributeList()));
        return R.success(returnMap);
    }

    private Map<String, Object> setParkEnvironmentMap(String type, String title, String unitName, List<SkBuildControlDeviceAttribute> list) {
        Map<String, Object> map = new HashMap<>();
        map.put("type", type);
        map.put("title", title);
        map.put("unitName", unitName);
        double total = 0;
        if (CollUtil.isNotEmpty(list)) {
            for (SkBuildControlDeviceAttribute attribute : list) {
                if (attribute.getAttributeName().indexOf(title) >= 0) {
                    total = Double.parseDouble(attribute.getAttributeVal());
                    continue;
                }
            }
        }
        map.put("value", total);
        return map;
    }

    @Override
    public R energyTrends(Map<String, Object> params) {
        if (params == null || params.get("type") == null) {
            return R.error("请输入能源类别");
        }
        int type = Integer.parseInt(params.get("type").toString());
        Map<String, Object> map = new HashMap<>();
        map.put("title", new String[]{"1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"});
        map.put("lastYear", new double[]{0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00});
        map.put("thisYear", new double[]{0.00, 0.00, 100.00 + type, 255.00 + type, 300.00 + type, 340.00 + type, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00});
        return R.success(map);
    }


    public void chengeOnlineRate(Map<String, Object> returnMap, List<Map<String, Object>> list, Map<String, Object> map) {
        int onlineNum = 0;
        int offlineNum = 0;
        int totalNum = 0;
        if (map != null) {
            Map<String, Object> map1 = new HashMap<>();
            if (map.get("onlineNum") != null) {
                onlineNum = Integer.parseInt(map.get("onlineNum").toString());
            }
            map1.put("name", "在线");
            map1.put("value", onlineNum);
            list.add(map1);
            Map<String, Object> map2 = new HashMap<>();
            if (map.get("offlineNum") != null) {
                offlineNum = Integer.parseInt(map.get("offlineNum").toString());
            }
            map2.put("name", "离线");
            map2.put("value", offlineNum);
            list.add(map2);
            totalNum = onlineNum + offlineNum;
        }
        // 计算百分比（保留两位小数）
        double onlineRate = 0;
        if (totalNum > 0) {
            onlineRate = NumberUtil.mul(NumberUtil.div(onlineNum, totalNum), 100);
            onlineRate = NumberUtil.round(onlineRate, 2).doubleValue();
        }
        returnMap.put("list", list);
        returnMap.put("ratio", onlineRate);
    }
}

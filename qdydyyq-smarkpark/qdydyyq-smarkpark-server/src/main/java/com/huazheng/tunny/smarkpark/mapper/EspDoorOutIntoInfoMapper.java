package com.huazheng.tunny.smarkpark.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.smarkpark.api.entity.EspDoorOutIntoInfo;

import java.util.List;

/**
 * 【科拓】门禁通行记录表  mapper层
 *
 * <AUTHOR> code generator
 * @date 2025-05-07 11:43:52
 */
public interface EspDoorOutIntoInfoMapper extends BaseMapper<EspDoorOutIntoInfo> {
    /**
     * 查询【科拓】门禁通行记录表信息
     *
     * @param id 【科拓】门禁通行记录表ID
     * @return 【科拓】门禁通行记录表信息
     */
    public EspDoorOutIntoInfo selectEspDoorOutIntoInfoById(String id);

    /**
     * 查询【科拓】门禁通行记录表列表
     *
     * @param espDoorOutIntoInfo 【科拓】门禁通行记录表信息
     * @return 【科拓】门禁通行记录表集合
     */
    public List<EspDoorOutIntoInfo> selectEspDoorOutIntoInfoList(EspDoorOutIntoInfo espDoorOutIntoInfo);

    /**
     * 模糊查询【科拓】门禁通行记录表列表
     *
     * @param espDoorOutIntoInfo 【科拓】门禁通行记录表信息
     * @return 【科拓】门禁通行记录表集合
     */
    public List<EspDoorOutIntoInfo> selectEspDoorOutIntoInfoListByLike(EspDoorOutIntoInfo espDoorOutIntoInfo);


    /**
     * 分页模糊查询【科拓】门禁通行记录表列表
     *
     * @param espDoorOutIntoInfo 【科拓】门禁通行记录表信息
     * @return 【科拓】门禁通行记录表集合
     */
    public List<EspDoorOutIntoInfo> selectEspDoorOutIntoInfoListByLike(Query query, EspDoorOutIntoInfo espDoorOutIntoInfo);


    /**
     * 新增【科拓】门禁通行记录表
     *
     * @param espDoorOutIntoInfo 【科拓】门禁通行记录表信息
     * @return 结果
     */
    public int insertEspDoorOutIntoInfo(EspDoorOutIntoInfo espDoorOutIntoInfo);

    /**
     * 修改【科拓】门禁通行记录表
     *
     * @param espDoorOutIntoInfo 【科拓】门禁通行记录表信息
     * @return 结果
     */
    public int updateEspDoorOutIntoInfo(EspDoorOutIntoInfo espDoorOutIntoInfo);

    /**
     * 删除【科拓】门禁通行记录表
     *
     * @param id 【科拓】门禁通行记录表ID
     * @return 结果
     */
    public int deleteEspDoorOutIntoInfoById(String id);

    /**
     * 批量删除【科拓】门禁通行记录表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteEspDoorOutIntoInfoByIds(Integer[] ids);


}

package com.huazheng.tunny.smarkpark.controller;

import com.huazheng.tunny.smarkpark.service.SkAlarmLogService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 定时任务控制器
 */
@RestController
@Slf4j
@Data
public class ScheduledJobController {

    @Resource
    private SkAlarmLogService skAlarmLogService;

    /**
     * 定时清理告警日志
     * 迁移30天前的数据，每7天一次，凌晨0点37分执行
     */
    @Scheduled(cron = "0 37 0 1/7 * ? ")
    public void cleanUpAlarmLog() {
        log.info("定时清理告警日志");
        Integer days = 30;
        // 迁移30天前的告警日志到备份表
        skAlarmLogService.transferDataToHisTable(days);
        // 清理30天前的告警日志
        skAlarmLogService.cleanUpAlarmLog(days);
    }

}

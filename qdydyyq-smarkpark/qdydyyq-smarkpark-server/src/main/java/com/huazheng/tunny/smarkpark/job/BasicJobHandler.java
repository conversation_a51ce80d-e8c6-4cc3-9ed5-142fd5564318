package com.huazheng.tunny.smarkpark.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * Created by Lenovo on 2020/4/27.
 * xxljob 执行器模板
 */
//@Component
@Slf4j
public class BasicJobHandler {
    /**
     * 简单任务示例（Bean模式）
     */
    @XxlJob("basicJobHandler")
    public ReturnT<String> basicJobHandler(String param) throws Exception {
        XxlJobLogger.log("TunnyBasic-JOB,Hello Job");
        log.info("定时任务开始执行，传入参数为：" + param);
        /**
         * 定时任务内部逻辑
         */
        return ReturnT.SUCCESS;
    }

    /**
     * 生命周期任务示例：任务初始化与销毁时，支持自定义相关逻辑；
     */
    @XxlJob(value = "basicJobHandlerWithLife", init = "init", destroy = "destroy")
    public ReturnT<String> basicJobHandlerWithLife(String param) throws Exception {
        XxlJobLogger.log("TunnyBasic-JOB,Hello Life Job");
        log.info("生命周期定时任务开始执行，传入参数为：" + param);
        return ReturnT.SUCCESS;
    }

    public void init() {
        log.info(" 生命周期任务init");
    }

    public void destroy() {
        log.info("生命周期任务destory");
    }


}

package com.huazheng.tunny.smarkpark.controller;

import com.huazheng.tunny.admin.api.entity.SysDict;
import com.huazheng.tunny.admin.api.entity.SysUser;
import com.huazheng.tunny.common.log.annotation.SysLog;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.smarkpark.api.entity.SkAlarmNotifyRule;
import com.huazheng.tunny.smarkpark.service.SkAlarmNotifyRuleService;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.core.listener.EasyExcelListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 安防联动管理
 *
 * <AUTHOR> code generator
 * @date 2024-07-16 18:18:10
 */
@Slf4j
@RestController
@RequestMapping("/skalarmnotifyrule")
public class SkAlarmNotifyRuleController {

    @Autowired
    private SkAlarmNotifyRuleService skAlarmNotifyRuleService;

    /**
     * 列表
     *
     * @param params
     * @return
     */
    @SysLog("安防联动管理列表")
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //对象模糊查询
        return skAlarmNotifyRuleService.selectSkAlarmNotifyRuleListByLike(new Query<>(params));
    }

    /**
     * 信息
     *
     * @param id
     * @return R
     */
    @SysLog("安防联动管理详情")
    @GetMapping("/{id}")
    public R info(@PathVariable("id") Integer id) {
        SkAlarmNotifyRule skAlarmNotifyRule = skAlarmNotifyRuleService.selectSkAlarmNotifyRuleById(id);
        return new R<>(skAlarmNotifyRule);
    }

    /**
     * 保存
     *
     * @param skAlarmNotifyRule
     * @return R
     */
    @PostMapping
    public R save(@RequestBody SkAlarmNotifyRule skAlarmNotifyRule) {
        skAlarmNotifyRule.setCreateTime(LocalDateTime.now());
        skAlarmNotifyRule.setCreateBy(SecurityUtils.getUserInfo().getUserName());
        skAlarmNotifyRuleService.insertSkAlarmNotifyRule(skAlarmNotifyRule);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     *
     * @param skAlarmNotifyRule
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody SkAlarmNotifyRule skAlarmNotifyRule) {
        skAlarmNotifyRule.setUpdateTime(LocalDateTime.now());
        skAlarmNotifyRule.setUpdateBy(SecurityUtils.getUserInfo().getUserName());
        skAlarmNotifyRuleService.updateSkAlarmNotifyRule(skAlarmNotifyRule);
        return new R<>(Boolean.TRUE);
    }


    /**
     * 删除
     *
     * @param id
     * @return R
     */
    @SysLog("删除安防联动管理")
    @GetMapping("/del/{id}")
    public R delete(@PathVariable Integer id) {
        skAlarmNotifyRuleService.deleteSkAlarmNotifyRuleById(id);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 获取告警事件类别列表
     * */
    @SysLog("获取告警事件类别列表")
    @GetMapping("/getAlarmEventTypeList")
    public R getAlarmEventTypeList(
            @RequestParam(value="alarmTypeCode") String alarmTypeCode
            , @RequestParam(value="eventTypeName", required = false) String eventTypeName
    ) {
        List<Map<String,Object>> list = skAlarmNotifyRuleService.getAlarmEventTypeList(alarmTypeCode, eventTypeName);
        return new R<>(list);
    }

    /**
     * 获取人员列表
     * */
    @SysLog("获取人员列表")
    @GetMapping("/getPersonList")
    public R getPersonList(@RequestParam(value="userRealname", required = false) String userRealname) {
        List<SysUser> list = skAlarmNotifyRuleService.getPersonList(userRealname);
        return new R<>(list);
    }
}

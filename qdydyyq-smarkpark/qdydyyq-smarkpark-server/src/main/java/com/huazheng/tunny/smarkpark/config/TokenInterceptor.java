package com.huazheng.tunny.smarkpark.config;

import com.huazheng.tunny.smarkpark.api.feign.RemoteAuthService;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.Serializable;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通过oauth2的client认证模式实现feign接口访问时自动附带token
 * （用于防止直接开放白名单接口造成的安全性问题）
 *
 * <AUTHOR>
 * @date 2024/4/25
 */
@Data
@Component
public class TokenInterceptor implements RequestInterceptor, Serializable {
    private static final long serialVersionUID = 1L;
    private final HttpServletRequest request;

    @Resource
    private RemoteAuthService remoteAuthService;

    @Autowired
    public TokenInterceptor(HttpServletRequest request) {
        this.request = request;
    }

    /**
     * ※※※ client_id、client_secret请修改成自己系统的
     *
     * 在admin服务的数据库中的sys_oauth_client_details表里查，没有的自己添加自己系统的，插入sql如下：
     * INSERT INTO sys_oauth_client_details (`client_id`, `resource_ids`, `client_secret`, `scope`, `authorized_grant_types`, `web_server_redirect_uri`, `authorities`, `access_token_validity`, `refresh_token_validity`, `additional_information`, `autoapprove`) VALUES (【系统标识，英文】, NULL, 【密钥，建议32位随机码】, 'server', 'client_credentials', NULL, NULL, NULL, NULL, 【备注信息，建议注明是用于哪个系统的】, 'false');
     * */
    private static final String CLIENT_ID = "???";
    private static final String CLIENT_SECRET = "???";
    private static final String GRANT_TYPE = "client_credentials";
    /**
    * ※※※ 填写需要自动获取token的feign接口，即原有需要访问的其他服务的白名单接口（非必要别乱加接口，这个token是拿不到登录用户信息的）
     */
    private static final List<String> IGNORE_API = Arrays.asList(
//            "/user/selectUserList"
//            ,"/syspost/postList"
//            ,"/role/roleListAll"
//            ,"/role/selectUserByRole"
//            ,"/outInterface/selectuserbyrolelistandprojectuuid"
//            ,"/outInterface/selectrolebyempno"
//            ,"/syspost/postList"
//            ,"/user/getUserByCode"
    );

    @Override
    public void apply(RequestTemplate template) {
        if (IGNORE_API.indexOf(template.url()) >= 0) {
            //获取 Token
            Map<String, String> parameters = new HashMap();
            parameters.put("client_id", CLIENT_ID);
            parameters.put("client_secret", CLIENT_SECRET);
            parameters.put("grant_type", GRANT_TYPE);
            OAuth2AccessToken tokenObj = remoteAuthService.getAccessToken(parameters);
            String token = String.valueOf(tokenObj.getValue());
            // 将 Token 添加到 Feign 请求头中
            template.header("Authorization", "Bearer " + token);
            // 将 Token 添加到 Feign 请求头中
            template.header("X-Access-Token", token);
        }
    }
}
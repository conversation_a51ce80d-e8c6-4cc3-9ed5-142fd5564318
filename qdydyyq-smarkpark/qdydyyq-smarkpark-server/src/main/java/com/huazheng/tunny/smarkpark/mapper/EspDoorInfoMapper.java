package com.huazheng.tunny.smarkpark.mapper;

import com.huazheng.tunny.smarkpark.api.entity.EspDoorInfo;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.huazheng.tunny.common.core.util.Query;
import java.util.List;
/**
 * 门禁明细表  mapper层
 *
 * <AUTHOR>
 * @date 2025-05-30 14:26:20
 */
public interface EspDoorInfoMapper extends BaseMapper<EspDoorInfo> {
    /**
     * 查询门禁明细表信息
     *
     * @param id 门禁明细表ID
     * @return 门禁明细表信息
     */
    public EspDoorInfo selectEspDoorInfoById(String id);

    /**
     * 查询门禁明细表列表
     *
     * @param espDoorInfo 门禁明细表信息
     * @return 门禁明细表集合
     */
    public List<EspDoorInfo> selectEspDoorInfoList(EspDoorInfo espDoorInfo);

    /**
     * 模糊查询门禁明细表列表
     *
     * @param espDoorInfo 门禁明细表信息
     * @return 门禁明细表集合
     */
    public List<EspDoorInfo> selectEspDoorInfoListByLike(EspDoorInfo espDoorInfo);


    /**
     * 分页模糊查询门禁明细表列表
     *
     * @param espDoorInfo 门禁明细表信息
     * @return 门禁明细表集合
     */
    public List<EspDoorInfo> selectEspDoorInfoListByLike(Query query, EspDoorInfo espDoorInfo);


    /**
     * 新增门禁明细表
     *
     * @param espDoorInfo 门禁明细表信息
     * @return 结果
     */
    public int insertEspDoorInfo(EspDoorInfo espDoorInfo);

    /**
     * 修改门禁明细表
     *
     * @param espDoorInfo 门禁明细表信息
     * @return 结果
     */
    public int updateEspDoorInfo(EspDoorInfo espDoorInfo);

    /**
     * 删除门禁明细表
     *
     * @param id 门禁明细表ID
     * @return 结果
     */
    public int deleteEspDoorInfoById(String id);

    /**
     * 批量删除门禁明细表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteEspDoorInfoByIds(Integer[] ids);


    void insertEspDoorInfoList(List<EspDoorInfo> espDoorInfos);

    void truncateEspDoorInfo();
}

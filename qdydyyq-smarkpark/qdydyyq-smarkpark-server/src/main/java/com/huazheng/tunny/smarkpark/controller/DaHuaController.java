package com.huazheng.tunny.smarkpark.controller;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.log.annotation.SysLog;
import com.huazheng.tunny.smarkpark.api.dto.DaHuaCamerasDTO;
import com.huazheng.tunny.smarkpark.api.dto.RegionsAndCamerasDto;
import com.huazheng.tunny.smarkpark.service.DaHuaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.plugins.Page;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * @Description: 大华接口
 * @Author: zhao<PERSON>ran
 * @Date: 2023-10-24 10:30:03
 */
@RestController
@RequestMapping("/dahua")
public class DaHuaController {

    @Autowired
    private DaHuaService daHuaService;

    /**
     * @Description: 获取大华token
     * @Param:
     * @Return: String
     * @Author: zhaohaoran
     * @Date: 2023-10-24 10:30:03
     */
    @GetMapping("/getToken")
    public String getToken() throws Exception {
        return daHuaService.getToken();
    }

    @PostMapping("/eventSubscribe")
    @ResponseBody
    public String eventSubscribe(HttpServletRequest req, HttpServletResponse resp) throws Exception {
        return daHuaService.eventSubscribe(req);
    }

    /**
     * @Description: 全量同步组织架构
     * @Param:
     * @Return: String
     * @Author: sj
     * @Date: 2023-10-24 10:30:03
     */
    @PostMapping("/cronOrganizationList")
    public R cronOrganizationList(@RequestBody Map<String, Object> param) throws Exception {
        return daHuaService.cronOrganizationList(param);
    }

    /**
     * @Description: 全量同步设备
     * @Param:
     * @Return: String
     * @Author: sj
     * @Date: 2023-10-24 10:30:03
     */
    @PostMapping("/cronDeviceList")
    public R cronDeviceList(@RequestBody Map<String, Object> param) throws Exception {
        return daHuaService.cronDeviceList(param);
    }
    /**
     * @Description: 全量同步组织架构
     * @Param:
     * @Return: String
     * @Author: sj
     * @Date: 2023-10-24 10:30:03
     */
    @PostMapping("/cronDeviceTree")
    public R cronDeviceTree(@RequestBody Map<String, Object> param) throws Exception {
        return daHuaService.cronDeviceTree(param);
    }
    /**
     * @Description: 全量同步客流量区域配置列表
     * @Param:
     * @Return: String
     * @Author: sj
     * @Date: 2023-10-24 10:30:03
     */
    @PostMapping("/cronRegionConfigList")
    public R cronRegionConfigList(@RequestBody Map<String, Object> param) throws Exception {
        return daHuaService.cronRegionConfigList(param);
    }

    /**
     * @Description: 查询大华事件列表
     * @Param:
     * @Return: String
     * @Author: sj
     * @Date: 2023-10-24 10:30:03
     */
    @PostMapping("/subscribeList")
    public R subscribeList(@RequestBody Map<String, Object> param) throws Exception {
        return daHuaService.subscribeList(param);
    }

    /**
     * @Description: 查询大华车场列表
     * @Param:
     * @Return: String
     * @Author: sj
     * @Date: 2023-10-24 10:30:03
     */
    @GetMapping("/queryParking")
    public R queryParking(@RequestParam Map<String, Object> param) throws Exception {
        return R.success(daHuaService.queryParking(new Query<>(param)));
    }

    @SysLog("大华ICC平台SSO单点登录")
    @GetMapping("/ssoLogin")
    public R ssoLogin() throws Exception{
        return daHuaService.ssoLogin();
    }

    /**
     * 区域&监控点树
     */
    @SysLog("区域&监控点树")
    @GetMapping("/videoDevice/tree")
    public R videoDeviceTree() {
        List<RegionsAndCamerasDto> list = daHuaService.regionsAndCamerasList();
        // 构建树
        return new R<>(list);
    }

    /**
     * 区域&监控点树
     */
    @GetMapping("/cameras/page")
    public R camerasPage(@RequestParam Map<String, Object> params) {
        Page<DaHuaCamerasDTO> page =daHuaService.camerasPage(new Query<>(params));
        return R.success(page);
    }

}

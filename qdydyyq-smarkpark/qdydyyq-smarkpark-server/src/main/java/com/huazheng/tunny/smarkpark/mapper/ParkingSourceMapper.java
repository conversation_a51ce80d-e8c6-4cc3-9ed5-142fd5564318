package com.huazheng.tunny.smarkpark.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.huazheng.tunny.smarkpark.api.entity.ParkingBarrierGate;
import com.huazheng.tunny.smarkpark.api.entity.ParkingVehicleRecord;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 停车场-进出场记录  mapper层
 *
 * <AUTHOR>
 * @date 2025-06-10 16:32:58
 */
@Component
public interface ParkingSourceMapper {

    List<ParkingBarrierGate> selectParkingBarrierGate();

    List<ParkingVehicleRecord> selectVehicleRecord(String startTme);

    int selectParkingSpacesNum();
}

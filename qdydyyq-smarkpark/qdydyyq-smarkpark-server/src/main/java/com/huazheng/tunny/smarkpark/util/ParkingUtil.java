package com.huazheng.tunny.smarkpark.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.security.MessageDigest;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Component
public class ParkingUtil implements Serializable {
    private static final long serialVersionUID = 1L;

    @Value("${parking.host:http://*************}")
    private String host;
    @Value("${parking.parkId:}")
    private String parkId;
    @Value("${parking.key:}")
    private String key;

    /**
     * 接口地址
     */
    public static String openApi = "/thirdServer/v1/openApi";
    /**
     * 服务标识
     */
    public static String queryChannels = "query_channels";
    public static String queryParkData = "query_parkData";

    /**
     * api调用逻辑
     */
    public String queryChannels() {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("service_name", queryChannels);
        paramMap.put("park_id", parkId);
        paramMap.put("reservedStr", "");
        paramMap.put("sign", sign(paramMap, key, "sign"));
        try {
            String str = HttpRequest.post(host + openApi).body(JSONUtil.toJsonStr(paramMap)).execute().body();
            return str;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * api调用逻辑
     */
    public String queryParkData() {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("service_name", queryParkData);
        paramMap.put("park_id", parkId);
        paramMap.put("reservedStr", "");
        paramMap.put("sign", sign(paramMap, key, "sign"));
        try {
            String str = HttpRequest.post(host + openApi).body(JSONUtil.toJsonStr(paramMap)).execute().body();
            if (StrUtil.isBlank(str)) {
                return null;
            }
            JSONObject jsonObject = JSONUtil.parseObj(str);
            if (!"1".equals(jsonObject.getStr("code")) || StrUtil.isBlank(jsonObject.getStr("data"))) {
                return null;
            }
            return jsonObject.getStr("data");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    private static final String CHARSET = "UTF-8";

    private static final String SIGN_TYPE = "MD5";

    /**
     * 判断签名是否正确，必须包含sign字段，否则返回false。
     *
     * @param data Map类型数据
     * @param key  API密钥
     * @return 签名是否正确
     * @throws Exception
     */
    public static boolean check(Map<String, String> data, String key, String signField) {
        if (!data.containsKey(signField)) {
            return false;
        }
        String sign = data.get(signField).toString();
        String newSign = sign(data, key, signField);

        System.out.println("newSign:" + newSign);

        return newSign.equals(sign);
    }


    public static String sign(final Map<String, String> data, String key, String signField) {
        Set<String> keySet = data.keySet();
        String[] keyArray = keySet.toArray(new String[keySet.size()]);
        Arrays.sort(keyArray);
        StringBuilder sb = new StringBuilder();
        for (String k : keyArray) {
            if (k.equals(signField)) {
                continue;
            }
            // 参数值为空，则不参与签名
            if (data.get(k) != null && data.get(k).trim().length() > 0) {
                sb.append(k).append("=").append(data.get(k).trim()).append("&");
            }
        }
        sb.append("key=").append(key);

        System.out.println("newSign:" + sb.toString());

        String result = MD5(sb.toString()).toUpperCase();

        return result;
    }


    public static String sign2(final Map<String, Object> data, String key, String signField) {
        Set<String> keySet = data.keySet();
        String[] keyArray = keySet.toArray(new String[keySet.size()]);
        Arrays.sort(keyArray);
        StringBuilder sb = new StringBuilder();
        for (String k : keyArray) {
            if (k.equals(signField)) {
                continue;
            }
            // 参数值为空，则不参与签名
            if (data.get(k) != null && data.get(k).toString().trim().length() > 0) {
                sb.append(k).append("=").append(data.get(k).toString().trim()).append("&");
            }
        }
        sb.append("key=").append(key);

        System.out.println("newSign:" + sb.toString());

        String result = MD5(sb.toString()).toUpperCase();

        return result;
    }

    /**
     * 生成 MD5
     *
     * @param data 待处理数据
     * @return MD5结果
     */
    public static String MD5(String data) {
        try {
            MessageDigest md = MessageDigest.getInstance(SIGN_TYPE);
            byte[] array = md.digest(data.getBytes(CHARSET));
            StringBuilder sb = new StringBuilder();
            for (byte item : array) {
                sb.append(Integer.toHexString((item & 0xFF) | 0x100).substring(1, 3));
            }
            return sb.toString().toUpperCase();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}

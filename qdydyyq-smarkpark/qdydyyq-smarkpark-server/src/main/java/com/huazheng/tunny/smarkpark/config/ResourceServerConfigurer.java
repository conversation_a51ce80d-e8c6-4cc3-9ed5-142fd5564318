package com.huazheng.tunny.smarkpark.config;

import com.huazheng.tunny.common.security.component.ResourceAuthExceptionEntryPoint;
import com.huazheng.tunny.common.security.component.TunnyAccessDeniedHandler;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configurers.ResourceServerSecurityConfigurer;

@Configuration
@EnableResourceServer
@AllArgsConstructor
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class ResourceServerConfigurer extends ResourceServerConfigurerAdapter {
    private final TunnyAccessDeniedHandler tunnyAccessDeniedHandler;
    private final ResourceAuthExceptionEntryPoint resourceAuthExceptionEntryPoint;

    @Override
    public void configure(HttpSecurity http) throws Exception {
        http.authorizeRequests()
                //关闭所有白名单，按需开放，对于开放的白名单接口要增加验签逻辑
                .antMatchers("/v2/api-docs",
                        "/platform/**",
                        "/index",
                        "/tokenTest",
                        "/test/errorlog",
                        "/test/infolog",
                        "/ssoIndex",
                        "/info",
                        "/index",
                        "/rollback",
                        "/hk/eventRcv",
                        "/dahua/**",
                        "/zhoujie*/**",
                        "/charts/**",
                        "/traffic/**",
                        "/skalarmlog/**",
                        "/skCockpitMapPoints/**",
                        "/skbuild/**",
                        "/parking/**"
                ).permitAll()
                .anyRequest().authenticated()
                .and().csrf().disable();
    }

    /**
     * why add  resourceId
     * https://stackoverflow.com/questions/28703847/how-do-you-set-a-resource-id-for-a-token
     *
     * @param resources
     * @throws Exception
     */
    @Override
    public void configure(ResourceServerSecurityConfigurer resources) throws Exception {
        //客户端授权失败异常处理401错误的提醒配置
        resources.authenticationEntryPoint(resourceAuthExceptionEntryPoint)
                //请求被决绝的错误处理403错误的提醒配置
                .accessDeniedHandler(tunnyAccessDeniedHandler)
                //资源ID的配置一般同服务名称
                .resourceId(BaseConfig.applicationName)
                //此处是关键，默认stateless=true，只支持access_token形式，
                // 如果OAuth2客户端连接需要使用session，所以需要设置成false以支持session授权
                .stateless(true);

    }
}

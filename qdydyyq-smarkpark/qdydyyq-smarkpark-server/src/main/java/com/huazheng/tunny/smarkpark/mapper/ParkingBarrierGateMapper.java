package com.huazheng.tunny.smarkpark.mapper;

import com.huazheng.tunny.smarkpark.api.entity.ParkingBarrierGate;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.huazheng.tunny.common.core.util.Query;
import java.util.List;
import java.util.Map;

/**
 * 停车场-车辆道闸  mapper层
 *
 * <AUTHOR>
 * @date 2025-06-10 13:30:19
 */
public interface ParkingBarrierGateMapper extends BaseMapper<ParkingBarrierGate> {
    /**
     * 查询停车场-车辆道闸信息
     *
     * @param channelId 停车场-车辆道闸ID
     * @return 停车场-车辆道闸信息
     */
    public ParkingBarrierGate selectParkingBarrierGateById(Integer channelId);

    /**
     * 查询停车场-车辆道闸列表
     *
     * @param parkingBarrierGate 停车场-车辆道闸信息
     * @return 停车场-车辆道闸集合
     */
    public List<ParkingBarrierGate> selectParkingBarrierGateList(ParkingBarrierGate parkingBarrierGate);

    /**
     * 模糊查询停车场-车辆道闸列表
     *
     * @param parkingBarrierGate 停车场-车辆道闸信息
     * @return 停车场-车辆道闸集合
     */
    public List<ParkingBarrierGate> selectParkingBarrierGateListByLike(ParkingBarrierGate parkingBarrierGate);


    /**
     * 分页模糊查询停车场-车辆道闸列表
     *
     * @param parkingBarrierGate 停车场-车辆道闸信息
     * @return 停车场-车辆道闸集合
     */
    public List<ParkingBarrierGate> selectParkingBarrierGateListByLike(Query query, ParkingBarrierGate parkingBarrierGate);


    /**
     * 新增停车场-车辆道闸
     *
     * @param parkingBarrierGate 停车场-车辆道闸信息
     * @return 结果
     */
    public int insertParkingBarrierGate(ParkingBarrierGate parkingBarrierGate);

    /**
     * 修改停车场-车辆道闸
     *
     * @param parkingBarrierGate 停车场-车辆道闸信息
     * @return 结果
     */
    public int updateParkingBarrierGate(ParkingBarrierGate parkingBarrierGate);

    /**
     * 删除停车场-车辆道闸
     *
     * @param channelId 停车场-车辆道闸ID
     * @return 结果
     */
    public int deleteParkingBarrierGateById(Integer channelId);

    /**
     * 批量删除停车场-车辆道闸
     *
     * @param channelIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteParkingBarrierGateByIds(Integer[] channelIds);


    void truncateParkingBarrierGate();

    Map<String, Object> statisticsDeviceNum();

    List<Map<String, Object>> statisticsBarrierGateList(Query query, Map<String, Object> map);
}

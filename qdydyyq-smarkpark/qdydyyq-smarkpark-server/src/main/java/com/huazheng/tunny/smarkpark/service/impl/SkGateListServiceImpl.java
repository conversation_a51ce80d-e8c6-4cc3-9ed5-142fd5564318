package com.huazheng.tunny.smarkpark.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import com.huazheng.tunny.smarkpark.mapper.SkGateListMapper;
import com.huazheng.tunny.smarkpark.api.entity.SkGateList;
import com.huazheng.tunny.smarkpark.service.SkGateListService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import javax.annotation.Resource;
import java.util.List;

@Data
@Service("skGateListService")
public class SkGateListServiceImpl extends ServiceImpl<SkGateListMapper, SkGateList> implements SkGateListService {

    @Resource
    private SkGateListMapper skGateListMapper;

    /**
     * 查询一卡通同步 - 闸机列表：包含人车道闸、测速点信息
     *
     * @param id 一卡通同步 - 闸机列表：包含人车道闸、测速点ID
     * @return 一卡通同步 - 闸机列表：包含人车道闸、测速点信息
     */
    @Override
    public SkGateList selectSkGateListById(Integer id)
    {
        return skGateListMapper.selectSkGateListById(id);
    }

    /**
     * 查询一卡通同步 - 闸机列表：包含人车道闸、测速点列表
     *
     * @param skGateList 一卡通同步 - 闸机列表：包含人车道闸、测速点信息
     * @return 一卡通同步 - 闸机列表：包含人车道闸、测速点集合
     */
    @Override
    public List<SkGateList> selectSkGateListList(SkGateList skGateList)
    {
        return skGateListMapper.selectSkGateListList(skGateList);
    }


    /**
     * 分页模糊查询一卡通同步 - 闸机列表：包含人车道闸、测速点列表
     * @return 一卡通同步 - 闸机列表：包含人车道闸、测速点集合
     */
    @Override
    public Page selectSkGateListListByLike(Query query)
    {
        SkGateList skGateList =  BeanUtil.toBean(query.getCondition(), SkGateList.class, CopyOptions.create());
        query.setRecords(skGateListMapper.selectSkGateListListByLike(query,skGateList));
        return query;
    }

    /**
     * 新增一卡通同步 - 闸机列表：包含人车道闸、测速点
     *
     * @param skGateList 一卡通同步 - 闸机列表：包含人车道闸、测速点信息
     * @return 结果
     */
    @Override
    public int insertSkGateList(SkGateList skGateList)
    {
        return skGateListMapper.insertSkGateList(skGateList);
    }

    /**
     * 修改一卡通同步 - 闸机列表：包含人车道闸、测速点
     *
     * @param skGateList 一卡通同步 - 闸机列表：包含人车道闸、测速点信息
     * @return 结果
     */
    @Override
    public int updateSkGateList(SkGateList skGateList)
    {
        return skGateListMapper.updateSkGateList(skGateList);
    }


    /**
     * 删除一卡通同步 - 闸机列表：包含人车道闸、测速点
     *
     * @param id 一卡通同步 - 闸机列表：包含人车道闸、测速点ID
     * @return 结果
     */
    @Override
    public int deleteSkGateListById(Integer id)
    {
        return skGateListMapper.deleteSkGateListById( id);
    };


    /**
     * 批量删除一卡通同步 - 闸机列表：包含人车道闸、测速点对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteSkGateListByIds(Integer[] ids)
    {
        return skGateListMapper.deleteSkGateListByIds( ids);
    }

    /**
     * 一卡通同步 - 闸机列表：包含人车道闸、测速点
     * */
    @Override
    public void syncSkGateList(List<SkGateList> list) {
        skGateListMapper.truncateSkGateList();
        int step = 50;
        for (int i = 0; i < list.size(); i += step) {
            skGateListMapper.syncSkGateList(list.subList(i, (i + step) < list.size() ? (i + step) : list.size()));
        }
    }

}

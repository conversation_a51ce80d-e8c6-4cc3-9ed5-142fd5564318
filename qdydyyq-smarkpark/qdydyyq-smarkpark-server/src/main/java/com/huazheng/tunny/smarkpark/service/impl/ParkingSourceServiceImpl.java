package com.huazheng.tunny.smarkpark.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.huazheng.tunny.smarkpark.api.entity.ParkingBarrierGate;
import com.huazheng.tunny.smarkpark.api.entity.ParkingVehicleRecord;
import com.huazheng.tunny.smarkpark.mapper.ParkingSourceMapper;
import com.huazheng.tunny.smarkpark.service.ParkingSourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;

@DS("sqlserver")
@Service("parkingSourceService")
public class ParkingSourceServiceImpl implements ParkingSourceService {
    @Autowired
    private ParkingSourceMapper parkingSourceMapper;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ParkingBarrierGate> cronParkingBarrierGate(HashMap<Object, Object> objectObjectHashMap) {
        List<ParkingBarrierGate> list = parkingSourceMapper.selectParkingBarrierGate();
        return list;
    }

    @Override
    public List<ParkingVehicleRecord> cronVehicleRecord(HashMap<Object, Object> objectObjectHashMap) {
        try {
            String time = redisTemplate.opsForValue().get("cronVehicleRecordTime");
            if (StrUtil.isBlank(time)) {
                time = this.getThreeMonthsAgoDate();
            }
            List<ParkingVehicleRecord> list = parkingSourceMapper.selectVehicleRecord(time);
            return list;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public void selectParkingSpacesNum() {
        try {
            int total = parkingSourceMapper.selectParkingSpacesNum();
            redisTemplate.opsForValue().set("parkingSpacesTotal", total + "");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String getThreeMonthsAgoDate() {
        // 获取当前日期
        DateTime now = DateUtil.date();
        // 计算三个月前日期（自动处理跨年）
        DateTime threeMonthsAgo = DateUtil.offsetMonth(now, -3);
        // 格式化为字符串（默认yyyy-MM-dd格式）
        String result = DateUtil.format(threeMonthsAgo, "yyyy-MM-dd hh:mm:ss");
        System.out.println("三个月前日期：" + result);
        return result;
    }

}

package com.huazheng.tunny.smarkpark.service;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.smarkpark.api.dto.DaHuaCamerasDTO;
import com.huazheng.tunny.smarkpark.api.dto.RegionsAndCamerasDto;
import com.huazheng.tunny.smarkpark.api.entity.DahuaCameras;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: Service
 * @Author: zhaohaoran
 * @Date: 2023-10-24 10:30:03
 */
public interface DaHuaService extends IService<DahuaCameras> {

    /**
     * @Description: 获取大华token
     * @Param:
     * @Return: String
     * @Author: z<PERSON><PERSON><PERSON>
     * @Date: 2023-10-24 10:30:03
     */
    String getToken() throws Exception;

    String eventSubscribe(HttpServletRequest req) throws Exception;

    R cronOrganizationList(Map<String, Object> param) throws Exception;

    /**
     * @Description: 全量同步设备
     * @Param:
     * @Return: String
     * @Author: sj
     * @Date: 2023-10-24 10:30:03
     */
    R cronDeviceList(Map<String, Object> param) throws Exception;

    /**
     * @Description: 查询大华事件列表
     * @Param:
     * @Return: String
     * @Author: sj
     * @Date: 2023-10-24 10:30:03
     */
    R subscribeList(Map<String, Object> param) throws Exception;

    /**
     * @Description: 查询大华车场列表
     * @Param:
     * @Return: String
     * @Author: sj
     * @Date: 2023-10-24 10:30:03
     */
    Page queryParking(Query query) throws Exception;

    /**
     * @Description: 请求大华post接口
     * @Param:
     * @Return: String
     * @Author: zhaohaoran
     * @Date: 2023-10-24 10:30:03
     */
    JSONObject httpPostDh(Map<String, Object> param, String url) throws Exception;

    List<RegionsAndCamerasDto> regionsAndCamerasList();

    R ssoLogin() throws Exception;

    R cronRegionConfigList(Map<String, Object> param) throws Exception;

    Page camerasPage(Query<Object> objectQuery);

    List<Map<String, Object>> passengerDistribution();

    R cronDeviceTree(Map<String, Object> param);
}


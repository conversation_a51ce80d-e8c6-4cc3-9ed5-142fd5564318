package com.huazheng.tunny.smarkpark.controller;

import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.core.util.StringUtils;
import com.huazheng.tunny.common.log.annotation.SysLog;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.smarkpark.api.entity.SkAlarmLog;
import com.huazheng.tunny.smarkpark.service.SkAlarmLogService;
import com.huazheng.tunny.tools.email.EmailUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 告警记录
 *
 * <AUTHOR> code generator
 * @date 2024-07-16 15:25:31
 */
@Slf4j
@RestController
@RequestMapping("/skalarmlog")
public class SkAlarmLogController {

    @Resource
    private SkAlarmLogService skAlarmLogService;

    @Value("${emailConfig.emailServer}")
    private String emailServer;

    @Value("${emailConfig.fromEmail}")
    private String fromEmail;

    @Value("${emailConfig.password}")
    private String password;

    /**
     * 列表
     *
     * @param params
     * @return
     */
    @SysLog("告警记录列表")
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        params.put("userName", SecurityUtils.getUserInfo().getUserName());
        //对象模糊查询
        return skAlarmLogService.selectSkAlarmLogListByLike(new Query<>(params));
    }

    /**
     * 信息
     *
     * @param eventId
     * @return R
     */
    @SysLog("告警记录详情")
    @GetMapping("/{eventId}")
    public R info(@PathVariable("eventId") String eventId) {
        SkAlarmLog skAlarmLog = skAlarmLogService.selectSkAlarmLogById(eventId);
        return new R<>(skAlarmLog);
    }

    /**
     * 批量处理
     *
     * @param skAlarmLog
     * @return R
     */
    @PostMapping("/handleBatch")
    public R handleBatch(@RequestBody SkAlarmLog skAlarmLog) {
        skAlarmLog.setHandleBy(SecurityUtils.getUserInfo().getUserName());
        skAlarmLog.setHandleByName(SecurityUtils.getUserInfo().getRealName());
        skAlarmLog.setHandleTime(LocalDateTime.now());
        skAlarmLogService.handleBatch(skAlarmLog);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 邮件通知推送
     * */
    @PostMapping("/sendAlarmNotify")
    public R sendAlarmNotify(@RequestBody Map<String, Object> body){
        if(body.get("users") != null) {
            List<String> users = (List<String>) body.get("users");
            if(users.size()>0) {
                List<String> mails = skAlarmLogService.selectMailsByUsers(users);
                SkAlarmLog skAlarmLog = skAlarmLogService.selectSkAlarmLogById(String.valueOf(body.get("eventId")));
                if(skAlarmLog != null){
                    EmailUtils.sendEmail(emailServer, 587, fromEmail, password, mails.stream().toArray(String[]::new), "告警通知", skAlarmLog.getDeviceName() + "错误日志：" + skAlarmLog.getDictName());
                }
            }
        }
        return new R<>(Boolean.TRUE);
    }

    /**
     * 查询告警通知数量
     */
    @SysLog("查询告警通知数量")
    @GetMapping("/getAlarmNotifyCount")
    public R getAlarmNotifyCount() {
        return new R<>(skAlarmLogService.getAlarmNotifyCount(SecurityUtils.getUserInfo().getUserName()));
    }

    /**
     * 查询告警通知数量
     */
    @SysLog("查询告警通知数量")
    @GetMapping("/getAlarmNotifyType")
    public R getAlarmNotifyType() {
        return new R<>(skAlarmLogService.getAlarmNotifyType(SecurityUtils.getUserInfo().getUserName()));
    }
}

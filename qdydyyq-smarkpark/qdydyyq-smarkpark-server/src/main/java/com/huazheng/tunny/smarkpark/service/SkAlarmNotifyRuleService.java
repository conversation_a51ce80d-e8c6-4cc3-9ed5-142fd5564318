package com.huazheng.tunny.smarkpark.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.admin.api.entity.SysDict;
import com.huazheng.tunny.admin.api.entity.SysUser;
import com.huazheng.tunny.smarkpark.api.entity.SkAlarmNotifyRule;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;
import java.util.Map;

/**
 * 安防联动管理 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2024-07-16 18:18:10
 */
public interface SkAlarmNotifyRuleService extends IService<SkAlarmNotifyRule> {
    /**
     * 查询安防联动管理信息
     *
     * @param id 安防联动管理ID
     * @return 安防联动管理信息
     */
    public SkAlarmNotifyRule selectSkAlarmNotifyRuleById(Integer id);

    /**
     * 查询安防联动管理列表
     *
     * @param skAlarmNotifyRule 安防联动管理信息
     * @return 安防联动管理集合
     */
    public List<SkAlarmNotifyRule> selectSkAlarmNotifyRuleList(SkAlarmNotifyRule skAlarmNotifyRule);


    /**
     * 分页模糊查询安防联动管理列表
     * @return 安防联动管理集合
     */
    public Page selectSkAlarmNotifyRuleListByLike(Query query);



    /**
     * 新增安防联动管理
     *
     * @param skAlarmNotifyRule 安防联动管理信息
     * @return 结果
     */
    public int insertSkAlarmNotifyRule(SkAlarmNotifyRule skAlarmNotifyRule);

    /**
     * 修改安防联动管理
     *
     * @param skAlarmNotifyRule 安防联动管理信息
     * @return 结果
     */
    public int updateSkAlarmNotifyRule(SkAlarmNotifyRule skAlarmNotifyRule);

    /**
     * 删除安防联动管理
     *
     * @param id 安防联动管理ID
     * @return 结果
     */
    public int deleteSkAlarmNotifyRuleById(Integer id);

    /**
     * 批量删除安防联动管理
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteSkAlarmNotifyRuleByIds(Integer[] ids);

    List<Map<String,Object>> getAlarmEventTypeList(String alarmTypeCode, String eventTypeName);

    List<SysUser> getPersonList(String userRealname);
}


package com.huazheng.tunny.smarkpark.mapper;

import com.huazheng.tunny.admin.api.entity.SysDict;
import com.huazheng.tunny.admin.api.entity.SysUser;
import com.huazheng.tunny.smarkpark.api.entity.SkAlarmNotifyRule;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.huazheng.tunny.common.core.util.Query;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 安防联动管理  mapper层
 *
 * <AUTHOR> code generator
 * @date 2024-07-16 18:18:10
 */
public interface SkAlarmNotifyRuleMapper extends BaseMapper<SkAlarmNotifyRule> {
    /**
     * 查询安防联动管理信息
     *
     * @param id 安防联动管理ID
     * @return 安防联动管理信息
     */
    public SkAlarmNotifyRule selectSkAlarmNotifyRuleById(Integer id);

    /**
     * 查询安防联动管理列表
     *
     * @param skAlarmNotifyRule 安防联动管理信息
     * @return 安防联动管理集合
     */
    public List<SkAlarmNotifyRule> selectSkAlarmNotifyRuleList(SkAlarmNotifyRule skAlarmNotifyRule);

    /**
     * 模糊查询安防联动管理列表
     *
     * @param skAlarmNotifyRule 安防联动管理信息
     * @return 安防联动管理集合
     */
    public List<SkAlarmNotifyRule> selectSkAlarmNotifyRuleListByLike(SkAlarmNotifyRule skAlarmNotifyRule);


    /**
     * 分页模糊查询安防联动管理列表
     *
     * @param skAlarmNotifyRule 安防联动管理信息
     * @return 安防联动管理集合
     */
    public List<SkAlarmNotifyRule> selectSkAlarmNotifyRuleListByLike(Query query, SkAlarmNotifyRule skAlarmNotifyRule);


    /**
     * 新增安防联动管理
     *
     * @param skAlarmNotifyRule 安防联动管理信息
     * @return 结果
     */
    public int insertSkAlarmNotifyRule(SkAlarmNotifyRule skAlarmNotifyRule);

    /**
     * 修改安防联动管理
     *
     * @param skAlarmNotifyRule 安防联动管理信息
     * @return 结果
     */
    public int updateSkAlarmNotifyRule(SkAlarmNotifyRule skAlarmNotifyRule);

    /**
     * 删除安防联动管理
     *
     * @param id 安防联动管理ID
     * @return 结果
     */
    public int deleteSkAlarmNotifyRuleById(Integer id);

    /**
     * 批量删除安防联动管理
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteSkAlarmNotifyRuleByIds(Integer[] ids);


    List<Map<String,Object>> getAlarmEventTypeList(@Param("alarmTypeCode") String alarmTypeCode, @Param("eventTypeName") String eventTypeName);

    List<SysUser> getPersonList(@Param("userRealname") String userRealname);
}

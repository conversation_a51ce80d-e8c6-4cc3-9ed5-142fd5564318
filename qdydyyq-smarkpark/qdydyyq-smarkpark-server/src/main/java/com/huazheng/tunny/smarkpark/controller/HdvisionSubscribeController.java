package com.huazheng.tunny.smarkpark.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.core.util.StringUtils;
import com.huazheng.tunny.smarkpark.api.entity.SkAlarmLog;
import com.huazheng.tunny.smarkpark.api.entity.SkAlarmLogRaw;
import com.huazheng.tunny.smarkpark.service.SkAlarmLogRawService;
import com.huazheng.tunny.smarkpark.service.SkAlarmLogService;
import com.huazheng.tunny.smarkpark.util.HikvisionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 海康事件订阅
 */
@RestController
@Slf4j
public class HdvisionSubscribeController {

    @Resource
    private HikvisionUtil hdvisionUtil;

    @Resource
    private SkAlarmLogService skAlarmLogService;

    @Resource
    private SkAlarmLogRawService skAlarmLogRawService;

    //告警事件编码
    @Value("${artemisConfig.subscirption.eventTypes}")
    private int[] eventTypes;

    //订阅类型，配合告警事件编码使用（0-订阅原始事件，1-联动事件，2-原始事件和联动事件）
    @Value("${artemisConfig.subscirption.subTypes}")
    private int[] subTypes;

    //告警事件接收接口
    @Value("${artemisConfig.subscirption.eventDest}")
    private String[] eventDest;

    //海康图片地址
    @Value("${artemisConfig.subscirption.picBashUrl}")
    private String picBashUrl;

    /**
     * 按事件类型订阅事件
     */
    @GetMapping("/hk/eventSubscriptionByEventTypes")
    public R eventSubscriptionByEventTypes() {
        List<String> res = new ArrayList<>();
        for (int i = 0; i < eventDest.length; i++) {
            for (int j = 0; j < eventTypes.length; j++) {
                JSONObject body = new JSONObject();
                body.put("eventTypes", new int[]{eventTypes[j]});
                body.put("eventDest", eventDest[i]);
                body.put("subType", subTypes[j]);
                res.add(hdvisionUtil.doPostStringArtemis(HikvisionUtil.eventSubscriptionByEventTypes, body));
            }
        }
        return new R(Boolean.FALSE, null, res);
    }

    /**
     * 事件接收
     */
    @PostMapping("/hk/eventRcv")
    public R eventRcv(@RequestBody Object body) {
        try {
//            log.info("海康事件报文接收：" + JSON.toJSONString(body));

            JSONObject res = JSON.parseObject(JSON.toJSONString(body));
            JSONObject params = res.getJSONObject("params");

            //事件从接收者（程序处理后）发出的时间
            String sendTime = params.getString("sendTime");

            JSONArray events = params.getJSONArray("events");
            for (int events_i = 0; events_i < events.size(); events_i++) {
                JSONObject event = events.getJSONObject(events_i);
                //事件唯一标识 同一事件若上报多次，则上报事件的eventId相同
                String eventId = event.getString("eventId");

                SkAlarmLogRaw skAlarmLogRaw = new SkAlarmLogRaw();
                skAlarmLogRaw.setAlarmSource(0);
                skAlarmLogRaw.setEventId(eventId);
                skAlarmLogRaw.setRawData(JSON.toJSONString(body));
                skAlarmLogRawService.insertOrUpdate(skAlarmLogRaw);

                //事件源编号，物理设备是资源编号
                String srcIndex = null;
                //事件源类型
                String srcType = null;
                //事件源名称
                String srcName = null;
                //事件类型
                String eventType = null;
                // 设备的IP地址
                String ipAddress = null;
                // 设备端口号
                String portNo = null;
                // 背景图URL
                String imageUrl = null;
                //事件数据
                JSONObject data = event.get("data") == null ? null : event.getJSONObject("data");
                if (data != null) {
                    //事件源编号，物理设备是资源编号
                    srcIndex = event.get("srcIndex") == null ? null : event.getString("srcIndex");
                    //事件源类型
                    srcType = event.get("srcType") == null ? null : event.getString("srcType");
                    //事件源名称
                    srcName = event.get("srcName") == null ? null : event.getString("srcName");
                    //事件类型
                    eventType = event.get("eventType") == null ? null : event.getString("eventType");
                    // 设备的IP地址
                    ipAddress = event.get("ipAddress") == null ? null : data.getString("ipAddress");
                    // 设备端口号
                    portNo = event.get("portNo") == null ? null : data.getString("portNo");
                    //分析结果
                    JSONArray fielddetection = data.getJSONArray("fielddetection");
                    if (fielddetection != null) {
                        for (int k = 0; k < fielddetection.size(); k++) {
                            JSONObject temp = fielddetection.getJSONObject(k);
                            imageUrl = temp.get("imageUrl") == null ? null : temp.getString("imageUrl");
                        }
                    }
                }
                //事件详情
                JSONArray eventDetails = event.get("eventDetails") == null ? null : event.getJSONArray("eventDetails");
                if (eventDetails != null) {
                    for (int eventDetails_i = 0; eventDetails_i < eventDetails.size(); eventDetails_i++) {
                        JSONObject eventDetail = eventDetails.getJSONObject(eventDetails_i);
                        JSONObject eventDetail_data = eventDetail.getJSONObject("data");
                        //事件源编号，物理设备是资源编号
                        srcIndex = eventDetail.get("srcIndex") == null ? null : eventDetail.getString("srcIndex");
                        //事件源类型
                        srcType = eventDetail.get("srcType") == null ? null : eventDetail.getString("srcType");
                        //事件源名称
                        srcName = eventDetail_data.get("channelName") == null ? null : eventDetail_data.getString("channelName");
                        //事件类型
                        eventType = eventDetail.get("eventType") == null ? null : eventDetail.getString("eventType");
                        // 设备的IP地址
                        ipAddress = eventDetail_data.get("ipAddress") == null ? null : eventDetail_data.getString("ipAddress");
                        // 设备端口号
                        portNo = eventDetail_data.get("portNo") == null ? null : eventDetail_data.getString("portNo");
                    }
                }
                //联动事件
                JSONArray linkageResult = event.get("linkageResult") == null ? null : event.getJSONArray("linkageResult");
                if (linkageResult != null) {
                    for (int linkageResult_i = 0; linkageResult_i < linkageResult.size(); linkageResult_i++) {
                        JSONObject linkage = linkageResult.getJSONObject(linkageResult_i);
                        String content = linkage.get("content") == null ? null : linkage.getString("content");
                        if (StringUtils.isNotBlank(content)) {
                            JSONArray contactArray = JSONArray.parseArray(content);
                            for (int contactArray_i = 0; contactArray_i < contactArray.size(); contactArray_i++) {
                                JSONObject contact = contactArray.getJSONObject(contactArray_i);
                                String picUrls = contact.get("picUrls") == null ? null : contact.getString("picUrls");
                                if (StringUtils.isNotBlank(picUrls)) {
                                    JSONArray picUrlArray = JSONArray.parseArray(picUrls);
                                    for (int picUrlArray_i = 0; picUrlArray_i < picUrlArray.size(); picUrlArray_i++) {
                                        imageUrl = picBashUrl + picUrlArray.getString(picUrlArray_i);
                                    }
                                }
                            }
                        }
                    }
                }


                // 告警记录存库
                SkAlarmLog skAlarmLog = new SkAlarmLog();
                skAlarmLog.setSendTime(LocalDateTime.parse(sendTime, DateTimeFormatter.ISO_OFFSET_DATE_TIME));
                skAlarmLog.setAlarmSource(0);
                skAlarmLog.setEventId(eventId);
                skAlarmLog.setSrcIndex(srcIndex);
                skAlarmLog.setSrcType(srcType);
                skAlarmLog.setSrcName(srcName);
                skAlarmLog.setEventType(eventType);
                skAlarmLog.setIpAddress(ipAddress);
                skAlarmLog.setPortNo(portNo);
                skAlarmLog.setImageUrl(imageUrl);
                skAlarmLogService.insertOrUpdate(skAlarmLog);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new R(Boolean.TRUE);
    }


    /**
     * 根据用户查询事件订阅详情
     */
    @GetMapping("/hk/eventSubscriptionView")
    public R eventSubscriptionView() {
        JSONObject body = new JSONObject();
        String resStr = hdvisionUtil.doPostStringArtemis(HikvisionUtil.eventSubscriptionView, body);
        return new R(Boolean.FALSE, null, resStr);
    }


    /**
     * 按事件类型取消订阅
     */
    @GetMapping("/hk/eventUnSubscriptionByEventTypes")
    public R eventUnSubscriptionByEventTypes() {
        JSONObject body = new JSONObject();
        body.put("eventTypes", eventTypes);
        String resStr = hdvisionUtil.doPostStringArtemis(HikvisionUtil.eventUnSubscriptionByEventTypes, body);
        return new R(Boolean.FALSE, null, resStr);
    }

}

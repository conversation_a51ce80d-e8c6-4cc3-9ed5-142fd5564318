package com.huazheng.tunny.smarkpark.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.core.util.StringUtils;
import com.huazheng.tunny.common.log.annotation.SysLog;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.smarkpark.api.entity.SkBlacklistFace;
import com.huazheng.tunny.smarkpark.api.entity.SkBlacklistPlan;
import com.huazheng.tunny.smarkpark.service.SkBlacklistFaceService;
import com.huazheng.tunny.smarkpark.service.SkBlacklistPlanService;
import com.huazheng.tunny.smarkpark.util.HikvisionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 人员布控计划&分组
 *
 * <AUTHOR> code generator
 * @date 2024-07-18 18:42:09
 */
@Slf4j
@RestController
@RequestMapping("/skblacklistplan")
public class SkBlacklistPlanController {

    @Autowired
    private SkBlacklistPlanService skBlacklistPlanService;

    @Autowired
    private SkBlacklistFaceService skBlacklistFaceService;

    @Resource
    private HikvisionUtil hdvisionUtil;

    /**
     * 列表
     *
     * @param params
     * @return
     */
    @SysLog("人员布控列表")
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //对象模糊查询
        return skBlacklistPlanService.selectSkBlacklistPlanListByLike(new Query<>(params));
    }

    /**
     * 信息
     *
     * @param planIndexCode
     * @return R
     */
    @SysLog("人员布控详情")
    @GetMapping("/{planIndexCode}")
    public R info(@PathVariable("planIndexCode") String planIndexCode) {
        SkBlacklistPlan skBlacklistPlan = skBlacklistPlanService.selectById(planIndexCode);
        skBlacklistPlan.setFaceList(skBlacklistFaceService.selectList(new EntityWrapper<SkBlacklistFace>().eq("face_group_index_code", skBlacklistPlan.getFaceGroupIndexCode())));

        // 查询海康平台关联的人脸列表
        JSONObject body_face = new JSONObject();
        body_face.put("faceGroupIndexCode", skBlacklistPlan.getFaceGroupIndexCode());
        String resStr_face = hdvisionUtil.doPostStringArtemis(HikvisionUtil.faceList, body_face);
        JSONObject res_face = JSON.parseObject(resStr_face);
        if ("0".equals(res_face.get("code"))) {
            log.debug("人员布控：" + resStr_face);
        }

        // 查询海康平台关联的重点目标识别计划
        JSONObject body_plan = new JSONObject();
        body_plan.put("faceGroupIndexCodes", new String[]{skBlacklistPlan.getFaceGroupIndexCode()});
        String resStr_plan = hdvisionUtil.doPostStringArtemis(HikvisionUtil.blackListPlanList, body_plan);
        JSONObject res_plan = JSON.parseObject(resStr_plan);
        if ("0".equals(res_plan.get("code"))) {
            log.debug("人员布控：" + resStr_plan);
        }

        // 查询海康平台关联的人脸分组
        JSONObject body_group = new JSONObject();
        body_group.put("indexCodes", new String[]{skBlacklistPlan.getFaceGroupIndexCode()});
        String resStr_group = hdvisionUtil.doPostStringArtemis(HikvisionUtil.faceGroupList, body_group);
        JSONObject res_group = JSON.parseObject(resStr_group);
        if ("0".equals(res_group.get("code"))) {
            log.debug("人员布控：" + resStr_group);
        }

        return new R<>(skBlacklistPlan);
    }

    /**
     * 保存
     *
     * @param skBlacklistPlan
     * @return R
     */
    @SysLog("人员布控保存")
    @PostMapping
    public R save(@RequestBody SkBlacklistPlan skBlacklistPlan) {
        // 查询是否已存在分组
        JSONObject body_group_checked = new JSONObject();
        body_group_checked.put("name", skBlacklistPlan.getName());
        String resStr_group_checked = hdvisionUtil.doPostStringArtemis(HikvisionUtil.faceGroupList, body_group_checked);
        JSONObject res_group_checked = JSON.parseObject(resStr_group_checked);
        if ("0".equals(res_group_checked.get("code"))) {
            JSONArray data = res_group_checked.getJSONArray("data");
            if(data.size() > 0){
                for(int i = 0; i < data.size(); i++){
                    if(skBlacklistPlan.getName().equals(data.getJSONObject(i).getString("name"))){
                        return new R(Boolean.FALSE, "布控名称已存在！");
                    }
                }
            }
        } else {
            return new R(Boolean.FALSE, null, res_group_checked);
        }

        // 创建分组
        JSONObject body_group = new JSONObject();
        body_group.put("name", skBlacklistPlan.getName());
        body_group.put("description", StringUtils.isNotEmpty(skBlacklistPlan.getDescription()) ? skBlacklistPlan.getDescription() : skBlacklistPlan.getName());
        String resStr_group = hdvisionUtil.doPostStringArtemis(HikvisionUtil.faceGroupAddition, body_group);
        JSONObject res_group = JSON.parseObject(resStr_group);
        if ("0".equals(res_group.get("code"))) {
            String faceGroupIndexCode = res_group.getJSONObject("data").getString("indexCode");
            skBlacklistPlan.setFaceGroupIndexCode(faceGroupIndexCode);
        } else {
            return new R(Boolean.FALSE, null, resStr_group);
        }

        // 创建人脸并绑定分组
        for (SkBlacklistFace skBlacklistFace : skBlacklistPlan.getFaceList()) {
            JSONObject body_face = new JSONObject();
            body_face.put("faceGroupIndexCode", skBlacklistPlan.getFaceGroupIndexCode());
            JSONObject faceInfo = new JSONObject();
            faceInfo.put("name", skBlacklistFace.getName());
            faceInfo.put("sex", skBlacklistFace.getSex());
            faceInfo.put("certificateType", skBlacklistFace.getCertificateType());
            faceInfo.put("certificateNum", skBlacklistFace.getCertificateNum());
            body_face.put("faceInfo", faceInfo);
            JSONObject facePic = new JSONObject();
            facePic.put("faceUrl", JSON.parseArray(skBlacklistFace.getFacePicUrl()).getJSONObject(0).getString("url"));
            body_face.put("facePic", facePic);
            String resStr_face = hdvisionUtil.doPostStringArtemis(HikvisionUtil.faceAddition, body_face);
            JSONObject res_face = JSON.parseObject(resStr_face);
            if ("0".equals(res_face.get("code"))) {
                String faceIndexCode = res_face.getJSONObject("data").getString("indexCode");
                skBlacklistFace.setIndexCode(faceIndexCode);
                skBlacklistFace.setFaceGroupIndexCode(skBlacklistPlan.getFaceGroupIndexCode());
            } else {
                return new R(Boolean.FALSE, null, resStr_face);
            }
            skBlacklistFaceService.insert(skBlacklistFace);
        }

        // 创建计划并绑定分组
        JSONObject body_plan = new JSONObject();
        body_plan.put("name", skBlacklistPlan.getName());
        body_plan.put("faceGroupIndexCodes", new String[]{skBlacklistPlan.getFaceGroupIndexCode()});
        String[] cameraIndexCodes = skBlacklistPlan.getCameraIndexCodes().split(",");
        body_plan.put("cameraIndexCodes", new String[]{cameraIndexCodes[cameraIndexCodes.length - 1]});
        // 识别资源类型，SUPER_BRAIN 超脑，FACE_RECOGNITION_SERVER 脸谱，COMPARISON 海康深眸
        body_plan.put("recognitionResourceType", "COMPARISON");
        body_plan.put("description", skBlacklistPlan.getDescription());
        body_plan.put("threshold", skBlacklistPlan.getThreshold());
        String resStr_plan = hdvisionUtil.doPostStringArtemis(HikvisionUtil.blackListPlanAddition, body_plan);
        JSONObject res_plan = JSON.parseObject(resStr_plan);
        if ("0".equals(res_plan.get("code"))) {
            String planIndexCode = res_plan.getString("data");
            skBlacklistPlan.setPlanIndexCode(planIndexCode);
        } else {
            return new R(Boolean.FALSE, null, resStr_plan);
        }
        skBlacklistPlan.setCreateBy(SecurityUtils.getUserInfo().getUserName());
        skBlacklistPlan.setCreateByName(SecurityUtils.getUserInfo().getRealName());
        skBlacklistPlan.setCreateTime(LocalDateTime.now());
        skBlacklistPlanService.insert(skBlacklistPlan);

        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     *
     * @param skBlacklistPlan
     * @return R
     */
    @SysLog("人员布控修改")
    @PostMapping("/update")
    public R update(@RequestBody SkBlacklistPlan skBlacklistPlan) {

        // 更新分组
        JSONObject body_group = new JSONObject();
        body_group.put("indexCode", skBlacklistPlan.getFaceGroupIndexCode());
        body_group.put("name", skBlacklistPlan.getName());
        body_group.put("description", StringUtils.isNotEmpty(skBlacklistPlan.getDescription()) ? skBlacklistPlan.getDescription() : skBlacklistPlan.getName());
        String resStr_group = hdvisionUtil.doPostStringArtemis(HikvisionUtil.faceGroupUpdate, body_group);
        JSONObject res_group = JSON.parseObject(resStr_group);
        if ("0".equals(res_group.get("code"))) {
        } else {
            return new R(Boolean.FALSE, null, resStr_group);
        }

        // 清空分组下所有人脸
        List<SkBlacklistFace> oldFaceList = skBlacklistFaceService.selectList(new EntityWrapper<SkBlacklistFace>().eq("face_group_index_code", skBlacklistPlan.getFaceGroupIndexCode()));
        if (oldFaceList.size() > 0) {
            JSONObject body_face = new JSONObject();
            body_face.put("faceGroupIndexCode", skBlacklistPlan.getFaceGroupIndexCode());
            body_face.put("indexCodes", oldFaceList.stream().map(SkBlacklistFace::getIndexCode).collect(Collectors.toList()));
            String resStr_face = hdvisionUtil.doPostStringArtemis(HikvisionUtil.faceDeletion, body_face);
            JSONObject res_face = JSON.parseObject(resStr_face);
            if ("0".equals(res_face.get("code"))) {
            } else {
                return new R(Boolean.FALSE, null, resStr_face);
            }
        }
        skBlacklistFaceService.delete(new EntityWrapper<SkBlacklistFace>().eq("face_group_index_code", skBlacklistPlan.getFaceGroupIndexCode()));
        // 创建人脸并绑定分组
        for (SkBlacklistFace skBlacklistFace : skBlacklistPlan.getFaceList()) {
            JSONObject body_face = new JSONObject();
            body_face.put("faceGroupIndexCode", skBlacklistPlan.getFaceGroupIndexCode());
            JSONObject faceInfo = new JSONObject();
            faceInfo.put("name", skBlacklistFace.getName());
            faceInfo.put("sex", skBlacklistFace.getSex());
            faceInfo.put("certificateType", skBlacklistFace.getCertificateType());
            faceInfo.put("certificateNum", skBlacklistFace.getCertificateNum());
            body_face.put("faceInfo", faceInfo);
            JSONObject facePic = new JSONObject();
            facePic.put("faceUrl", JSON.parseArray(skBlacklistFace.getFacePicUrl()).getJSONObject(0).getString("url"));
            body_face.put("facePic", facePic);
            String resStr_face = hdvisionUtil.doPostStringArtemis(HikvisionUtil.faceAddition, body_face);
            JSONObject res_face = JSON.parseObject(resStr_face);
            if ("0".equals(res_face.get("code"))) {
                String faceIndexCode = res_face.getJSONObject("data").getString("indexCode");
                skBlacklistFace.setIndexCode(faceIndexCode);
                skBlacklistFace.setFaceGroupIndexCode(skBlacklistPlan.getFaceGroupIndexCode());
            } else {
                return new R(Boolean.FALSE, null, resStr_face);
            }
            skBlacklistFaceService.insert(skBlacklistFace);
        }


        SkBlacklistPlan old_plan = skBlacklistPlanService.selectById(skBlacklistPlan.getPlanIndexCode());
        // 删除计划
        JSONObject body_plan_del = new JSONObject();
        body_plan_del.put("indexCodes", new String[]{skBlacklistPlan.getPlanIndexCode()});
        String resStr_plan_del = hdvisionUtil.doPostStringArtemis(HikvisionUtil.blackListPlanDeletion, body_plan_del);
        JSONObject res_plan_del = JSON.parseObject(resStr_plan_del);
        if ("0".equals(res_plan_del.get("code"))) {
        } else {
            return new R(Boolean.FALSE, null, res_plan_del);
        }
        skBlacklistPlanService.deleteSkBlacklistPlanById(skBlacklistPlan.getPlanIndexCode());
        // 创建计划
        JSONObject body_plan = new JSONObject();
        body_plan.put("name", skBlacklistPlan.getName());
        body_plan.put("faceGroupIndexCodes", new String[]{skBlacklistPlan.getFaceGroupIndexCode()});
        String[] cameraIndexCodes = skBlacklistPlan.getCameraIndexCodes().split(",");
        body_plan.put("cameraIndexCodes", new String[]{cameraIndexCodes[cameraIndexCodes.length - 1]});
        // 识别资源类型，SUPER_BRAIN 超脑，FACE_RECOGNITION_SERVER 脸谱，COMPARISON 海康深眸
        body_plan.put("recognitionResourceType", "COMPARISON");
        body_plan.put("description", skBlacklistPlan.getDescription());
        body_plan.put("threshold", skBlacklistPlan.getThreshold());
        String resStr_plan = hdvisionUtil.doPostStringArtemis(HikvisionUtil.blackListPlanAddition, body_plan);
        JSONObject res_plan = JSON.parseObject(resStr_plan);
        if ("0".equals(res_plan.get("code"))) {
            String planIndexCode = res_plan.getString("data");
            skBlacklistPlan.setPlanIndexCode(planIndexCode);
        } else {
            return new R(Boolean.FALSE, null, resStr_plan);
        }
        skBlacklistPlan.setCreateBy(old_plan.getCreateBy());
        skBlacklistPlan.setCreateByName(old_plan.getCreateByName());
        skBlacklistPlan.setCreateTime(old_plan.getCreateTime());
        skBlacklistPlanService.insert(skBlacklistPlan);

        return new R<>(Boolean.TRUE);
    }


    /**
     * 删除
     *
     * @param planIndexCode
     * @return R
     */
    @SysLog("人员布控删除")
    @GetMapping("/del/{planIndexCode}")
    public R delete(@PathVariable String planIndexCode) {
        SkBlacklistPlan skBlacklistPlan = skBlacklistPlanService.selectById(planIndexCode);

        // 清空分组下所有人脸
        List<SkBlacklistFace> oldFaceList = skBlacklistFaceService.selectList(new EntityWrapper<SkBlacklistFace>().eq("face_group_index_code", skBlacklistPlan.getFaceGroupIndexCode()));
        if (oldFaceList.size() > 0) {
            JSONObject body_face = new JSONObject();
            body_face.put("faceGroupIndexCode", skBlacklistPlan.getFaceGroupIndexCode());
            body_face.put("indexCodes", oldFaceList.stream().map(SkBlacklistFace::getIndexCode).collect(Collectors.toList()));
            String resStr_face = hdvisionUtil.doPostStringArtemis(HikvisionUtil.faceDeletion, body_face);
            JSONObject res_face = JSON.parseObject(resStr_face);
            if ("0".equals(res_face.get("code"))) {
            } else {
                return new R(Boolean.FALSE, null, resStr_face);
            }
        }

        // 删除计划
        JSONObject body_plan = new JSONObject();
        body_plan.put("indexCodes", new String[]{skBlacklistPlan.getPlanIndexCode()});
        String resStr_plan = hdvisionUtil.doPostStringArtemis(HikvisionUtil.blackListPlanDeletion, body_plan);
        JSONObject res_plan = JSON.parseObject(resStr_plan);
        if ("0".equals(res_plan.get("code"))) {
        } else {
            return new R(Boolean.FALSE, null, resStr_plan);
        }

        // 删除分组
        JSONObject body_group = new JSONObject();
        body_group.put("indexCodes", new String[]{skBlacklistPlan.getFaceGroupIndexCode()});
        String resStr_group = hdvisionUtil.doPostStringArtemis(HikvisionUtil.faceGroupDeletion, body_group);
        JSONObject res_group = JSON.parseObject(resStr_group);
        if ("0".equals(res_group.get("code"))) {
        } else {
            return new R(Boolean.FALSE, null, resStr_group);
        }
        skBlacklistPlan.setUpdateBy(SecurityUtils.getUserInfo().getUserName());
        skBlacklistPlan.setUpdateByName(SecurityUtils.getUserInfo().getRealName());
        skBlacklistPlan.setUpdateTime(LocalDateTime.now());
        skBlacklistPlan.setDelFlag("0");
        skBlacklistPlanService.updateSkBlacklistPlan(skBlacklistPlan);

        return new R<>(Boolean.TRUE);
    }

}

package com.huazheng.tunny.smarkpark.controller;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.smarkpark.api.entity.EspDoorOutIntoInfo;
import com.huazheng.tunny.smarkpark.service.EspDoorOutIntoInfoService;
import com.huazheng.tunny.smarkpark.util.KeytopUtil;
import com.xxl.job.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 门禁通行记录
 *
 * <AUTHOR> code generator
 * @date 2025-05-07 11:43:52
 */
@Slf4j
@RestController
@RequestMapping("/espDoorOutIntoInfo")
public class EspDoorOutIntoInfoController {

    @Resource
    private EspDoorOutIntoInfoService espDoorOutIntoInfoService;

    @Resource
    private KeytopUtil keytopUtil;

    /**
     * 手动同步人员通行记录
     *
     * 同步3小时内的门禁通行记录
     */
    @GetMapping("/syncDoorOutIntoInfo")
    public R syncDoorOutIntoInfo() {
        log.info("手动同步人员通行记录开始 " + DateUtil.now());
        //设置请求参数
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("current", 1);
        paramMap.put("size", 100000);
        paramMap.put("startTime", DateUtil.offsetHour(DateUtil.date(), -3).toString());
        paramMap.put("endTime", DateUtil.now());

        keytopUtil.syncDoorOutIntoInfo(paramMap, keytopUtil.getToken(true));

        log.info("手动同步人员通行记录成功 " + DateUtil.now());
        return new R<>(Boolean.TRUE);
    }

    /**
     * 列表
     *
     * @param params
     * @return
     */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //对象模糊查询
        return espDoorOutIntoInfoService.selectEspDoorOutIntoInfoListByLike(new Query<>(params));
    }

    /**
     * 信息
     *
     * @param id
     * @return R
     */
    @GetMapping("/{id}")
    public R info(@PathVariable("id") String id) {
        EspDoorOutIntoInfo espDoorOutIntoInfo = espDoorOutIntoInfoService.selectById(id);
        return new R<>(espDoorOutIntoInfo);
    }

    /**
     * 保存
     *
     * @param espDoorOutIntoInfo
     * @return R
     */
    @PostMapping
    public R save(@RequestBody EspDoorOutIntoInfo espDoorOutIntoInfo) {
        espDoorOutIntoInfoService.insert(espDoorOutIntoInfo);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     *
     * @param espDoorOutIntoInfo
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody EspDoorOutIntoInfo espDoorOutIntoInfo) {
        espDoorOutIntoInfoService.updateById(espDoorOutIntoInfo);
        return new R<>(Boolean.TRUE);
    }


    /**
     * 删除
     *
     * @param id
     * @return R
     */
    @GetMapping("/del/{id}")
    public R delete(@PathVariable String id) {
        espDoorOutIntoInfoService.deleteById(id);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return R
     */
    @PostMapping("/delObjs")
    public R delObjs(@RequestBody List<String> ids) {
        espDoorOutIntoInfoService.deleteBatchIds(ids);
        return new R<>(Boolean.TRUE);
    }

}

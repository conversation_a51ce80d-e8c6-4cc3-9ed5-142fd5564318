package com.huazheng.tunny.smarkpark.mqtt;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.io.Serializable;

/**
 * @Description: 监听设备上报 MQ
 * @Author: wx
 * @Date: 2021/6/9 10:53
 */
@Configuration      //1.主要用于标记配置类，兼备Component的效果。
@EnableScheduling   // 2.开启定时任务
@Slf4j
@Data
public class MqttListener implements CommandLineRunner, Serializable {
    private static final long serialVersionUID = 1L;

    @Value("${address.mqtt:tcp://**************:1883}")
    private String host;
    @Value("${server.port:9038}")
    private String serverPort;
    @Value("${topic.event:/iot/events/+/+/up}")
    private String topicEvent;

    private Boolean taskFaly = false;

    @Override
    public void run(String... args) throws Exception {
        //this.startMqtt();
    }

    public void startMqtt() {
        try {
            log.info("clientIot开始连接MQTT服务器！！！！！！！！！");
            // 创建 MQTT 连接
            ServerMqtt.connect(host, "IOT_Client" + "_" + IpUtils.getIpAddress() + "_" + serverPort + "_" + System.currentTimeMillis());
            // 事件上报
            ServerMqtt.subscribe(topicEvent);

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Scheduled(fixedRate = 600000)
    private void configureTasks() {
        if (!taskFaly) {
            taskFaly = true;
            return;
        }
        String message = "客户机未连接";
        String message1 = "Client is not connected";
        try {
            String topic = "/mqtt/server/clientIot";
            ServerMqtt.publish(topic, "心跳检测", 0);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e.getMessage().indexOf(message) >= 0 || e.getMessage().indexOf(message1) >= 0) {
                this.startMqtt();
            }
        }
    }
}

package com.huazheng.tunny.smarkpark.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;

import javax.crypto.Cipher;
import java.security.KeyFactory;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
public class HttpUtils {

    static String getPublicKey = "/evo-apigw/evo-oauth/1.0.0/oauth/public-key";

    static String getToken = "/evo-apigw/evo-oauth/1.0.0/oauth/extend/token";

    /**
     * 获取token
     *
     * @throws Exception
     */
    public static String getToken(RedisTemplate<String, String> redisTemplate, String http, String userName, String password, String clientId, String clientSecret) throws Exception {
        //获取大华token
        String accessToken;
        //先查询redis内是否存储大华token，否则请求大华接口获取token
        String redisToken = redisTemplate.opsForValue().get("dh_access_token");
        String refreshToken = redisTemplate.opsForValue().get("dh_refresh_token");

        if (StrUtil.isNotEmpty(redisToken) && StrUtil.isNotEmpty(refreshToken)) {
            accessToken = redisToken;
        } else {
            String publicKey = getPublicKey(http);
            // 加密密码
            String encryptedPassword = encrypt(publicKey, password);

            //设置请求参数
            HashMap<String, Object> paramMap = new HashMap<>();
            paramMap.put("grant_type", "password");
            paramMap.put("username", userName);
            paramMap.put("password", encryptedPassword);
            paramMap.put("client_id", clientId);
            paramMap.put("client_secret", clientSecret);
            paramMap.put("public_key", publicKey);

            String json = JSONUtil.toJsonStr(paramMap);
            HttpRequest request = HttpUtil.createPost(http + getToken);
            request.header("Content-Type", "application/json");
            request.body(json);
            // 发送POST请求
            final String result = request.execute().body();

            System.out.println(result);
            //解析参数
            JSONObject jsonObject = JSONUtil.parseObj(result);
            String data = String.valueOf(jsonObject.get("data"));
            JSONObject dataObject = JSONUtil.parseObj(data);
            //token
            accessToken = String.valueOf(dataObject.get("access_token"));
            //刷新token
            refreshToken = String.valueOf(dataObject.get("refresh_token"));
            //有效期
            Long expireTime = Long.valueOf(String.valueOf(dataObject.get("expires_in")));
            System.out.println(accessToken);

            //存入redis
            redisTemplate.opsForValue().set("dh_access_token", accessToken, expireTime, TimeUnit.SECONDS);
            //存入redis
            redisTemplate.opsForValue().set("dh_refresh_token", refreshToken, expireTime, TimeUnit.SECONDS);
        }

        return accessToken;
    }

    /**
     * 获取大华公钥
     *
     * @throws Exception
     */
    public static String getPublicKey(String http) {
        log.info("getPublicKey:{}", http + getPublicKey);
        String result = HttpUtil.get(http + getPublicKey);
        JSONObject jsonObject = JSONUtil.parseObj(result);
        String data = String.valueOf(jsonObject.get("data"));
        JSONObject dataObject = JSONUtil.parseObj(data);
        String publicKey = String.valueOf(dataObject.get("publicKey"));
        return publicKey;
    }

    /**
     * 基于 原生RSA公钥加密
     *
     * @throws Exception
     */
    public static String encrypt(String publicKey, String password) throws Exception {
        String outStr = "";
        try {
            byte[] decoded = Base64.decode(publicKey);
            RSAPublicKey pubKey =
                    (RSAPublicKey)
                            KeyFactory.getInstance("RSA").generatePublic(new X509EncodedKeySpec(decoded));
            // RSA加密
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.ENCRYPT_MODE, pubKey);
            //**此处Base64编码，开发者可以使用自己的库**
            outStr = new String(Base64.encode(cipher.doFinal(password.getBytes("UTF-8"))));
            System.out.println(outStr);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return outStr;
    }

    /*
     *
     * 大华get请求方法
     *
     * */
    public static String doGet(String url, String token, RedisTemplate<String, String> redisTemplate, String http, String userName, String password, String clientId, String clientSecret) throws Exception {
        //设置请求头
        HttpRequest request = HttpRequest.get(url)
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer " + token);
        String body = request.execute().body();
        log.error(body);
        //解析返回参数
        JSONObject jsonObject = JSONUtil.parseObj(body);
        String code = String.valueOf(jsonObject.get("code"));
        //如果大华接口鉴权未通过，则重新获取token再请求一次
        if (StrUtil.isNotEmpty(code) && "27001007".equals(code)) {
            log.info(body);
            token = getToken(redisTemplate, http, userName, password, clientId, clientSecret);
            //再次请求
            HttpRequest request2 = HttpRequest.get(url)
                    .header("Content-Type", "application/json")
                    .header("Authorization", "Bearer " + token);
            String body2 = request2.execute().body();
            JSONObject jsonObject2 = JSONUtil.parseObj(body2);
            String code2 = String.valueOf(jsonObject2.get("code"));
            if (StrUtil.isNotEmpty(code2) && "27001007".equals(code2)) {
                log.error(body2);
            }
            return body2;
        }
        return body;
    }

    public static String doGetLot(String url, String token, RedisTemplate<String, String> redisTemplate, String http, String userName, String password, String clientId, String clientSecret) throws Exception {
        //设置请求头
        HttpRequest request = HttpRequest.get(url)
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer " + token);
        String body = request.execute().body();
        //解析返回参数
        JSONObject jsonObject = JSONUtil.parseObj(body);
        String code = String.valueOf(jsonObject.get("code"));
        //如果大华接口鉴权未通过，则重新获取token再请求一次
        if (StrUtil.isNotEmpty(code) && "27001007".equals(code)) {
            token = getToken(redisTemplate, http, userName, password, clientId, clientSecret);
            //再次请求
            HttpRequest request2 = HttpRequest.get(url)
                    .header("Content-Type", "application/json")
                    .header("Authorization", "Bearer " + token);
            String body2 = request2.execute().body();
            JSONObject jsonObject2 = JSONUtil.parseObj(body2);
            String code2 = String.valueOf(jsonObject2.get("code"));
            if (StrUtil.isNotEmpty(code2) && "27001007".equals(code2)) {
                log.error(body2);
            }
            return body2;
        }
        return body;
    }

    /*
     *
     * 大华post请求方法
     *
     * */
    public static String doPost(String url, String token, RedisTemplate<String, String> redisTemplate, String http, String userName, String password, String clientId, String clientSecret) throws Exception {
        //设置请求头
        HttpRequest request = HttpRequest.post(url)
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer " + token);
        String body = request.execute().body();
        log.info(body);
        //解析返回参数
        JSONObject jsonObject = JSONUtil.parseObj(body);
        String code = String.valueOf(jsonObject.get("code"));
        //如果大华接口鉴权未通过，则重新获取token再请求一次
        if (StrUtil.isNotEmpty(code) && "27001007".equals(code)) {
            log.error(body);
            token = getToken(redisTemplate, http, userName, password, clientId, clientSecret);
            //再次请求
            HttpRequest request2 = HttpRequest.post(url)
                    .header("Content-Type", "application/json")
                    .header("Authorization", "Bearer " + token);
            String body2 = request2.execute().body();
            JSONObject jsonObject2 = JSONUtil.parseObj(body2);
            String code2 = String.valueOf(jsonObject2.get("code"));
            if (StrUtil.isNotEmpty(code2) && "27001007".equals(code2)) {
                log.error(body2);
            }
            return body2;
        }
        return body;
    }

    public static String doPostWithBody(String url, String token, RedisTemplate<String, String> redisTemplate, String http, String userName, String password, String clientId, String clientSecret, String bodyStr) throws Exception {
        //设置请求头
        HttpRequest request = HttpRequest.post(url)
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer " + token)
                .body(bodyStr);
        String body = request.execute().body();
        log.info(body);
        //解析返回参数
        JSONObject jsonObject = JSONUtil.parseObj(body);
        String code = String.valueOf(jsonObject.get("code"));
        //如果大华接口鉴权未通过，则重新获取token再请求一次
        if (StrUtil.isNotEmpty(code) && "27001007".equals(code)) {
            log.error(body);
            token = getToken(redisTemplate, http, userName, password, clientId, clientSecret);
            //再次请求
            HttpRequest request2 = HttpRequest.post(url)
                    .header("Content-Type", "application/json")
                    .header("Authorization", "Bearer " + token);
            String body2 = request2.execute().body();
            JSONObject jsonObject2 = JSONUtil.parseObj(body2);
            String code2 = String.valueOf(jsonObject2.get("code"));
            if (StrUtil.isNotEmpty(code2) && "27001007".equals(code2)) {
                log.error(body2);
            }
            return body2;
        }
        return body;
    }
}

package com.huazheng.tunny.smarkpark.util;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PlateColor {
    BLUE(1, "蓝色"),
    YELLOW(2, "黄色"),
    WHITE(3, "白色"),
    BLACK(4, "黑色"),
    GREEN(5, "绿色"),
    OTHER(99, "其他车型");
    private final int code;
    private final String name;

    /**
     * 根据编码获取枚举
     */
    public static String getNameByCode(int code) {
        for (PlateColor type : values()) {
            if (type.code == code) {
                return type.name;
            }
        }
        return OTHER.name;
    }

}

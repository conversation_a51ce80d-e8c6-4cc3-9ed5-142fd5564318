package com.huazheng.tunny.smarkpark.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.StrUtil;
import com.huazheng.tunny.smarkpark.api.entity.EspDoorOutIntoInfo;
import com.huazheng.tunny.smarkpark.mapper.EspDoorOutIntoInfoMapper;
import com.huazheng.tunny.smarkpark.service.EspDoorOutIntoInfoService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Data
@Service("espDoorOutIntoInfoService")
public class EspDoorOutIntoInfoServiceImpl extends ServiceImpl<EspDoorOutIntoInfoMapper, EspDoorOutIntoInfo> implements EspDoorOutIntoInfoService {

    @Autowired
    private EspDoorOutIntoInfoMapper espDoorOutIntoInfoMapper;

    /**
     * 查询【科拓】门禁通行记录表信息
     *
     * @param id 【科拓】门禁通行记录表ID
     * @return 【科拓】门禁通行记录表信息
     */
    @Override
    public EspDoorOutIntoInfo selectEspDoorOutIntoInfoById(String id) {
        return espDoorOutIntoInfoMapper.selectEspDoorOutIntoInfoById(id);
    }

    /**
     * 查询【科拓】门禁通行记录表列表
     *
     * @param espDoorOutIntoInfo 【科拓】门禁通行记录表信息
     * @return 【科拓】门禁通行记录表集合
     */
    @Override
    public List<EspDoorOutIntoInfo> selectEspDoorOutIntoInfoList(EspDoorOutIntoInfo espDoorOutIntoInfo) {
        return espDoorOutIntoInfoMapper.selectEspDoorOutIntoInfoList(espDoorOutIntoInfo);
    }


    /**
     * 分页模糊查询【科拓】门禁通行记录表列表
     *
     * @return 【科拓】门禁通行记录表集合
     */
    @Override
    public Page selectEspDoorOutIntoInfoListByLike(Query query) {
        EspDoorOutIntoInfo espDoorOutIntoInfo = BeanUtil.toBean(query.getCondition(), EspDoorOutIntoInfo.class, CopyOptions.create());
        List<EspDoorOutIntoInfo> records = espDoorOutIntoInfoMapper.selectEspDoorOutIntoInfoListByLike(query, espDoorOutIntoInfo);
        for (EspDoorOutIntoInfo outIntoInfo : records) {
            if (StrUtil.isNotBlank(outIntoInfo.getTime()) && outIntoInfo.getTime().length() > 19) {
                outIntoInfo.setTime(outIntoInfo.getTime().substring(0, 19));
            }
        }
        query.setRecords(records);
        return query;
    }

    /**
     * 新增【科拓】门禁通行记录表
     *
     * @param espDoorOutIntoInfo 【科拓】门禁通行记录表信息
     * @return 结果
     */
    @Override
    public int insertEspDoorOutIntoInfo(EspDoorOutIntoInfo espDoorOutIntoInfo) {
        return espDoorOutIntoInfoMapper.insertEspDoorOutIntoInfo(espDoorOutIntoInfo);
    }

    /**
     * 修改【科拓】门禁通行记录表
     *
     * @param espDoorOutIntoInfo 【科拓】门禁通行记录表信息
     * @return 结果
     */
    @Override
    public int updateEspDoorOutIntoInfo(EspDoorOutIntoInfo espDoorOutIntoInfo) {
        return espDoorOutIntoInfoMapper.updateEspDoorOutIntoInfo(espDoorOutIntoInfo);
    }


    /**
     * 删除【科拓】门禁通行记录表
     *
     * @param id 【科拓】门禁通行记录表ID
     * @return 结果
     */
    @Override
    public int deleteEspDoorOutIntoInfoById(String id) {
        return espDoorOutIntoInfoMapper.deleteEspDoorOutIntoInfoById(id);
    }

    /**
     * 批量删除【科拓】门禁通行记录表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteEspDoorOutIntoInfoByIds(Integer[] ids) {
        return espDoorOutIntoInfoMapper.deleteEspDoorOutIntoInfoByIds(ids);
    }

}

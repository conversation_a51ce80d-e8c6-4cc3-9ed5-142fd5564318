package com.huazheng.tunny.smarkpark.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.smarkpark.api.entity.SkBlacklistPlan;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 人员布控计划&分组 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2024-07-18 18:42:09
 */
public interface SkBlacklistPlanService extends IService<SkBlacklistPlan> {
    /**
     * 查询人员布控计划&分组信息
     *
     * @param planIndexCode 人员布控计划&分组ID
     * @return 人员布控计划&分组信息
     */
    public SkBlacklistPlan selectSkBlacklistPlanById(String planIndexCode);

    /**
     * 查询人员布控计划&分组列表
     *
     * @param skBlacklistPlan 人员布控计划&分组信息
     * @return 人员布控计划&分组集合
     */
    public List<SkBlacklistPlan> selectSkBlacklistPlanList(SkBlacklistPlan skBlacklistPlan);


    /**
     * 分页模糊查询人员布控计划&分组列表
     * @return 人员布控计划&分组集合
     */
    public Page selectSkBlacklistPlanListByLike(Query query);



    /**
     * 新增人员布控计划&分组
     *
     * @param skBlacklistPlan 人员布控计划&分组信息
     * @return 结果
     */
    public int insertSkBlacklistPlan(SkBlacklistPlan skBlacklistPlan);

    /**
     * 修改人员布控计划&分组
     *
     * @param skBlacklistPlan 人员布控计划&分组信息
     * @return 结果
     */
    public int updateSkBlacklistPlan(SkBlacklistPlan skBlacklistPlan);

    /**
     * 删除人员布控计划&分组
     *
     * @param planIndexCode 人员布控计划&分组ID
     * @return 结果
     */
    public int deleteSkBlacklistPlanById(String planIndexCode);

    /**
     * 批量删除人员布控计划&分组
     *
     * @param planIndexCodes 需要删除的数据ID
     * @return 结果
     */
    public int deleteSkBlacklistPlanByIds(Integer[] planIndexCodes);

}


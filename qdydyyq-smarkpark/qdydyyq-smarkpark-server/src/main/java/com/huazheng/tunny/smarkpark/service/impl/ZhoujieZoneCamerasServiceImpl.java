package com.huazheng.tunny.smarkpark.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.smarkpark.api.dto.DaHuaCamerasDTO;
import com.huazheng.tunny.smarkpark.api.entity.SkAlarmLog;
import com.huazheng.tunny.smarkpark.mapper.SkAlarmLogMapper;
import com.huazheng.tunny.smarkpark.mapper.ZhoujieZoneCamerasMapper;
import com.huazheng.tunny.smarkpark.api.entity.ZhoujieZoneCameras;
import com.huazheng.tunny.smarkpark.service.ZhoujieZoneCamerasService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Service("zhoujieZoneCamerasService")
public class ZhoujieZoneCamerasServiceImpl extends ServiceImpl<ZhoujieZoneCamerasMapper, ZhoujieZoneCameras> implements ZhoujieZoneCamerasService {

    @Autowired
    private ZhoujieZoneCamerasMapper zhoujieZoneCamerasMapper;
    @Autowired
    private SkAlarmLogMapper skAlarmLogMapper;

    /**
     * 查询周界电子围栏设备防区关联摄像头信息
     *
     * @param id 周界电子围栏设备防区关联摄像头ID
     * @return 周界电子围栏设备防区关联摄像头信息
     */
    @Override
    public ZhoujieZoneCameras selectZhoujieZoneCamerasById(Long id) {
        return zhoujieZoneCamerasMapper.selectZhoujieZoneCamerasById(id);
    }

    /**
     * 查询周界电子围栏设备防区关联摄像头列表
     *
     * @param zhoujieZoneCameras 周界电子围栏设备防区关联摄像头信息
     * @return 周界电子围栏设备防区关联摄像头集合
     */
    @Override
    public List<ZhoujieZoneCameras> selectZhoujieZoneCamerasList(ZhoujieZoneCameras zhoujieZoneCameras) {
        return zhoujieZoneCamerasMapper.selectZhoujieZoneCamerasList(zhoujieZoneCameras);
    }


    /**
     * 分页模糊查询周界电子围栏设备防区关联摄像头列表
     *
     * @return 周界电子围栏设备防区关联摄像头集合
     */
    @Override
    public Page selectZhoujieZoneCamerasListByLike(Query query) {
        DaHuaCamerasDTO zhoujieZoneCameras = BeanUtil.toBean(query.getCondition(), DaHuaCamerasDTO.class, CopyOptions.create());
        if (StrUtil.isNotBlank(zhoujieZoneCameras.getEventId())) {
            SkAlarmLog skAlarmLog = skAlarmLogMapper.selectSkAlarmLogById(zhoujieZoneCameras.getEventId());
            if (skAlarmLog == null) {
                return query;
            }
            String[] ids = skAlarmLog.getSrcIndex().split("-");
            zhoujieZoneCameras.setDeviceId(Integer.parseInt(ids[0]));
            if (ids.length > 1) {
                zhoujieZoneCameras.setZoneId(Integer.parseInt(ids[1]));
            }
        }
        query.setRecords(zhoujieZoneCamerasMapper.selectZhoujieZoneCamerasListByLike(query, zhoujieZoneCameras));
        return query;
    }

    /**
     * 新增周界电子围栏设备防区关联摄像头
     *
     * @param zhoujieZoneCameras 周界电子围栏设备防区关联摄像头信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R insertZhoujieZoneCameras(ZhoujieZoneCameras zhoujieZoneCameras) {
        if (zhoujieZoneCameras == null || zhoujieZoneCameras.getDeviceId() == null
                || zhoujieZoneCameras.getZoneId() == null || StrUtil.isBlank(zhoujieZoneCameras.getCameraCode())) {
            return R.error("数据不全");
        }
        SecruityUser user = SecurityUtils.getUserInfo();
        String[] cameraCodes = zhoujieZoneCameras.getCameraCode().split(",");
        for (String cameraCode : cameraCodes) {
            ZhoujieZoneCameras zoneCameras = new ZhoujieZoneCameras();
            zoneCameras.setZoneId(zhoujieZoneCameras.getZoneId());
            zoneCameras.setDeviceId(zhoujieZoneCameras.getDeviceId());
            zoneCameras.setCameraCode(cameraCode);
            List<ZhoujieZoneCameras> list = zhoujieZoneCamerasMapper.selectZhoujieZoneCamerasList(zoneCameras);
            if (CollUtil.isNotEmpty(list)) {
                continue;
            }
            zoneCameras.setCreateBy(user.getId() + "");
            zoneCameras.setCreateTime(LocalDateTime.now());
            zhoujieZoneCamerasMapper.insertZhoujieZoneCameras(zoneCameras);
        }
        return R.success();
    }

    /**
     * 修改周界电子围栏设备防区关联摄像头
     *
     * @param zhoujieZoneCameras 周界电子围栏设备防区关联摄像头信息
     * @return 结果
     */
    @Override
    public int updateZhoujieZoneCameras(ZhoujieZoneCameras zhoujieZoneCameras) {
        return zhoujieZoneCamerasMapper.updateZhoujieZoneCameras(zhoujieZoneCameras);
    }


    /**
     * 删除周界电子围栏设备防区关联摄像头
     *
     * @param id 周界电子围栏设备防区关联摄像头ID
     * @return 结果
     */
    @Override
    public int deleteZhoujieZoneCamerasById(Long id) {
        return zhoujieZoneCamerasMapper.deleteZhoujieZoneCamerasById(id);
    }


    /**
     * 批量删除周界电子围栏设备防区关联摄像头对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteZhoujieZoneCamerasByIds(Integer[] ids) {
        return zhoujieZoneCamerasMapper.deleteZhoujieZoneCamerasByIds(ids);
    }

}

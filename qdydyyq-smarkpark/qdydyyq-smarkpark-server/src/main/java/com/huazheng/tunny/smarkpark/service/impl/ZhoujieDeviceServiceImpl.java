package com.huazheng.tunny.smarkpark.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.smarkpark.mapper.ZhoujieDeviceMapper;
import com.huazheng.tunny.smarkpark.api.entity.ZhoujieDevice;
import com.huazheng.tunny.smarkpark.service.ZhoujieDeviceService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Service("zhoujieDeviceService")
public class ZhoujieDeviceServiceImpl extends ServiceImpl<ZhoujieDeviceMapper, ZhoujieDevice> implements ZhoujieDeviceService {

    @Autowired
    private ZhoujieDeviceMapper zhoujieDeviceMapper;
    @Value("${tcp.deviceUrl:http://localhost:19991/api/v1/zhoujie/deviceSync}")
    private String deviceUrl;
    /**
     * 查询周界电子围栏设备信息
     *
     * @param id 周界电子围栏设备ID
     * @return 周界电子围栏设备信息
     */
    @Override
    public ZhoujieDevice selectZhoujieDeviceById(Long id) {
        return zhoujieDeviceMapper.selectZhoujieDeviceById(id);
    }

    /**
     * 查询周界电子围栏设备列表
     *
     * @param zhoujieDevice 周界电子围栏设备信息
     * @return 周界电子围栏设备集合
     */
    @Override
    public List<ZhoujieDevice> selectZhoujieDeviceList(ZhoujieDevice zhoujieDevice) {
        return zhoujieDeviceMapper.selectZhoujieDeviceList(zhoujieDevice);
    }


    /**
     * 分页模糊查询周界电子围栏设备列表
     *
     * @return 周界电子围栏设备集合
     */
    @Override
    public Page selectZhoujieDeviceListByLike(Query query) {
        ZhoujieDevice zhoujieDevice = BeanUtil.toBean(query.getCondition(), ZhoujieDevice.class, CopyOptions.create());
        query.setRecords(zhoujieDeviceMapper.selectZhoujieDeviceListByLike(query, zhoujieDevice));
        return query;
    }

    /**
     * 新增周界电子围栏设备
     *
     * @param zhoujieDevice 周界电子围栏设备信息
     * @return 结果
     */
    @Override
    public int insertZhoujieDevice(ZhoujieDevice zhoujieDevice) {
        return zhoujieDeviceMapper.insertZhoujieDevice(zhoujieDevice);
    }

    /**
     * 修改周界电子围栏设备
     *
     * @param zhoujieDevice 周界电子围栏设备信息
     * @return 结果
     */
    @Override
    public int updateZhoujieDevice(ZhoujieDevice zhoujieDevice) {
        return zhoujieDeviceMapper.updateZhoujieDevice(zhoujieDevice);
    }


    /**
     * 删除周界电子围栏设备
     *
     * @param id 周界电子围栏设备ID
     * @return 结果
     */
    @Override
    public int deleteZhoujieDeviceById(Long id) {
        return zhoujieDeviceMapper.deleteZhoujieDeviceById(id);
    }


    /**
     * 批量删除周界电子围栏设备对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteZhoujieDeviceByIds(Integer[] ids) {
        return zhoujieDeviceMapper.deleteZhoujieDeviceByIds(ids);
    }

    @Override
    public R cronDeviceList() {
        try {
            Map<String, Object> paramMap = new HashMap<>();
            String str = HttpRequest.post(deviceUrl).execute().body();
            JSONObject jsonObject = JSONUtil.parseObj(str);
            String successCode = "0";
            String code = jsonObject.getStr("code");
            if (successCode.equals(code)) {
                return R.success();
            }
            return R.error(jsonObject.getStr("msg"));
        } catch (Exception e) {
            e.printStackTrace();
            return R.error("操作失败,失败原因：" + e.getMessage());
        }
    }

}

package com.huazheng.tunny.smarkpark.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.smarkpark.api.entity.ParkingVehicleRecord;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;
import java.util.Map;

/**
 * 停车场-车辆记录 服务接口层
 *
 * <AUTHOR>
 * @date 2025-06-10 13:30:26
 */
public interface ParkingVehicleRecordService extends IService<ParkingVehicleRecord> {
    /**
     * 查询停车场-车辆记录信息
     *
     * @param id 停车场-车辆记录ID
     * @return 停车场-车辆记录信息
     */
    public ParkingVehicleRecord selectParkingVehicleRecordById(String id);

    /**
     * 查询停车场-车辆记录列表
     *
     * @param parkingVehicleRecord 停车场-车辆记录信息
     * @return 停车场-车辆记录集合
     */
    public List<ParkingVehicleRecord> selectParkingVehicleRecordList(ParkingVehicleRecord parkingVehicleRecord);


    /**
     * 分页模糊查询停车场-车辆记录列表
     * @return 停车场-车辆记录集合
     */
    public R selectParkingVehicleRecordListByLike(Query query);



    /**
     * 新增停车场-车辆记录
     *
     * @param parkingVehicleRecord 停车场-车辆记录信息
     * @return 结果
     */
    public Map<String, Object> insertParkingVehicleRecord(Map<String, Object> parkingVehicleRecord);

    /**
     * 修改停车场-车辆记录
     *
     * @param parkingVehicleRecord 停车场-车辆记录信息
     * @return 结果
     */
    public int updateParkingVehicleRecord(ParkingVehicleRecord parkingVehicleRecord);

    /**
     * 删除停车场-车辆记录
     *
     * @param id 停车场-车辆记录ID
     * @return 结果
     */
    public int deleteParkingVehicleRecordById(String id);

    /**
     * 批量删除停车场-车辆记录
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteParkingVehicleRecordByIds(Integer[] ids);

    R selectParkingOutIntoListByLike(Query query);

    void conVehicleRecord(List<ParkingVehicleRecord> list);
}


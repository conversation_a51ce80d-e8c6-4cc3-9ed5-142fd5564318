package com.huazheng.tunny.smarkpark.mqtt;

import lombok.extern.slf4j.Slf4j;

import java.net.*;
import java.util.Enumeration;
import java.util.LinkedList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class IpUtils {
    public static String getIpAddress() {
        String ip = "";
        try {
            ip = InetAddress.getLocalHost().getHostAddress();
            //log.info("ip1:{}", ip);
            if ("127.0.0.1".equals(ip)) {
                List<String> list = getIpAddress1();
                for (String str : list) {
                    //log.info("ip2:{}", str);
                    if (!"127.0.0.1".equals(str)) {
                        ip = str;
                        continue;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        //log.info("ip3:{}", ip);
        return ip;
    }

    public static List<String> getIpAddress1() throws SocketException {
        List<String> list = new LinkedList<>();
        Enumeration enumeration = NetworkInterface.getNetworkInterfaces();
        while (enumeration.hasMoreElements()) {
            NetworkInterface network = (NetworkInterface) enumeration.nextElement();
            if (network.isVirtual() || !network.isUp()) {
                continue;
            } else {
                Enumeration addresses = network.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress address = (InetAddress) addresses.nextElement();
                    if (address != null && (address instanceof Inet4Address || address instanceof Inet6Address)) {
                        if(isIP(address.getHostAddress())){
                            list.add(address.getHostAddress());
                        }
                    }
                }
            }
        }
        return list;
    }

    public static boolean isIP(String addr) {
        if (addr.length() < 7 || addr.length() > 15 || "".equals(addr)) {
            return false;
        }
        /**
         * 判断IP格式和范围
         */
        String rexp = "([1-9]|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])(\\.(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])){3}";
        Pattern pat = Pattern.compile(rexp);
        Matcher mat = pat.matcher(addr);
        boolean ipAddress = mat.find();
        return ipAddress;

    }

}

package com.huazheng.tunny.smarkpark.mapper;

import com.huazheng.tunny.smarkpark.api.entity.SkBuildControlDevice;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.huazheng.tunny.common.core.util.Query;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 楼控设备表  mapper层
 *
 * <AUTHOR>
 * @date 2025-06-04 09:16:07
 */
@Component
public interface SkBuildControlDeviceMapper extends BaseMapper<SkBuildControlDevice> {
    /**
     * 查询楼控设备表信息
     *
     * @param id 楼控设备表ID
     * @return 楼控设备表信息
     */
    public SkBuildControlDevice selectSkBuildControlDeviceById(Long id);

    /**
     * 查询楼控设备表列表
     *
     * @param skBuildControlDevice 楼控设备表信息
     * @return 楼控设备表集合
     */
    public List<SkBuildControlDevice> selectSkBuildControlDeviceList(SkBuildControlDevice skBuildControlDevice);

    /**
     * 模糊查询楼控设备表列表
     *
     * @param skBuildControlDevice 楼控设备表信息
     * @return 楼控设备表集合
     */
    public List<SkBuildControlDevice> selectSkBuildControlDeviceListByLike(SkBuildControlDevice skBuildControlDevice);


    /**
     * 分页模糊查询楼控设备表列表
     *
     * @param skBuildControlDevice 楼控设备表信息
     * @return 楼控设备表集合
     */
    public List<SkBuildControlDevice> selectSkBuildControlDeviceListByLike(Query query, SkBuildControlDevice skBuildControlDevice);


    /**
     * 新增楼控设备表
     *
     * @param skBuildControlDevice 楼控设备表信息
     * @return 结果
     */
    public int insertSkBuildControlDevice(SkBuildControlDevice skBuildControlDevice);

    /**
     * 修改楼控设备表
     *
     * @param skBuildControlDevice 楼控设备表信息
     * @return 结果
     */
    public int updateSkBuildControlDevice(SkBuildControlDevice skBuildControlDevice);

    /**
     * 删除楼控设备表
     *
     * @param id 楼控设备表ID
     * @return 结果
     */
    public int deleteSkBuildControlDeviceById(Long id);

    /**
     * 批量删除楼控设备表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteSkBuildControlDeviceByIds(Integer[] ids);


    SkBuildControlDevice selectSkBuildControlDeviceByCode(String deviceCode);

    List<Map<String, Object>> buildingControlDeviceByType();

    List<Map<String, Object>> buildingControlDeviceState();

    void updateDeviceStatusList(List<SkBuildControlDevice> list);

    void truncateLightingDevice();

    void insertSkBuildControlDeviceList(List<SkBuildControlDevice> skBuildControlDevices);
}

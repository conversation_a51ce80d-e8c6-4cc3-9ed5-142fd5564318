package com.huazheng.tunny.smarkpark.controller;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.huazheng.tunny.common.log.annotation.SysLog;
import com.huazheng.tunny.smarkpark.api.entity.EspDoorOutIntoInfo;
import com.huazheng.tunny.smarkpark.api.entity.SkAlarmLog;
import com.huazheng.tunny.smarkpark.service.EspDoorOutIntoInfoService;
import com.huazheng.tunny.smarkpark.service.SkAlarmLogService;
import com.huazheng.tunny.smarkpark.util.DoorAlarmType;
import com.huazheng.tunny.smarkpark.util.SnowUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

@Slf4j
@RestController
@RequestMapping("/traffic")
public class TrafficController {
    @Resource
    private SkAlarmLogService skAlarmLogService;
    @Resource
    private EspDoorOutIntoInfoService espDoorOutIntoInfoService;

    /**
     * 区域&监控点树
     */
    @SysLog("门禁告警记录上报")
    @PostMapping("/uploadWarnInfo")
    public Map uploadWarnInfo(@RequestBody Map<String, Object> map) {
        log.info("门禁告警记录上报:{}", JSONUtil.toJsonStr(map));
        //{"buildingId":"1","buildingName":"默认大楼","cardReadType":1,"createdTime":"2025-05-29 17:27:18","deviceId":"1921840443969839105","deviceIp":"*************","deviceName":"*************","doorCode":"2","doorId":"1921840444095668226","doorName":"200-门禁2","doorNum":2,"id":"1928020320553062402","logId":"1928020320553062402","port":"80","time":1748510838000,"warnType":"31"}

        SkAlarmLog skAlarmLog = new SkAlarmLog();
        skAlarmLog.setEventId(SnowUtil.nextId() + "");
        skAlarmLog.setAlarmSource(1);
        skAlarmLog.setIpAddress(map.get("deviceIp").toString());
        skAlarmLog.setPortNo(map.get("port").toString());
        skAlarmLog.setSrcIndex(map.get("deviceId").toString());
        skAlarmLog.setSrcName(map.get("deviceName").toString());
        skAlarmLog.setSrcType(map.get("warnType").toString());
        skAlarmLog.setEventType(DoorAlarmType.getEventCodeByCode(map.get("warnType").toString()));
        skAlarmLog.setSendTime(LocalDateTime.now());
        skAlarmLog.setEventContent(map.get("buildingName").toString() + "::" + map.get("cardReadType").toString() + "::" + map.get("doorName").toString() + "::" + DoorAlarmType.getDescriptionByCode(map.get("warnType").toString()));
        skAlarmLog.setHandleStatus("0");
        skAlarmLogService.insertOrUpdateAllColumn(skAlarmLog);
        Map<String, Object> result = new HashMap<>();
        result.put("data", null);
        result.put("resultCode", 200);
        result.put("resultMsg", "操作成功！");
        return result;
    }

    /**
     * 区域&监控点树
     */
    @SysLog("门禁告警记录上报")
    @PostMapping("/uploadAccessInfo")
    public Map uploadAccessInfo(@RequestBody Map<String, Object> map) {
        log.info("门禁告警记录上报:{}", JSONUtil.toJsonStr(map));
        EspDoorOutIntoInfo espDoorOutIntoInfo = JSONUtil.toBean(JSONUtil.toJsonStr(map), EspDoorOutIntoInfo.class);
        List<EspDoorOutIntoInfo> list = new ArrayList();
        list.add(espDoorOutIntoInfo);
        espDoorOutIntoInfoService.insertOrUpdateBatch(list);
        Map<String, Object> result = new HashMap<>();
        result.put("data", null);
        result.put("resultCode", 200);
        result.put("resultMsg", "操作成功！");
        return result;
    }

}

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:33 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>LogbackListAppender</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="LogbackListAppender";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":9,"i6":9,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../com/vip/vjtools/test/log/LogbackListAppenderTest.html" title="com.vip.vjtools.test.log中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/vip/vjtools/test/log/LogbackListAppender.html" target="_top">框架</a></li>
<li><a href="LogbackListAppender.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.ch.qos.logback.core.UnsynchronizedAppenderBase">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.vip.vjtools.test.log</div>
<h2 title="类 LogbackListAppender" class="title">类 LogbackListAppender</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>ch.qos.logback.core.spi.ContextAwareBase</li>
<li>
<ul class="inheritance">
<li>ch.qos.logback.core.UnsynchronizedAppenderBase&lt;ch.qos.logback.classic.spi.ILoggingEvent&gt;</li>
<li>
<ul class="inheritance">
<li>com.vip.vjtools.test.log.LogbackListAppender</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>ch.qos.logback.core.Appender&lt;ch.qos.logback.classic.spi.ILoggingEvent&gt;, ch.qos.logback.core.spi.ContextAware, ch.qos.logback.core.spi.FilterAttachable&lt;ch.qos.logback.classic.spi.ILoggingEvent&gt;, ch.qos.logback.core.spi.LifeCycle</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">LogbackListAppender</span>
extends ch.qos.logback.core.UnsynchronizedAppenderBase&lt;ch.qos.logback.classic.spi.ILoggingEvent&gt;</pre>
<div class="block">在List中保存日志的Appender, 用于测试Logback的日志输出.
 
 在测试开始前, 使用任意一种addToLogger()方法将此appender添加到需要侦听的logger中.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.ch.qos.logback.core.UnsynchronizedAppenderBase">
<!--   -->
</a>
<h3>从类继承的字段&nbsp;ch.qos.logback.core.UnsynchronizedAppenderBase</h3>
<code>name, started</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.ch.qos.logback.core.spi.ContextAwareBase">
<!--   -->
</a>
<h3>从类继承的字段&nbsp;ch.qos.logback.core.spi.ContextAwareBase</h3>
<code>context</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/vip/vjtools/test/log/LogbackListAppender.html#LogbackListAppender--">LogbackListAppender</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/vip/vjtools/test/log/LogbackListAppender.html#addToLogger-java.lang.Class-">addToLogger</a></span>(java.lang.Class&lt;?&gt;&nbsp;loggerClass)</code>
<div class="block">将此appender添加到logger中.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/vip/vjtools/test/log/LogbackListAppender.html#addToLogger-java.lang.String-">addToLogger</a></span>(java.lang.String&nbsp;loggerName)</code>
<div class="block">将此appender添加到logger中.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/vip/vjtools/test/log/LogbackListAppender.html#addToRootLogger--">addToRootLogger</a></span>()</code>
<div class="block">将此appender添加到root logger中.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/vip/vjtools/test/log/LogbackListAppender.html#append-ch.qos.logback.classic.spi.ILoggingEvent-">append</a></span>(ch.qos.logback.classic.spi.ILoggingEvent&nbsp;e)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/vip/vjtools/test/log/LogbackListAppender.html#clearLogs--">clearLogs</a></span>()</code>
<div class="block">清除之前append的所有log.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/vip/vjtools/test/log/LogbackListAppender.html" title="com.vip.vjtools.test.log中的类">LogbackListAppender</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/vip/vjtools/test/log/LogbackListAppender.html#create-java.lang.Class-">create</a></span>(java.lang.Class&lt;?&gt;&nbsp;loggerClass)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/vip/vjtools/test/log/LogbackListAppender.html" title="com.vip.vjtools.test.log中的类">LogbackListAppender</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/vip/vjtools/test/log/LogbackListAppender.html#create-java.lang.String-">create</a></span>(java.lang.String&nbsp;loggerName)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;ch.qos.logback.classic.spi.ILoggingEvent&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/vip/vjtools/test/log/LogbackListAppender.html#getAllLogs--">getAllLogs</a></span>()</code>
<div class="block">返回之前append的所有log.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>ch.qos.logback.classic.spi.ILoggingEvent</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/vip/vjtools/test/log/LogbackListAppender.html#getFirstLog--">getFirstLog</a></span>()</code>
<div class="block">返回之前append的第一个log.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/vip/vjtools/test/log/LogbackListAppender.html#getFirstMessage--">getFirstMessage</a></span>()</code>
<div class="block">返回之前append的第一个log的内容.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>ch.qos.logback.classic.spi.ILoggingEvent</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/vip/vjtools/test/log/LogbackListAppender.html#getLastLog--">getLastLog</a></span>()</code>
<div class="block">返回之前append的最后一个log.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/vip/vjtools/test/log/LogbackListAppender.html#getLastMessage--">getLastMessage</a></span>()</code>
<div class="block">返回之前append的最后一个log的内容.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/vip/vjtools/test/log/LogbackListAppender.html#getLogsCount--">getLogsCount</a></span>()</code>
<div class="block">返回Log的数量。</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/vip/vjtools/test/log/LogbackListAppender.html#isEmpty--">isEmpty</a></span>()</code>
<div class="block">判断是否有log.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/vip/vjtools/test/log/LogbackListAppender.html#removeFromLogger-java.lang.Class-">removeFromLogger</a></span>(java.lang.Class&lt;?&gt;&nbsp;loggerClass)</code>
<div class="block">将此appender从logger中移除.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/vip/vjtools/test/log/LogbackListAppender.html#removeFromLogger-java.lang.String-">removeFromLogger</a></span>(java.lang.String&nbsp;loggerName)</code>
<div class="block">将此appender从logger中移除.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/vip/vjtools/test/log/LogbackListAppender.html#removeFromRootLogger--">removeFromRootLogger</a></span>()</code>
<div class="block">将此appender从root logger中移除.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.ch.qos.logback.core.UnsynchronizedAppenderBase">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;ch.qos.logback.core.UnsynchronizedAppenderBase</h3>
<code>addFilter, clearAllFilters, doAppend, getCopyOfAttachedFiltersList, getFilterChainDecision, getName, isStarted, setName, start, stop, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.ch.qos.logback.core.spi.ContextAwareBase">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;ch.qos.logback.core.spi.ContextAwareBase</h3>
<code>addError, addError, addInfo, addInfo, addStatus, addWarn, addWarn, getContext, getDeclaredOrigin, getStatusManager, setContext</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.ch.qos.logback.core.spi.ContextAware">
<!--   -->
</a>
<h3>从接口继承的方法&nbsp;ch.qos.logback.core.spi.ContextAware</h3>
<code>addError, addError, addInfo, addInfo, addStatus, addWarn, addWarn, getContext, setContext</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="LogbackListAppender--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>LogbackListAppender</h4>
<pre>public&nbsp;LogbackListAppender()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="create-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../../../com/vip/vjtools/test/log/LogbackListAppender.html" title="com.vip.vjtools.test.log中的类">LogbackListAppender</a>&nbsp;create(java.lang.Class&lt;?&gt;&nbsp;loggerClass)</pre>
</li>
</ul>
<a name="create-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../../../com/vip/vjtools/test/log/LogbackListAppender.html" title="com.vip.vjtools.test.log中的类">LogbackListAppender</a>&nbsp;create(java.lang.String&nbsp;loggerName)</pre>
</li>
</ul>
<a name="append-ch.qos.logback.classic.spi.ILoggingEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>append</h4>
<pre>protected&nbsp;void&nbsp;append(ch.qos.logback.classic.spi.ILoggingEvent&nbsp;e)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>append</code>&nbsp;在类中&nbsp;<code>ch.qos.logback.core.UnsynchronizedAppenderBase&lt;ch.qos.logback.classic.spi.ILoggingEvent&gt;</code></dd>
</dl>
</li>
</ul>
<a name="getFirstLog--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFirstLog</h4>
<pre>public&nbsp;ch.qos.logback.classic.spi.ILoggingEvent&nbsp;getFirstLog()</pre>
<div class="block">返回之前append的第一个log.</div>
</li>
</ul>
<a name="getFirstMessage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFirstMessage</h4>
<pre>public&nbsp;java.lang.String&nbsp;getFirstMessage()</pre>
<div class="block">返回之前append的第一个log的内容.</div>
</li>
</ul>
<a name="getLastLog--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLastLog</h4>
<pre>public&nbsp;ch.qos.logback.classic.spi.ILoggingEvent&nbsp;getLastLog()</pre>
<div class="block">返回之前append的最后一个log.</div>
</li>
</ul>
<a name="getLastMessage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLastMessage</h4>
<pre>public&nbsp;java.lang.String&nbsp;getLastMessage()</pre>
<div class="block">返回之前append的最后一个log的内容.</div>
</li>
</ul>
<a name="getAllLogs--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAllLogs</h4>
<pre>public&nbsp;java.util.List&lt;ch.qos.logback.classic.spi.ILoggingEvent&gt;&nbsp;getAllLogs()</pre>
<div class="block">返回之前append的所有log.</div>
</li>
</ul>
<a name="getLogsCount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLogsCount</h4>
<pre>public&nbsp;int&nbsp;getLogsCount()</pre>
<div class="block">返回Log的数量。</div>
</li>
</ul>
<a name="isEmpty--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEmpty</h4>
<pre>public&nbsp;boolean&nbsp;isEmpty()</pre>
<div class="block">判断是否有log.</div>
</li>
</ul>
<a name="clearLogs--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clearLogs</h4>
<pre>public&nbsp;void&nbsp;clearLogs()</pre>
<div class="block">清除之前append的所有log.</div>
</li>
</ul>
<a name="addToLogger-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addToLogger</h4>
<pre>public&nbsp;void&nbsp;addToLogger(java.lang.String&nbsp;loggerName)</pre>
<div class="block">将此appender添加到logger中.</div>
</li>
</ul>
<a name="addToLogger-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addToLogger</h4>
<pre>public&nbsp;void&nbsp;addToLogger(java.lang.Class&lt;?&gt;&nbsp;loggerClass)</pre>
<div class="block">将此appender添加到logger中.</div>
</li>
</ul>
<a name="addToRootLogger--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addToRootLogger</h4>
<pre>public&nbsp;void&nbsp;addToRootLogger()</pre>
<div class="block">将此appender添加到root logger中.</div>
</li>
</ul>
<a name="removeFromLogger-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeFromLogger</h4>
<pre>public&nbsp;void&nbsp;removeFromLogger(java.lang.String&nbsp;loggerName)</pre>
<div class="block">将此appender从logger中移除.</div>
</li>
</ul>
<a name="removeFromLogger-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeFromLogger</h4>
<pre>public&nbsp;void&nbsp;removeFromLogger(java.lang.Class&lt;?&gt;&nbsp;loggerClass)</pre>
<div class="block">将此appender从logger中移除.</div>
</li>
</ul>
<a name="removeFromRootLogger--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>removeFromRootLogger</h4>
<pre>public&nbsp;void&nbsp;removeFromRootLogger()</pre>
<div class="block">将此appender从root logger中移除.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../com/vip/vjtools/test/log/LogbackListAppenderTest.html" title="com.vip.vjtools.test.log中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/vip/vjtools/test/log/LogbackListAppender.html" target="_top">框架</a></li>
<li><a href="LogbackListAppender.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.ch.qos.logback.core.UnsynchronizedAppenderBase">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:35 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>com.huazheng.tunny.tools.base</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.huazheng.tunny.tools.base";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li class="navBarCell1Rev">程序包</li>
<li>类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个程序包</li>
<li><a href="../../../../../com/huazheng/tunny/tools/base/annotation/package-summary.html">下一个程序包</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/base/package-summary.html" target="_top">框架</a></li>
<li><a href="package-summary.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="程序包" class="title">程序包&nbsp;com.huazheng.tunny.tools.base</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="接口概要表, 列表接口和解释">
<caption><span>接口概要</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">接口</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/base/ValueValidator.Validator.html" title="com.huazheng.tunny.tools.base中的接口">ValueValidator.Validator</a>&lt;T&gt;</td>
<td class="colLast">
<div class="block">对Properties值进行规则匹配的验证器</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="类概要表, 列表类和解释">
<caption><span>类概要</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">类</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/base/BeanUtilTest.html" title="com.huazheng.tunny.tools.base中的类">BeanUtilTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/base/BooleanUtil.html" title="com.huazheng.tunny.tools.base中的类">BooleanUtil</a></td>
<td class="colLast">
<div class="block">1.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/base/EnumUtil.html" title="com.huazheng.tunny.tools.base中的类">EnumUtil</a></td>
<td class="colLast">
<div class="block">枚举工具集
 
 1.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/base/EnumUtilTest.html" title="com.huazheng.tunny.tools.base中的类">EnumUtilTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/base/ExceptionUtil.html" title="com.huazheng.tunny.tools.base中的类">ExceptionUtil</a></td>
<td class="colLast">
<div class="block">关于异常的工具类.
 
 1.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/base/ExceptionUtilTest.html" title="com.huazheng.tunny.tools.base中的类">ExceptionUtilTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/base/MoreValidate.html" title="com.huazheng.tunny.tools.base中的类">MoreValidate</a></td>
<td class="colLast">
<div class="block">参数校验统一使用Apache Common Lange Validate, 补充一些缺少的.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/base/MoreValidateTest.html" title="com.huazheng.tunny.tools.base中的类">MoreValidateTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/base/ObjectUtil.html" title="com.huazheng.tunny.tools.base中的类">ObjectUtil</a></td>
<td class="colLast">
<div class="block">1.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/base/ObjectUtilTest.html" title="com.huazheng.tunny.tools.base中的类">ObjectUtilTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/base/PairTest.html" title="com.huazheng.tunny.tools.base中的类">PairTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/base/Platforms.html" title="com.huazheng.tunny.tools.base中的类">Platforms</a></td>
<td class="colLast">
<div class="block">关于系统设定，平台信息的变量(via Common Lang SystemUtils)</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/base/PlatformsTest.html" title="com.huazheng.tunny.tools.base中的类">PlatformsTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/base/PropertiesUtil.html" title="com.huazheng.tunny.tools.base中的类">PropertiesUtil</a></td>
<td class="colLast">
<div class="block">关于Properties的工具类
 
 1.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/base/PropertiesUtilTest.html" title="com.huazheng.tunny.tools.base中的类">PropertiesUtilTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/base/RuntimeUtil.html" title="com.huazheng.tunny.tools.base中的类">RuntimeUtil</a></td>
<td class="colLast">
<div class="block">运行时工具类
 
 1.取得当前进程PID, JVM参数
 
 2.注册JVM关闭钩子, 获得CPU核数
 
 3.通过StackTrace 获得当前方法的类名方法名，调用者的类名方法名(获取StackTrace有消耗，不要滥用)</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/base/RuntimeUtilTest.html" title="com.huazheng.tunny.tools.base中的类">RuntimeUtilTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtil</a></td>
<td class="colLast">
<div class="block">关于SystemProperties的工具类
 
 1.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/base/SystemPropertiesUtil.ListenableProperties.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtil.ListenableProperties</a></td>
<td class="colLast">
<div class="block">Properties 本质上是一个HashTable，每次读写都会加锁，所以不支持频繁的System.getProperty(name)来检查系统内容变化 因此扩展了Properties子类,
 在其所关心的属性变化时进行通知.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/base/SystemPropertiesUtil.PropertiesListener.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtil.PropertiesListener</a></td>
<td class="colLast">
<div class="block">获取所关心的Property变更的Listener基类.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/base/SystemPropertiesUtilTest.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtilTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/base/SystemPropertiesUtilTest.TestPropertiesListener.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtilTest.TestPropertiesListener</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/base/ValueValidator.html" title="com.huazheng.tunny.tools.base中的类">ValueValidator</a></td>
<td class="colLast">
<div class="block">配值较验器 
 
 提供对值进行较验的api，并根据较验结果取值且返回</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/base/ValueValidatorTest.html" title="com.huazheng.tunny.tools.base中的类">ValueValidatorTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="枚举概要表, 列表枚举和解释">
<caption><span>枚举概要</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">枚举</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/base/EnumUtilTest.Options.html" title="com.huazheng.tunny.tools.base中的枚举">EnumUtilTest.Options</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li class="navBarCell1Rev">程序包</li>
<li>类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个程序包</li>
<li><a href="../../../../../com/huazheng/tunny/tools/base/annotation/package-summary.html">下一个程序包</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/base/package-summary.html" target="_top">框架</a></li>
<li><a href="package-summary.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

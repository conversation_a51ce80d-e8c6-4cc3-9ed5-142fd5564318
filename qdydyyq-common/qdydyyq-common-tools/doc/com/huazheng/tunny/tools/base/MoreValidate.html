<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:33 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>MoreValidate</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="MoreValidate";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/base/ExceptionUtilTest.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/base/MoreValidateTest.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/base/MoreValidate.html" target="_top">框架</a></li>
<li><a href="MoreValidate.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.base</div>
<h2 title="类 MoreValidate" class="title">类 MoreValidate</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.base.MoreValidate</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">MoreValidate</span>
extends java.lang.Object</pre>
<div class="block">参数校验统一使用Apache Common Lange Validate, 补充一些缺少的.
 
 为什么不用Guava的<code>com.google.common.base.Preconditions</code> , 一是少打几个字而已, 二是Validate的方法多，比如noNullElements()判断多个元素都不为空
 
 目前主要参考 <code>com.google.common.math.MathPreconditions</code> , 补充数字为正数或非负数的校验</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/MoreValidate.html#MoreValidate--">MoreValidate</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/MoreValidate.html#nonNegative-java.lang.String-double-">nonNegative</a></span>(java.lang.String&nbsp;role,
           double&nbsp;x)</code>
<div class="block">校验为非负数则返回该数字，否则抛出异常.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/MoreValidate.html#nonNegative-java.lang.String-int-">nonNegative</a></span>(java.lang.String&nbsp;role,
           int&nbsp;x)</code>
<div class="block">校验为非负数则返回该数字，否则抛出异常.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/MoreValidate.html#nonNegative-java.lang.String-java.lang.Integer-">nonNegative</a></span>(java.lang.String&nbsp;role,
           java.lang.Integer&nbsp;x)</code>
<div class="block">校验为非负数则返回该数字，否则抛出异常.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/MoreValidate.html#nonNegative-java.lang.String-long-">nonNegative</a></span>(java.lang.String&nbsp;role,
           long&nbsp;x)</code>
<div class="block">校验为非负数则返回该数字，否则抛出异常.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static java.lang.Long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/MoreValidate.html#nonNegative-java.lang.String-java.lang.Long-">nonNegative</a></span>(java.lang.String&nbsp;role,
           java.lang.Long&nbsp;x)</code>
<div class="block">校验为非负数则返回该数字，否则抛出异常.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/MoreValidate.html#positive-java.lang.String-double-">positive</a></span>(java.lang.String&nbsp;role,
        double&nbsp;x)</code>
<div class="block">校验为正数则返回该数字，否则抛出异常.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/MoreValidate.html#positive-java.lang.String-int-">positive</a></span>(java.lang.String&nbsp;role,
        int&nbsp;x)</code>
<div class="block">校验为正数则返回该数字，否则抛出异常.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/MoreValidate.html#positive-java.lang.String-java.lang.Integer-">positive</a></span>(java.lang.String&nbsp;role,
        java.lang.Integer&nbsp;x)</code>
<div class="block">校验为正数则返回该数字，否则抛出异常.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/MoreValidate.html#positive-java.lang.String-long-">positive</a></span>(java.lang.String&nbsp;role,
        long&nbsp;x)</code>
<div class="block">校验为正数则返回该数字，否则抛出异常.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static java.lang.Long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/MoreValidate.html#positive-java.lang.String-java.lang.Long-">positive</a></span>(java.lang.String&nbsp;role,
        java.lang.Long&nbsp;x)</code>
<div class="block">校验为正数则返回该数字，否则抛出异常.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="MoreValidate--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>MoreValidate</h4>
<pre>public&nbsp;MoreValidate()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="positive-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>positive</h4>
<pre>public static&nbsp;int&nbsp;positive(java.lang.String&nbsp;role,
                           int&nbsp;x)</pre>
<div class="block">校验为正数则返回该数字，否则抛出异常.</div>
</li>
</ul>
<a name="positive-java.lang.String-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>positive</h4>
<pre>public static&nbsp;java.lang.Integer&nbsp;positive(java.lang.String&nbsp;role,
                                         java.lang.Integer&nbsp;x)</pre>
<div class="block">校验为正数则返回该数字，否则抛出异常.</div>
</li>
</ul>
<a name="positive-java.lang.String-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>positive</h4>
<pre>public static&nbsp;long&nbsp;positive(java.lang.String&nbsp;role,
                            long&nbsp;x)</pre>
<div class="block">校验为正数则返回该数字，否则抛出异常.</div>
</li>
</ul>
<a name="positive-java.lang.String-java.lang.Long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>positive</h4>
<pre>public static&nbsp;java.lang.Long&nbsp;positive(java.lang.String&nbsp;role,
                                      java.lang.Long&nbsp;x)</pre>
<div class="block">校验为正数则返回该数字，否则抛出异常.</div>
</li>
</ul>
<a name="positive-java.lang.String-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>positive</h4>
<pre>public static&nbsp;double&nbsp;positive(java.lang.String&nbsp;role,
                              double&nbsp;x)</pre>
<div class="block">校验为正数则返回该数字，否则抛出异常.</div>
</li>
</ul>
<a name="nonNegative-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nonNegative</h4>
<pre>public static&nbsp;int&nbsp;nonNegative(java.lang.String&nbsp;role,
                              int&nbsp;x)</pre>
<div class="block">校验为非负数则返回该数字，否则抛出异常.</div>
</li>
</ul>
<a name="nonNegative-java.lang.String-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nonNegative</h4>
<pre>public static&nbsp;java.lang.Integer&nbsp;nonNegative(java.lang.String&nbsp;role,
                                            java.lang.Integer&nbsp;x)</pre>
<div class="block">校验为非负数则返回该数字，否则抛出异常.</div>
</li>
</ul>
<a name="nonNegative-java.lang.String-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nonNegative</h4>
<pre>public static&nbsp;long&nbsp;nonNegative(java.lang.String&nbsp;role,
                               long&nbsp;x)</pre>
<div class="block">校验为非负数则返回该数字，否则抛出异常.</div>
</li>
</ul>
<a name="nonNegative-java.lang.String-java.lang.Long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nonNegative</h4>
<pre>public static&nbsp;java.lang.Long&nbsp;nonNegative(java.lang.String&nbsp;role,
                                         java.lang.Long&nbsp;x)</pre>
<div class="block">校验为非负数则返回该数字，否则抛出异常.</div>
</li>
</ul>
<a name="nonNegative-java.lang.String-double-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>nonNegative</h4>
<pre>public static&nbsp;double&nbsp;nonNegative(java.lang.String&nbsp;role,
                                 double&nbsp;x)</pre>
<div class="block">校验为非负数则返回该数字，否则抛出异常.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/base/ExceptionUtilTest.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/base/MoreValidateTest.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/base/MoreValidate.html" target="_top">框架</a></li>
<li><a href="MoreValidate.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

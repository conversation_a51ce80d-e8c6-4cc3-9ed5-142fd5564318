<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:33 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>SystemPropertiesUtil</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SystemPropertiesUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9,"i14":9,"i15":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/base/RuntimeUtilTest.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/base/SystemPropertiesUtil.ListenableProperties.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/base/SystemPropertiesUtil.html" target="_top">框架</a></li>
<li><a href="SystemPropertiesUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.base</div>
<h2 title="类 SystemPropertiesUtil" class="title">类 SystemPropertiesUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.base.SystemPropertiesUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">SystemPropertiesUtil</span>
extends java.lang.Object</pre>
<div class="block">关于SystemProperties的工具类
 
 1. 统一风格的读取系统变量到各种数据类型，其中Boolean.readBoolean的风格不统一，Double则不支持，都进行了扩展.
 
 2. 简单的合并系统变量(-D)，环境变量 和默认值，以系统变量优先，在未引入Commons Config时使用.
 
 3. Properties 本质上是一个HashTable，每次读写都会加锁，所以不支持频繁的System.getProperty(name)来检查系统内容变化 因此扩展了一个ListenableProperties,
 在其所关心的属性变化时进行通知.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>嵌套类概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="嵌套类概要表, 列表嵌套类和解释">
<caption><span>嵌套类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">类和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/SystemPropertiesUtil.ListenableProperties.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtil.ListenableProperties</a></span></code>
<div class="block">Properties 本质上是一个HashTable，每次读写都会加锁，所以不支持频繁的System.getProperty(name)来检查系统内容变化 因此扩展了Properties子类,
 在其所关心的属性变化时进行通知.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/SystemPropertiesUtil.PropertiesListener.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtil.PropertiesListener</a></span></code>
<div class="block">获取所关心的Property变更的Listener基类.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#SystemPropertiesUtil--">SystemPropertiesUtil</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static java.lang.Boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#getBoolean-java.lang.String-">getBoolean</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">读取Boolean类型的系统变量，为空时返回null，代表未设置，而不是Boolean.getBoolean()的false.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static java.lang.Boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#getBoolean-java.lang.String-java.lang.Boolean-">getBoolean</a></span>(java.lang.String&nbsp;name,
          java.lang.Boolean&nbsp;defaultValue)</code>
<div class="block">读取Boolean类型的系统变量，为空时返回默认值, 而不是Boolean.getBoolean()的false.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static java.lang.Boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#getBoolean-java.lang.String-java.lang.String-java.lang.Boolean-">getBoolean</a></span>(java.lang.String&nbsp;propertyName,
          java.lang.String&nbsp;envName,
          java.lang.Boolean&nbsp;defaultValue)</code>
<div class="block">合并系统变量(-D)，环境变量 和默认值，以系统变量优先</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static java.lang.Double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#getDouble-java.lang.String-">getDouble</a></span>(java.lang.String&nbsp;propertyName)</code>
<div class="block">读取Double类型的系统变量，为空时返回null.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static java.lang.Double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#getDouble-java.lang.String-java.lang.Double-">getDouble</a></span>(java.lang.String&nbsp;propertyName,
         java.lang.Double&nbsp;defaultValue)</code>
<div class="block">读取Double类型的系统变量，为空时返回默认值.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static java.lang.Double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#getDouble-java.lang.String-java.lang.String-java.lang.Double-">getDouble</a></span>(java.lang.String&nbsp;propertyName,
         java.lang.String&nbsp;envName,
         java.lang.Double&nbsp;defaultValue)</code>
<div class="block">合并系统变量(-D)，环境变量 和默认值，以系统变量优先</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#getInteger-java.lang.String-">getInteger</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">读取Integer类型的系统变量，为空时返回null.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#getInteger-java.lang.String-java.lang.Integer-">getInteger</a></span>(java.lang.String&nbsp;name,
          java.lang.Integer&nbsp;defaultValue)</code>
<div class="block">读取Integer类型的系统变量，为空时返回默认值</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#getInteger-java.lang.String-java.lang.String-java.lang.Integer-">getInteger</a></span>(java.lang.String&nbsp;propertyName,
          java.lang.String&nbsp;envName,
          java.lang.Integer&nbsp;defaultValue)</code>
<div class="block">合并系统变量(-D)，环境变量 和默认值，以系统变量优先</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static java.lang.Long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#getLong-java.lang.String-">getLong</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">读取Long类型的系统变量，为空时返回null.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static java.lang.Long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#getLong-java.lang.String-java.lang.Long-">getLong</a></span>(java.lang.String&nbsp;name,
       java.lang.Long&nbsp;defaultValue)</code>
<div class="block">读取Integer类型的系统变量，为空时返回默认值</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static java.lang.Long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#getLong-java.lang.String-java.lang.String-java.lang.Long-">getLong</a></span>(java.lang.String&nbsp;propertyName,
       java.lang.String&nbsp;envName,
       java.lang.Long&nbsp;defaultValue)</code>
<div class="block">合并系统变量(-D)，环境变量 和默认值，以系统变量优先</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#getString-java.lang.String-">getString</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">读取String类型的系统变量，为空时返回null.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#getString-java.lang.String-java.lang.String-">getString</a></span>(java.lang.String&nbsp;name,
         java.lang.String&nbsp;defaultValue)</code>
<div class="block">读取String类型的系统变量，为空时返回默认值</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#getString-java.lang.String-java.lang.String-java.lang.String-">getString</a></span>(java.lang.String&nbsp;propertyName,
         java.lang.String&nbsp;envName,
         java.lang.String&nbsp;defaultValue)</code>
<div class="block">合并系统变量(-D)，环境变量 和默认值，以系统变量优先</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#registerSystemPropertiesListener-com.huazheng.tunny.tools.base.SystemPropertiesUtil.PropertiesListener-">registerSystemPropertiesListener</a></span>(<a href="../../../../../com/huazheng/tunny/tools/base/SystemPropertiesUtil.PropertiesListener.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtil.PropertiesListener</a>&nbsp;listener)</code>
<div class="block">Properties 本质上是一个HashTable，每次读写都会加锁，所以不支持频繁的System.getProperty(name)来检查系统内容变化 因此扩展了一个ListenableProperties,
 在其所关心的属性变化时进行通知.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="SystemPropertiesUtil--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>SystemPropertiesUtil</h4>
<pre>public&nbsp;SystemPropertiesUtil()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getBoolean-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBoolean</h4>
<pre>public static&nbsp;java.lang.Boolean&nbsp;getBoolean(java.lang.String&nbsp;name)</pre>
<div class="block">读取Boolean类型的系统变量，为空时返回null，代表未设置，而不是Boolean.getBoolean()的false.</div>
</li>
</ul>
<a name="getBoolean-java.lang.String-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBoolean</h4>
<pre>public static&nbsp;java.lang.Boolean&nbsp;getBoolean(java.lang.String&nbsp;name,
                                           java.lang.Boolean&nbsp;defaultValue)</pre>
<div class="block">读取Boolean类型的系统变量，为空时返回默认值, 而不是Boolean.getBoolean()的false.</div>
</li>
</ul>
<a name="getString-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getString</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getString(java.lang.String&nbsp;name)</pre>
<div class="block">读取String类型的系统变量，为空时返回null.</div>
</li>
</ul>
<a name="getString-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getString</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getString(java.lang.String&nbsp;name,
                                         java.lang.String&nbsp;defaultValue)</pre>
<div class="block">读取String类型的系统变量，为空时返回默认值</div>
</li>
</ul>
<a name="getInteger-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInteger</h4>
<pre>public static&nbsp;java.lang.Integer&nbsp;getInteger(java.lang.String&nbsp;name)</pre>
<div class="block">读取Integer类型的系统变量，为空时返回null.</div>
</li>
</ul>
<a name="getInteger-java.lang.String-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInteger</h4>
<pre>public static&nbsp;java.lang.Integer&nbsp;getInteger(java.lang.String&nbsp;name,
                                           java.lang.Integer&nbsp;defaultValue)</pre>
<div class="block">读取Integer类型的系统变量，为空时返回默认值</div>
</li>
</ul>
<a name="getLong-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLong</h4>
<pre>public static&nbsp;java.lang.Long&nbsp;getLong(java.lang.String&nbsp;name)</pre>
<div class="block">读取Long类型的系统变量，为空时返回null.</div>
</li>
</ul>
<a name="getLong-java.lang.String-java.lang.Long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLong</h4>
<pre>public static&nbsp;java.lang.Long&nbsp;getLong(java.lang.String&nbsp;name,
                                     java.lang.Long&nbsp;defaultValue)</pre>
<div class="block">读取Integer类型的系统变量，为空时返回默认值</div>
</li>
</ul>
<a name="getDouble-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDouble</h4>
<pre>public static&nbsp;java.lang.Double&nbsp;getDouble(java.lang.String&nbsp;propertyName)</pre>
<div class="block">读取Double类型的系统变量，为空时返回null.</div>
</li>
</ul>
<a name="getDouble-java.lang.String-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDouble</h4>
<pre>public static&nbsp;java.lang.Double&nbsp;getDouble(java.lang.String&nbsp;propertyName,
                                         java.lang.Double&nbsp;defaultValue)</pre>
<div class="block">读取Double类型的系统变量，为空时返回默认值.</div>
</li>
</ul>
<a name="getString-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getString</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getString(java.lang.String&nbsp;propertyName,
                                         java.lang.String&nbsp;envName,
                                         java.lang.String&nbsp;defaultValue)</pre>
<div class="block">合并系统变量(-D)，环境变量 和默认值，以系统变量优先</div>
</li>
</ul>
<a name="getInteger-java.lang.String-java.lang.String-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInteger</h4>
<pre>public static&nbsp;java.lang.Integer&nbsp;getInteger(java.lang.String&nbsp;propertyName,
                                           java.lang.String&nbsp;envName,
                                           java.lang.Integer&nbsp;defaultValue)</pre>
<div class="block">合并系统变量(-D)，环境变量 和默认值，以系统变量优先</div>
</li>
</ul>
<a name="getLong-java.lang.String-java.lang.String-java.lang.Long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLong</h4>
<pre>public static&nbsp;java.lang.Long&nbsp;getLong(java.lang.String&nbsp;propertyName,
                                     java.lang.String&nbsp;envName,
                                     java.lang.Long&nbsp;defaultValue)</pre>
<div class="block">合并系统变量(-D)，环境变量 和默认值，以系统变量优先</div>
</li>
</ul>
<a name="getDouble-java.lang.String-java.lang.String-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDouble</h4>
<pre>public static&nbsp;java.lang.Double&nbsp;getDouble(java.lang.String&nbsp;propertyName,
                                         java.lang.String&nbsp;envName,
                                         java.lang.Double&nbsp;defaultValue)</pre>
<div class="block">合并系统变量(-D)，环境变量 和默认值，以系统变量优先</div>
</li>
</ul>
<a name="getBoolean-java.lang.String-java.lang.String-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBoolean</h4>
<pre>public static&nbsp;java.lang.Boolean&nbsp;getBoolean(java.lang.String&nbsp;propertyName,
                                           java.lang.String&nbsp;envName,
                                           java.lang.Boolean&nbsp;defaultValue)</pre>
<div class="block">合并系统变量(-D)，环境变量 和默认值，以系统变量优先</div>
</li>
</ul>
<a name="registerSystemPropertiesListener-com.huazheng.tunny.tools.base.SystemPropertiesUtil.PropertiesListener-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>registerSystemPropertiesListener</h4>
<pre>public static&nbsp;void&nbsp;registerSystemPropertiesListener(<a href="../../../../../com/huazheng/tunny/tools/base/SystemPropertiesUtil.PropertiesListener.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtil.PropertiesListener</a>&nbsp;listener)</pre>
<div class="block">Properties 本质上是一个HashTable，每次读写都会加锁，所以不支持频繁的System.getProperty(name)来检查系统内容变化 因此扩展了一个ListenableProperties,
 在其所关心的属性变化时进行通知.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/huazheng/tunny/tools/base/SystemPropertiesUtil.ListenableProperties.html" title="com.huazheng.tunny.tools.base中的类"><code>SystemPropertiesUtil.ListenableProperties</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/base/RuntimeUtilTest.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/base/SystemPropertiesUtil.ListenableProperties.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/base/SystemPropertiesUtil.html" target="_top">框架</a></li>
<li><a href="SystemPropertiesUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

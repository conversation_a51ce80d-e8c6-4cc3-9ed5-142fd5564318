<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:33 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Platforms</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Platforms";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/base/PairTest.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/base/PlatformsTest.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/base/Platforms.html" target="_top">框架</a></li>
<li><a href="Platforms.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.java.lang.Object">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li>方法</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.base</div>
<h2 title="类 Platforms" class="title">类 Platforms</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.base.Platforms</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Platforms</span>
extends java.lang.Object</pre>
<div class="block">关于系统设定，平台信息的变量(via Common Lang SystemUtils)</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/Platforms.html#CLASS_PATH_SEPARATOR">CLASS_PATH_SEPARATOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static char</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/Platforms.html#CLASS_PATH_SEPARATOR_CHAR">CLASS_PATH_SEPARATOR_CHAR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/Platforms.html#FILE_PATH_SEPARATOR">FILE_PATH_SEPARATOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static char</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/Platforms.html#FILE_PATH_SEPARATOR_CHAR">FILE_PATH_SEPARATOR_CHAR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/Platforms.html#IS_ATLEASET_JAVA7">IS_ATLEASET_JAVA7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/Platforms.html#IS_ATLEASET_JAVA8">IS_ATLEASET_JAVA8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/Platforms.html#IS_JAVA7">IS_JAVA7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/Platforms.html#IS_JAVA8">IS_JAVA8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/Platforms.html#IS_LINUX">IS_LINUX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/Platforms.html#IS_UNIX">IS_UNIX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/Platforms.html#IS_WINDOWS">IS_WINDOWS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/Platforms.html#JAVA_HOME">JAVA_HOME</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/Platforms.html#JAVA_SPECIFICATION_VERSION">JAVA_SPECIFICATION_VERSION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/Platforms.html#JAVA_VERSION">JAVA_VERSION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/Platforms.html#LINE_SEPARATOR">LINE_SEPARATOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static char</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/Platforms.html#LINUX_FILE_PATH_SEPARATOR_CHAR">LINUX_FILE_PATH_SEPARATOR_CHAR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/Platforms.html#OS_ARCH">OS_ARCH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/Platforms.html#OS_NAME">OS_NAME</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/Platforms.html#OS_VERSION">OS_VERSION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/Platforms.html#TMP_DIR">TMP_DIR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/Platforms.html#USER_HOME">USER_HOME</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static char</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/Platforms.html#WINDOWS_FILE_PATH_SEPARATOR_CHAR">WINDOWS_FILE_PATH_SEPARATOR_CHAR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/Platforms.html#WORKING_DIR">WORKING_DIR</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/Platforms.html#Platforms--">Platforms</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="FILE_PATH_SEPARATOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FILE_PATH_SEPARATOR</h4>
<pre>public static final&nbsp;java.lang.String FILE_PATH_SEPARATOR</pre>
</li>
</ul>
<a name="FILE_PATH_SEPARATOR_CHAR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FILE_PATH_SEPARATOR_CHAR</h4>
<pre>public static final&nbsp;char FILE_PATH_SEPARATOR_CHAR</pre>
</li>
</ul>
<a name="WINDOWS_FILE_PATH_SEPARATOR_CHAR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WINDOWS_FILE_PATH_SEPARATOR_CHAR</h4>
<pre>public static final&nbsp;char WINDOWS_FILE_PATH_SEPARATOR_CHAR</pre>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.huazheng.tunny.tools.base.Platforms.WINDOWS_FILE_PATH_SEPARATOR_CHAR">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="LINUX_FILE_PATH_SEPARATOR_CHAR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LINUX_FILE_PATH_SEPARATOR_CHAR</h4>
<pre>public static final&nbsp;char LINUX_FILE_PATH_SEPARATOR_CHAR</pre>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.huazheng.tunny.tools.base.Platforms.LINUX_FILE_PATH_SEPARATOR_CHAR">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="CLASS_PATH_SEPARATOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CLASS_PATH_SEPARATOR</h4>
<pre>public static final&nbsp;java.lang.String CLASS_PATH_SEPARATOR</pre>
</li>
</ul>
<a name="CLASS_PATH_SEPARATOR_CHAR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CLASS_PATH_SEPARATOR_CHAR</h4>
<pre>public static final&nbsp;char CLASS_PATH_SEPARATOR_CHAR</pre>
</li>
</ul>
<a name="LINE_SEPARATOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LINE_SEPARATOR</h4>
<pre>public static final&nbsp;java.lang.String LINE_SEPARATOR</pre>
</li>
</ul>
<a name="TMP_DIR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TMP_DIR</h4>
<pre>public static final&nbsp;java.lang.String TMP_DIR</pre>
</li>
</ul>
<a name="WORKING_DIR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WORKING_DIR</h4>
<pre>public static final&nbsp;java.lang.String WORKING_DIR</pre>
</li>
</ul>
<a name="USER_HOME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>USER_HOME</h4>
<pre>public static final&nbsp;java.lang.String USER_HOME</pre>
</li>
</ul>
<a name="JAVA_HOME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>JAVA_HOME</h4>
<pre>public static final&nbsp;java.lang.String JAVA_HOME</pre>
</li>
</ul>
<a name="JAVA_SPECIFICATION_VERSION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>JAVA_SPECIFICATION_VERSION</h4>
<pre>public static final&nbsp;java.lang.String JAVA_SPECIFICATION_VERSION</pre>
</li>
</ul>
<a name="JAVA_VERSION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>JAVA_VERSION</h4>
<pre>public static final&nbsp;java.lang.String JAVA_VERSION</pre>
</li>
</ul>
<a name="IS_JAVA7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IS_JAVA7</h4>
<pre>public static final&nbsp;boolean IS_JAVA7</pre>
</li>
</ul>
<a name="IS_JAVA8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IS_JAVA8</h4>
<pre>public static final&nbsp;boolean IS_JAVA8</pre>
</li>
</ul>
<a name="IS_ATLEASET_JAVA7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IS_ATLEASET_JAVA7</h4>
<pre>public static final&nbsp;boolean IS_ATLEASET_JAVA7</pre>
</li>
</ul>
<a name="IS_ATLEASET_JAVA8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IS_ATLEASET_JAVA8</h4>
<pre>public static final&nbsp;boolean IS_ATLEASET_JAVA8</pre>
</li>
</ul>
<a name="OS_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OS_NAME</h4>
<pre>public static final&nbsp;java.lang.String OS_NAME</pre>
</li>
</ul>
<a name="OS_VERSION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OS_VERSION</h4>
<pre>public static final&nbsp;java.lang.String OS_VERSION</pre>
</li>
</ul>
<a name="OS_ARCH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OS_ARCH</h4>
<pre>public static final&nbsp;java.lang.String OS_ARCH</pre>
</li>
</ul>
<a name="IS_LINUX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IS_LINUX</h4>
<pre>public static final&nbsp;boolean IS_LINUX</pre>
</li>
</ul>
<a name="IS_UNIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IS_UNIX</h4>
<pre>public static final&nbsp;boolean IS_UNIX</pre>
</li>
</ul>
<a name="IS_WINDOWS">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>IS_WINDOWS</h4>
<pre>public static final&nbsp;boolean IS_WINDOWS</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="Platforms--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Platforms</h4>
<pre>public&nbsp;Platforms()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/base/PairTest.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/base/PlatformsTest.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/base/Platforms.html" target="_top">框架</a></li>
<li><a href="Platforms.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.java.lang.Object">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li>方法</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

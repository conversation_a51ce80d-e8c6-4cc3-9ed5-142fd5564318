<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:32 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>CloneableRuntimeException</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CloneableRuntimeException";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/huazheng/tunny/tools/base/type/CloneableException.html" title="com.huazheng.tunny.tools.base.type中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../../com/huazheng/tunny/tools/base/type/Pair.html" title="com.huazheng.tunny.tools.base.type中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html" target="_top">框架</a></li>
<li><a href="CloneableRuntimeException.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.base.type</div>
<h2 title="类 CloneableRuntimeException" class="title">类 CloneableRuntimeException</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.lang.Throwable</li>
<li>
<ul class="inheritance">
<li>java.lang.Exception</li>
<li>
<ul class="inheritance">
<li>java.lang.RuntimeException</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.base.type.CloneableRuntimeException</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>java.io.Serializable, java.lang.Cloneable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">CloneableRuntimeException</span>
extends java.lang.RuntimeException
implements java.lang.Cloneable</pre>
<div class="block">适用于异常信息需要变更的情况, 可通过clone()，不经过构造函数（也就避免了获得StackTrace）地从之前定义的静态异常中克隆，再设定新的异常信息</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../../com/huazheng/tunny/tools/base/type/CloneableException.html" title="com.huazheng.tunny.tools.base.type中的类"><code>CloneableException</code></a>, 
<a href="../../../../../../serialized-form.html#com.huazheng.tunny.tools.base.type.CloneableRuntimeException">序列化表格</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html#message">message</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html#CloneableRuntimeException--">CloneableRuntimeException</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html#CloneableRuntimeException-java.lang.String-">CloneableRuntimeException</a></span>(java.lang.String&nbsp;message)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html#CloneableRuntimeException-java.lang.String-java.lang.Throwable-">CloneableRuntimeException</a></span>(java.lang.String&nbsp;message,
                         java.lang.Throwable&nbsp;cause)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html" title="com.huazheng.tunny.tools.base.type中的类">CloneableRuntimeException</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html#clone--">clone</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html" title="com.huazheng.tunny.tools.base.type中的类">CloneableRuntimeException</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html#clone-java.lang.String-">clone</a></span>(java.lang.String&nbsp;message)</code>
<div class="block">简便函数, clone并重新设定Message</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html#getMessage--">getMessage</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html" title="com.huazheng.tunny.tools.base.type中的类">CloneableRuntimeException</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html#setMessage-java.lang.String-">setMessage</a></span>(java.lang.String&nbsp;message)</code>
<div class="block">简便函数, 重新设定Message</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html" title="com.huazheng.tunny.tools.base.type中的类">CloneableRuntimeException</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html#setStackTrace-java.lang.Class-java.lang.String-">setStackTrace</a></span>(java.lang.Class&lt;?&gt;&nbsp;throwClazz,
             java.lang.String&nbsp;throwMethod)</code>
<div class="block">简便函数，定义静态异常时使用</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Throwable">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Throwable</h3>
<code>addSuppressed, fillInStackTrace, getCause, getLocalizedMessage, getStackTrace, getSuppressed, initCause, printStackTrace, printStackTrace, printStackTrace, setStackTrace, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="message">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>message</h4>
<pre>protected&nbsp;java.lang.String message</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="CloneableRuntimeException--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CloneableRuntimeException</h4>
<pre>public&nbsp;CloneableRuntimeException()</pre>
</li>
</ul>
<a name="CloneableRuntimeException-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CloneableRuntimeException</h4>
<pre>public&nbsp;CloneableRuntimeException(java.lang.String&nbsp;message)</pre>
</li>
</ul>
<a name="CloneableRuntimeException-java.lang.String-java.lang.Throwable-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CloneableRuntimeException</h4>
<pre>public&nbsp;CloneableRuntimeException(java.lang.String&nbsp;message,
                                 java.lang.Throwable&nbsp;cause)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="clone--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clone</h4>
<pre>public&nbsp;<a href="../../../../../../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html" title="com.huazheng.tunny.tools.base.type中的类">CloneableRuntimeException</a>&nbsp;clone()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>clone</code>&nbsp;在类中&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
<a name="getMessage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMessage</h4>
<pre>public&nbsp;java.lang.String&nbsp;getMessage()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>getMessage</code>&nbsp;在类中&nbsp;<code>java.lang.Throwable</code></dd>
</dl>
</li>
</ul>
<a name="setStackTrace-java.lang.Class-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStackTrace</h4>
<pre>public&nbsp;<a href="../../../../../../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html" title="com.huazheng.tunny.tools.base.type中的类">CloneableRuntimeException</a>&nbsp;setStackTrace(java.lang.Class&lt;?&gt;&nbsp;throwClazz,
                                               java.lang.String&nbsp;throwMethod)</pre>
<div class="block">简便函数，定义静态异常时使用</div>
</li>
</ul>
<a name="clone-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clone</h4>
<pre>public&nbsp;<a href="../../../../../../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html" title="com.huazheng.tunny.tools.base.type中的类">CloneableRuntimeException</a>&nbsp;clone(java.lang.String&nbsp;message)</pre>
<div class="block">简便函数, clone并重新设定Message</div>
</li>
</ul>
<a name="setMessage-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setMessage</h4>
<pre>public&nbsp;<a href="../../../../../../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html" title="com.huazheng.tunny.tools.base.type中的类">CloneableRuntimeException</a>&nbsp;setMessage(java.lang.String&nbsp;message)</pre>
<div class="block">简便函数, 重新设定Message</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/huazheng/tunny/tools/base/type/CloneableException.html" title="com.huazheng.tunny.tools.base.type中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../../com/huazheng/tunny/tools/base/type/Pair.html" title="com.huazheng.tunny.tools.base.type中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html" target="_top">框架</a></li>
<li><a href="CloneableRuntimeException.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

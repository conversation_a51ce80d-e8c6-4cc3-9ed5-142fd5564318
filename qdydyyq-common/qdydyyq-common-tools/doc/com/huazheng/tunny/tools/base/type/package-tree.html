<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:35 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>com.huazheng.tunny.tools.base.type 类分层结构</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.huazheng.tunny.tools.base.type \u7C7B\u5206\u5C42\u7ED3\u6784";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li class="navBarCell1Rev">树</li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/huazheng/tunny/tools/base/annotation/package-tree.html">上一个</a></li>
<li><a href="../../../../../../com/huazheng/tunny/tools/collection/package-tree.html">下一个</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/huazheng/tunny/tools/base/type/package-tree.html" target="_top">框架</a></li>
<li><a href="package-tree.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 class="title">程序包com.huazheng.tunny.tools.base.type的分层结构</h1>
<span class="packageHierarchyLabel">程序包分层结构:</span>
<ul class="horizontal">
<li><a href="../../../../../../overview-tree.html">所有程序包</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="类分层结构">类分层结构</h2>
<ul>
<li type="circle">java.lang.Object
<ul>
<li type="circle">com.huazheng.tunny.tools.base.type.<a href="../../../../../../com/huazheng/tunny/tools/base/type/Pair.html" title="com.huazheng.tunny.tools.base.type中的类"><span class="typeNameLink">Pair</span></a>&lt;L,R&gt;</li>
<li type="circle">java.lang.Throwable (implements java.io.Serializable)
<ul>
<li type="circle">java.lang.Exception
<ul>
<li type="circle">com.huazheng.tunny.tools.base.type.<a href="../../../../../../com/huazheng/tunny/tools/base/type/CloneableException.html" title="com.huazheng.tunny.tools.base.type中的类"><span class="typeNameLink">CloneableException</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">java.lang.RuntimeException
<ul>
<li type="circle">com.huazheng.tunny.tools.base.type.<a href="../../../../../../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html" title="com.huazheng.tunny.tools.base.type中的类"><span class="typeNameLink">CloneableRuntimeException</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.huazheng.tunny.tools.base.type.<a href="../../../../../../com/huazheng/tunny/tools/base/type/UncheckedException.html" title="com.huazheng.tunny.tools.base.type中的类"><span class="typeNameLink">UncheckedException</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.huazheng.tunny.tools.base.type.<a href="../../../../../../com/huazheng/tunny/tools/base/type/Triple.html" title="com.huazheng.tunny.tools.base.type中的类"><span class="typeNameLink">Triple</span></a>&lt;L,M,R&gt;</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li class="navBarCell1Rev">树</li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/huazheng/tunny/tools/base/annotation/package-tree.html">上一个</a></li>
<li><a href="../../../../../../com/huazheng/tunny/tools/collection/package-tree.html">下一个</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/huazheng/tunny/tools/base/type/package-tree.html" target="_top">框架</a></li>
<li><a href="package-tree.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:33 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>PropertiesUtil</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="PropertiesUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/base/PlatformsTest.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/base/PropertiesUtilTest.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/base/PropertiesUtil.html" target="_top">框架</a></li>
<li><a href="PropertiesUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.base</div>
<h2 title="类 PropertiesUtil" class="title">类 PropertiesUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.base.PropertiesUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">PropertiesUtil</span>
extends java.lang.Object</pre>
<div class="block">关于Properties的工具类
 
 1. 统一风格读取Properties到各种数据类型
 
 2. 从文件或字符串装载Properties</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/PropertiesUtil.html#PropertiesUtil--">PropertiesUtil</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static java.lang.Boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/PropertiesUtil.html#getBoolean-java.util.Properties-java.lang.String-java.lang.Boolean-">getBoolean</a></span>(java.util.Properties&nbsp;p,
          java.lang.String&nbsp;name,
          java.lang.Boolean&nbsp;defaultValue)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static java.lang.Double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/PropertiesUtil.html#getDouble-java.util.Properties-java.lang.String-java.lang.Double-">getDouble</a></span>(java.util.Properties&nbsp;p,
         java.lang.String&nbsp;name,
         java.lang.Double&nbsp;defaultValue)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/PropertiesUtil.html#getInt-java.util.Properties-java.lang.String-java.lang.Integer-">getInt</a></span>(java.util.Properties&nbsp;p,
      java.lang.String&nbsp;name,
      java.lang.Integer&nbsp;defaultValue)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static java.lang.Long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/PropertiesUtil.html#getLong-java.util.Properties-java.lang.String-java.lang.Long-">getLong</a></span>(java.util.Properties&nbsp;p,
       java.lang.String&nbsp;name,
       java.lang.Long&nbsp;defaultValue)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/PropertiesUtil.html#getString-java.util.Properties-java.lang.String-java.lang.String-">getString</a></span>(java.util.Properties&nbsp;p,
         java.lang.String&nbsp;name,
         java.lang.String&nbsp;defaultValue)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static java.util.Properties</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/PropertiesUtil.html#loadFromFile-java.lang.String-">loadFromFile</a></span>(java.lang.String&nbsp;generalPath)</code>
<div class="block">从文件路径加载properties.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static java.util.Properties</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/PropertiesUtil.html#loadFromString-java.lang.String-">loadFromString</a></span>(java.lang.String&nbsp;content)</code>
<div class="block">从字符串内容加载Properties</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="PropertiesUtil--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PropertiesUtil</h4>
<pre>public&nbsp;PropertiesUtil()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getBoolean-java.util.Properties-java.lang.String-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBoolean</h4>
<pre>public static&nbsp;java.lang.Boolean&nbsp;getBoolean(java.util.Properties&nbsp;p,
                                           java.lang.String&nbsp;name,
                                           java.lang.Boolean&nbsp;defaultValue)</pre>
</li>
</ul>
<a name="getInt-java.util.Properties-java.lang.String-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInt</h4>
<pre>public static&nbsp;java.lang.Integer&nbsp;getInt(java.util.Properties&nbsp;p,
                                       java.lang.String&nbsp;name,
                                       java.lang.Integer&nbsp;defaultValue)</pre>
</li>
</ul>
<a name="getLong-java.util.Properties-java.lang.String-java.lang.Long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLong</h4>
<pre>public static&nbsp;java.lang.Long&nbsp;getLong(java.util.Properties&nbsp;p,
                                     java.lang.String&nbsp;name,
                                     java.lang.Long&nbsp;defaultValue)</pre>
</li>
</ul>
<a name="getDouble-java.util.Properties-java.lang.String-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDouble</h4>
<pre>public static&nbsp;java.lang.Double&nbsp;getDouble(java.util.Properties&nbsp;p,
                                         java.lang.String&nbsp;name,
                                         java.lang.Double&nbsp;defaultValue)</pre>
</li>
</ul>
<a name="getString-java.util.Properties-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getString</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getString(java.util.Properties&nbsp;p,
                                         java.lang.String&nbsp;name,
                                         java.lang.String&nbsp;defaultValue)</pre>
</li>
</ul>
<a name="loadFromFile-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>loadFromFile</h4>
<pre>public static&nbsp;java.util.Properties&nbsp;loadFromFile(java.lang.String&nbsp;generalPath)</pre>
<div class="block">从文件路径加载properties. 默认使用utf-8编码解析文件
 
 路径支持从外部文件或resources文件加载, "file://"或无前缀代表外部文件, "classpath:"代表resources</div>
</li>
</ul>
<a name="loadFromString-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>loadFromString</h4>
<pre>public static&nbsp;java.util.Properties&nbsp;loadFromString(java.lang.String&nbsp;content)</pre>
<div class="block">从字符串内容加载Properties</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/base/PlatformsTest.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/base/PropertiesUtilTest.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/base/PropertiesUtil.html" target="_top">框架</a></li>
<li><a href="PropertiesUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

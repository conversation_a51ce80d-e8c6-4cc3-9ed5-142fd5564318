<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:33 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ValueValidator.Validator</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ValueValidator.Validator";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/base/ValueValidator.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/base/ValueValidatorTest.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/base/ValueValidator.Validator.html" target="_top">框架</a></li>
<li><a href="ValueValidator.Validator.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.base</div>
<h2 title="接口 ValueValidator.Validator" class="title">接口 ValueValidator.Validator&lt;T&gt;</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>封闭类:</dt>
<dd><a href="../../../../../com/huazheng/tunny/tools/base/ValueValidator.html" title="com.huazheng.tunny.tools.base中的类">ValueValidator</a></dd>
</dl>
<hr>
<br>
<pre>public static interface <span class="typeNameLabel">ValueValidator.Validator&lt;T&gt;</span></pre>
<div class="block">对Properties值进行规则匹配的验证器</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/huazheng/tunny/tools/base/ValueValidator.Validator.html" title="com.huazheng.tunny.tools.base中的接口">ValueValidator.Validator</a>&lt;java.lang.Integer&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/ValueValidator.Validator.html#INTEGER_GT_ZERO_VALIDATOR">INTEGER_GT_ZERO_VALIDATOR</a></span></code>
<div class="block">校验器: 数值配置不为null, 且大于0较验</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/huazheng/tunny/tools/base/ValueValidator.Validator.html" title="com.huazheng.tunny.tools.base中的接口">ValueValidator.Validator</a>&lt;java.lang.String&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/ValueValidator.Validator.html#STRICT_BOOL_VALUE_VALIDATOR">STRICT_BOOL_VALUE_VALIDATOR</a></span></code>
<div class="block">校验器: BOOL字符串较验</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/huazheng/tunny/tools/base/ValueValidator.Validator.html" title="com.huazheng.tunny.tools.base中的接口">ValueValidator.Validator</a>&lt;java.lang.String&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/ValueValidator.Validator.html#STRING_EMPTY_VALUE_VALIDATOR">STRING_EMPTY_VALUE_VALIDATOR</a></span></code>
<div class="block">校验器: 字符串不为空串较验</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/ValueValidator.Validator.html#validate-T-">validate</a></span>(<a href="../../../../../com/huazheng/tunny/tools/base/ValueValidator.Validator.html" title="ValueValidator.Validator中的类型参数">T</a>&nbsp;value)</code>
<div class="block">校验值是否匹配</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="INTEGER_GT_ZERO_VALIDATOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INTEGER_GT_ZERO_VALIDATOR</h4>
<pre>static final&nbsp;<a href="../../../../../com/huazheng/tunny/tools/base/ValueValidator.Validator.html" title="com.huazheng.tunny.tools.base中的接口">ValueValidator.Validator</a>&lt;java.lang.Integer&gt; INTEGER_GT_ZERO_VALIDATOR</pre>
<div class="block">校验器: 数值配置不为null, 且大于0较验</div>
</li>
</ul>
<a name="STRING_EMPTY_VALUE_VALIDATOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STRING_EMPTY_VALUE_VALIDATOR</h4>
<pre>static final&nbsp;<a href="../../../../../com/huazheng/tunny/tools/base/ValueValidator.Validator.html" title="com.huazheng.tunny.tools.base中的接口">ValueValidator.Validator</a>&lt;java.lang.String&gt; STRING_EMPTY_VALUE_VALIDATOR</pre>
<div class="block">校验器: 字符串不为空串较验</div>
</li>
</ul>
<a name="STRICT_BOOL_VALUE_VALIDATOR">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>STRICT_BOOL_VALUE_VALIDATOR</h4>
<pre>static final&nbsp;<a href="../../../../../com/huazheng/tunny/tools/base/ValueValidator.Validator.html" title="com.huazheng.tunny.tools.base中的接口">ValueValidator.Validator</a>&lt;java.lang.String&gt; STRICT_BOOL_VALUE_VALIDATOR</pre>
<div class="block">校验器: BOOL字符串较验</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="validate-java.lang.Object-">
<!--   -->
</a><a name="validate-T-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>validate</h4>
<pre>boolean&nbsp;validate(<a href="../../../../../com/huazheng/tunny/tools/base/ValueValidator.Validator.html" title="ValueValidator.Validator中的类型参数">T</a>&nbsp;value)</pre>
<div class="block">校验值是否匹配</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/base/ValueValidator.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/base/ValueValidatorTest.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/base/ValueValidator.Validator.html" target="_top">框架</a></li>
<li><a href="ValueValidator.Validator.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

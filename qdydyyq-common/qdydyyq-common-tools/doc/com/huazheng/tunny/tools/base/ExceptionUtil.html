<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:33 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ExceptionUtil</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ExceptionUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/base/EnumUtilTest.Options.html" title="com.huazheng.tunny.tools.base中的枚举"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/base/ExceptionUtilTest.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/base/ExceptionUtil.html" target="_top">框架</a></li>
<li><a href="ExceptionUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.base</div>
<h2 title="类 ExceptionUtil" class="title">类 ExceptionUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.base.ExceptionUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ExceptionUtil</span>
extends java.lang.Object</pre>
<div class="block">关于异常的工具类.
 
 1. Checked/Uncheked及Wrap(如ExecutionException)的转换.
 
 2. 打印Exception的辅助函数. (其中一些来自Common Lang ExceptionUtils)
 
 3. 查找Cause(其中一些来自Guava Throwables)
 
 4. StackTrace性能优化相关，尽量使用静态异常避免异常生成时获取StackTrace(Netty)</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/huazheng/tunny/tools/base/type/CloneableException.html" title="com.huazheng.tunny.tools.base.type中的类"><code>CloneableException</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/ExceptionUtil.html#ExceptionUtil--">ExceptionUtil</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static &lt;T extends java.lang.Throwable&gt;<br>T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/ExceptionUtil.html#clearStackTrace-T-">clearStackTrace</a></span>(T&nbsp;throwable)</code>
<div class="block">清除StackTrace.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static &lt;T extends java.lang.Throwable&gt;<br>T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/ExceptionUtil.html#findCause-java.lang.Throwable-java.lang.Class-">findCause</a></span>(java.lang.Throwable&nbsp;throwable,
         java.lang.Class&lt;T&gt;&nbsp;cause)</code>
<div class="block">获取某种类型的cause，如果没有则返回空
 
 copy from Jodd ExceptionUtil</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static java.lang.Throwable</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/ExceptionUtil.html#getRootCause-java.lang.Throwable-">getRootCause</a></span>(java.lang.Throwable&nbsp;t)</code>
<div class="block">获取异常的Root Cause.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/ExceptionUtil.html#isCausedBy-java.lang.Throwable-java.lang.Class...-">isCausedBy</a></span>(java.lang.Throwable&nbsp;throwable,
          java.lang.Class&lt;? extends java.lang.Exception&gt;...&nbsp;causeExceptionClasses)</code>
<div class="block">判断异常是否由某些底层的异常引起.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static &lt;T extends java.lang.Throwable&gt;<br>T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/ExceptionUtil.html#setStackTrace-T-java.lang.Class-java.lang.String-">setStackTrace</a></span>(T&nbsp;throwable,
             java.lang.Class&lt;?&gt;&nbsp;throwClass,
             java.lang.String&nbsp;throwClazz)</code>
<div class="block">copy from Netty, 为静态异常设置StackTrace.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/ExceptionUtil.html#stackTraceText-java.lang.Throwable-">stackTraceText</a></span>(java.lang.Throwable&nbsp;t)</code>
<div class="block">将StackTrace[]转换为String, 供Logger或e.printStackTrace()外的其他地方使用.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/ExceptionUtil.html#toStringWithRootCause-java.lang.Throwable-">toStringWithRootCause</a></span>(java.lang.Throwable&nbsp;t)</code>
<div class="block">拼装 短异常类名: 异常信息 <-- RootCause的短异常类名: 异常信息</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/ExceptionUtil.html#toStringWithShortName-java.lang.Throwable-">toStringWithShortName</a></span>(java.lang.Throwable&nbsp;t)</code>
<div class="block">拼装 短异常类名: 异常信息.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static java.lang.RuntimeException</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/ExceptionUtil.html#unchecked-java.lang.Throwable-">unchecked</a></span>(java.lang.Throwable&nbsp;t)</code>
<div class="block">将CheckedException转换为RuntimeException重新抛出, 可以减少函数签名中的CheckExcetpion定义.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static java.lang.Throwable</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/ExceptionUtil.html#unwrap-java.lang.Throwable-">unwrap</a></span>(java.lang.Throwable&nbsp;t)</code>
<div class="block">如果是著名的包裹类，从cause中获得真正异常.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static java.lang.RuntimeException</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/base/ExceptionUtil.html#unwrapAndUnchecked-java.lang.Throwable-">unwrapAndUnchecked</a></span>(java.lang.Throwable&nbsp;t)</code>
<div class="block">组合unwrap与unchecked，用于处理反射/Callable的异常</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="ExceptionUtil--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ExceptionUtil</h4>
<pre>public&nbsp;ExceptionUtil()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="unchecked-java.lang.Throwable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unchecked</h4>
<pre>public static&nbsp;java.lang.RuntimeException&nbsp;unchecked(java.lang.Throwable&nbsp;t)</pre>
<div class="block">将CheckedException转换为RuntimeException重新抛出, 可以减少函数签名中的CheckExcetpion定义.
 
 CheckedException会用UndeclaredThrowableException包裹，RunTimeException和Error则不会被转变.
 
 copy from Commons Lange 3.5 ExceptionUtils.
 
 虽然unchecked()里已直接抛出异常，但仍然定义返回值，方便欺骗Sonar。因此本函数也改变了一下返回值
 
 示例代码:
 
 <pre>
 try{ ... }catch(Exception e){ throw unchecked(t); }
 </pre></div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>ExceptionUtils.wrapAndThrow(Throwable)</code></dd>
</dl>
</li>
</ul>
<a name="unwrap-java.lang.Throwable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unwrap</h4>
<pre>public static&nbsp;java.lang.Throwable&nbsp;unwrap(java.lang.Throwable&nbsp;t)</pre>
<div class="block">如果是著名的包裹类，从cause中获得真正异常. 其他异常则不变.
 
 Future中使用的ExecutionException 与 反射时定义的InvocationTargetException， 真正的异常都封装在Cause中
 
 前面 unchecked() 使用的UncheckedException同理.</div>
</li>
</ul>
<a name="unwrapAndUnchecked-java.lang.Throwable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unwrapAndUnchecked</h4>
<pre>public static&nbsp;java.lang.RuntimeException&nbsp;unwrapAndUnchecked(java.lang.Throwable&nbsp;t)</pre>
<div class="block">组合unwrap与unchecked，用于处理反射/Callable的异常</div>
</li>
</ul>
<a name="stackTraceText-java.lang.Throwable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stackTraceText</h4>
<pre>public static&nbsp;java.lang.String&nbsp;stackTraceText(java.lang.Throwable&nbsp;t)</pre>
<div class="block">将StackTrace[]转换为String, 供Logger或e.printStackTrace()外的其他地方使用.
 
 为了使用StringBuilderWriter，没有用Throwables#getStackTraceAsString(Throwable)</div>
</li>
</ul>
<a name="toStringWithShortName-java.lang.Throwable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toStringWithShortName</h4>
<pre>public static&nbsp;java.lang.String&nbsp;toStringWithShortName(java.lang.Throwable&nbsp;t)</pre>
<div class="block">拼装 短异常类名: 异常信息.
 
 与Throwable.toString()相比使用了短类名</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>ExceptionUtils.getMessage(Throwable)</code></dd>
</dl>
</li>
</ul>
<a name="toStringWithRootCause-java.lang.Throwable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toStringWithRootCause</h4>
<pre>public static&nbsp;java.lang.String&nbsp;toStringWithRootCause(java.lang.Throwable&nbsp;t)</pre>
<div class="block">拼装 短异常类名: 异常信息 <-- RootCause的短异常类名: 异常信息</div>
</li>
</ul>
<a name="getRootCause-java.lang.Throwable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRootCause</h4>
<pre>public static&nbsp;java.lang.Throwable&nbsp;getRootCause(java.lang.Throwable&nbsp;t)</pre>
<div class="block">获取异常的Root Cause.
 
 如无底层Cause, 则返回自身</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Throwables.getRootCause(Throwable)</code></dd>
</dl>
</li>
</ul>
<a name="findCause-java.lang.Throwable-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findCause</h4>
<pre>public static&nbsp;&lt;T extends java.lang.Throwable&gt;&nbsp;T&nbsp;findCause(java.lang.Throwable&nbsp;throwable,
                                                          java.lang.Class&lt;T&gt;&nbsp;cause)</pre>
<div class="block">获取某种类型的cause，如果没有则返回空
 
 copy from Jodd ExceptionUtil</div>
</li>
</ul>
<a name="isCausedBy-java.lang.Throwable-java.lang.Class...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isCausedBy</h4>
<pre>public static&nbsp;boolean&nbsp;isCausedBy(java.lang.Throwable&nbsp;throwable,
                                 java.lang.Class&lt;? extends java.lang.Exception&gt;...&nbsp;causeExceptionClasses)</pre>
<div class="block">判断异常是否由某些底层的异常引起.</div>
</li>
</ul>
<a name="setStackTrace-java.lang.Throwable-java.lang.Class-java.lang.String-">
<!--   -->
</a><a name="setStackTrace-T-java.lang.Class-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStackTrace</h4>
<pre>public static&nbsp;&lt;T extends java.lang.Throwable&gt;&nbsp;T&nbsp;setStackTrace(T&nbsp;throwable,
                                                              java.lang.Class&lt;?&gt;&nbsp;throwClass,
                                                              java.lang.String&nbsp;throwClazz)</pre>
<div class="block">copy from Netty, 为静态异常设置StackTrace.
 
 对某些已知且经常抛出的异常, 不需要每次创建异常类并很消耗性能的并生成完整的StackTrace. 此时可使用静态声明的异常.
 
 如果异常可能在多个地方抛出，使用本函数设置抛出的类名和方法名.
 
 <pre>
 private static RuntimeException TIMEOUT_EXCEPTION = ExceptionUtil.setStackTrace(new RuntimeException("Timeout"),
                MyClass.class, "mymethod");
 </pre></div>
</li>
</ul>
<a name="clearStackTrace-java.lang.Throwable-">
<!--   -->
</a><a name="clearStackTrace-T-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>clearStackTrace</h4>
<pre>public static&nbsp;&lt;T extends java.lang.Throwable&gt;&nbsp;T&nbsp;clearStackTrace(T&nbsp;throwable)</pre>
<div class="block">清除StackTrace. 假设StackTrace已生成, 但把它打印出来也有不小的消耗.
 
 如果不能控制StackTrace的生成，也不能控制它的打印端(如logger)，可用此方法暴力清除Trace.
 
 但Cause链依然不能清除, 只能清除每一个Cause的StackTrace.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/base/EnumUtilTest.Options.html" title="com.huazheng.tunny.tools.base中的枚举"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/base/ExceptionUtilTest.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/base/ExceptionUtil.html" target="_top">框架</a></li>
<li><a href="ExceptionUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

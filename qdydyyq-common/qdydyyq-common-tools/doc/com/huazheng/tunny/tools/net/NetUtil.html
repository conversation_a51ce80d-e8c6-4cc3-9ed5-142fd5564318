<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:32 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>NetUtil</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="NetUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/net/IPUtilTest.html" title="com.huazheng.tunny.tools.net中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/net/NetUtilTest.html" title="com.huazheng.tunny.tools.net中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/net/NetUtil.html" target="_top">框架</a></li>
<li><a href="NetUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.net</div>
<h2 title="类 NetUtil" class="title">类 NetUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.net.NetUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>@Beta
public class <span class="typeNameLabel">NetUtil</span>
extends java.lang.Object</pre>
<div class="block">关于网络的工具类.
 
 1. 获取本机IP地址与HostName
 
 2. 查找空闲端口</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/net/NetUtil.html#PORT_RANGE_MAX">PORT_RANGE_MAX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/net/NetUtil.html#PORT_RANGE_MIN">PORT_RANGE_MIN</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/net/NetUtil.html#NetUtil--">NetUtil</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/net/NetUtil.html#findAvailablePortFrom-int-">findAvailablePortFrom</a></span>(int&nbsp;minPort)</code>
<div class="block">从某个端口开始，递增直到65535，找一个空闲端口.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/net/NetUtil.html#findRandomAvailablePort--">findRandomAvailablePort</a></span>()</code>
<div class="block">从1024到65535， 随机找一个空闲端口 from Spring SocketUtils</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/net/NetUtil.html#findRandomAvailablePort-int-int-">findRandomAvailablePort</a></span>(int&nbsp;minPort,
                       int&nbsp;maxPort)</code>
<div class="block">在范围里随机找一个空闲端口,from Spring SocketUtils.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/net/NetUtil.html#getHostName--">getHostName</a></span>()</code>
<div class="block">获得本地HostName</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static java.net.InetAddress</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/net/NetUtil.html#getLocalAddress--">getLocalAddress</a></span>()</code>
<div class="block">获得本地地址</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/net/NetUtil.html#getLocalHost--">getLocalHost</a></span>()</code>
<div class="block">获得本地Ip地址</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/net/NetUtil.html#isPortAvailable-int-">isPortAvailable</a></span>(int&nbsp;port)</code>
<div class="block">测试端口是否空闲可用, from Spring SocketUtils</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="PORT_RANGE_MIN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PORT_RANGE_MIN</h4>
<pre>public static final&nbsp;int PORT_RANGE_MIN</pre>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.huazheng.tunny.tools.net.NetUtil.PORT_RANGE_MIN">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="PORT_RANGE_MAX">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PORT_RANGE_MAX</h4>
<pre>public static final&nbsp;int PORT_RANGE_MAX</pre>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.huazheng.tunny.tools.net.NetUtil.PORT_RANGE_MAX">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="NetUtil--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>NetUtil</h4>
<pre>public&nbsp;NetUtil()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getLocalAddress--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLocalAddress</h4>
<pre>public static&nbsp;java.net.InetAddress&nbsp;getLocalAddress()</pre>
<div class="block">获得本地地址</div>
</li>
</ul>
<a name="getLocalHost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLocalHost</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getLocalHost()</pre>
<div class="block">获得本地Ip地址</div>
</li>
</ul>
<a name="getHostName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHostName</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getHostName()</pre>
<div class="block">获得本地HostName</div>
</li>
</ul>
<a name="isPortAvailable-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPortAvailable</h4>
<pre>public static&nbsp;boolean&nbsp;isPortAvailable(int&nbsp;port)</pre>
<div class="block">测试端口是否空闲可用, from Spring SocketUtils</div>
</li>
</ul>
<a name="findRandomAvailablePort--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findRandomAvailablePort</h4>
<pre>public static&nbsp;int&nbsp;findRandomAvailablePort()</pre>
<div class="block">从1024到65535， 随机找一个空闲端口 from Spring SocketUtils</div>
</li>
</ul>
<a name="findRandomAvailablePort-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findRandomAvailablePort</h4>
<pre>public static&nbsp;int&nbsp;findRandomAvailablePort(int&nbsp;minPort,
                                          int&nbsp;maxPort)</pre>
<div class="block">在范围里随机找一个空闲端口,from Spring SocketUtils.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - 最多尝试(maxPort-minPort)次，如无空闲端口，抛出此异常.</dd>
</dl>
</li>
</ul>
<a name="findAvailablePortFrom-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>findAvailablePortFrom</h4>
<pre>public static&nbsp;int&nbsp;findAvailablePortFrom(int&nbsp;minPort)</pre>
<div class="block">从某个端口开始，递增直到65535，找一个空闲端口.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - 范围内如无空闲端口，抛出此异常</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/net/IPUtilTest.html" title="com.huazheng.tunny.tools.net中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/net/NetUtilTest.html" title="com.huazheng.tunny.tools.net中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/net/NetUtil.html" target="_top">框架</a></li>
<li><a href="NetUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

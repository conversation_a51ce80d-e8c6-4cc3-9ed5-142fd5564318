<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:32 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>IPUtil</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IPUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../com/huazheng/tunny/tools/net/IPUtilTest.html" title="com.huazheng.tunny.tools.net中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/net/IPUtil.html" target="_top">框架</a></li>
<li><a href="IPUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.net</div>
<h2 title="类 IPUtil" class="title">类 IPUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.net.IPUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">IPUtil</span>
extends java.lang.Object</pre>
<div class="block">InetAddress工具类，基于Guava的InetAddresses.
 
 主要包含int, String/IPV4String, InetAdress/Inet4Address之间的互相转换
 
 先将字符串传换为byte[]再用InetAddress.getByAddress(byte[])，避免了InetAddress.getByName(ip)可能引起的DNS访问.
 
 InetAddress与String的转换其实消耗不小，如果是有限的地址，建议进行缓存.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/net/IPUtil.html#IPUtil--">IPUtil</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static java.net.Inet4Address</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/net/IPUtil.html#fromInt-int-">fromInt</a></span>(int&nbsp;address)</code>
<div class="block">从int转换为Inet4Address(仅支持IPV4)</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static java.net.InetAddress</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/net/IPUtil.html#fromIpString-java.lang.String-">fromIpString</a></span>(java.lang.String&nbsp;address)</code>
<div class="block">从String转换为InetAddress.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static java.net.Inet4Address</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/net/IPUtil.html#fromIpv4String-java.lang.String-">fromIpv4String</a></span>(java.lang.String&nbsp;address)</code>
<div class="block">从IPv4String转换为InetAddress.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/net/IPUtil.html#intToIpv4String-int-">intToIpv4String</a></span>(int&nbsp;i)</code>
<div class="block">int转换到IPV4 String, from Netty NetUtil</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/net/IPUtil.html#ipv4StringToInt-java.lang.String-">ipv4StringToInt</a></span>(java.lang.String&nbsp;ipv4Str)</code>
<div class="block">Ipv4 String 转换到int</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/net/IPUtil.html#toInt-java.net.InetAddress-">toInt</a></span>(java.net.InetAddress&nbsp;address)</code>
<div class="block">从InetAddress转化到int, 传输和存储时, 用int代表InetAddress是最小的开销.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/net/IPUtil.html#toIpString-java.net.InetAddress-">toIpString</a></span>(java.net.InetAddress&nbsp;address)</code>
<div class="block">InetAddress转换为String.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="IPUtil--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>IPUtil</h4>
<pre>public&nbsp;IPUtil()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="toInt-java.net.InetAddress-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toInt</h4>
<pre>public static&nbsp;int&nbsp;toInt(java.net.InetAddress&nbsp;address)</pre>
<div class="block">从InetAddress转化到int, 传输和存储时, 用int代表InetAddress是最小的开销.
 
 InetAddress可以是IPV4或IPV6，都会转成IPV4.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>InetAddresses.coerceToInteger(InetAddress)</code></dd>
</dl>
</li>
</ul>
<a name="toIpString-java.net.InetAddress-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toIpString</h4>
<pre>public static&nbsp;java.lang.String&nbsp;toIpString(java.net.InetAddress&nbsp;address)</pre>
<div class="block">InetAddress转换为String.
 
 InetAddress可以是IPV4或IPV6. 其中IPV4直接调用getHostAddress()</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>InetAddresses.toAddrString(InetAddress)</code></dd>
</dl>
</li>
</ul>
<a name="fromInt-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fromInt</h4>
<pre>public static&nbsp;java.net.Inet4Address&nbsp;fromInt(int&nbsp;address)</pre>
<div class="block">从int转换为Inet4Address(仅支持IPV4)</div>
</li>
</ul>
<a name="fromIpString-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fromIpString</h4>
<pre>public static&nbsp;java.net.InetAddress&nbsp;fromIpString(java.lang.String&nbsp;address)</pre>
<div class="block">从String转换为InetAddress.
 
 IpString可以是ipv4 或 ipv6 string, 但不可以是域名.
 
 先字符串传换为byte[]再调getByAddress(byte[])，避免了调用getByName(ip)可能引起的DNS访问.</div>
</li>
</ul>
<a name="fromIpv4String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fromIpv4String</h4>
<pre>public static&nbsp;java.net.Inet4Address&nbsp;fromIpv4String(java.lang.String&nbsp;address)</pre>
<div class="block">从IPv4String转换为InetAddress.
 
 IpString如果确定ipv4, 使用本方法减少字符分析消耗 .
 
 先字符串传换为byte[]再调getByAddress(byte[])，避免了调用getByName(ip)可能引起的DNS访问.</div>
</li>
</ul>
<a name="intToIpv4String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>intToIpv4String</h4>
<pre>public static&nbsp;java.lang.String&nbsp;intToIpv4String(int&nbsp;i)</pre>
<div class="block">int转换到IPV4 String, from Netty NetUtil</div>
</li>
</ul>
<a name="ipv4StringToInt-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ipv4StringToInt</h4>
<pre>public static&nbsp;int&nbsp;ipv4StringToInt(java.lang.String&nbsp;ipv4Str)</pre>
<div class="block">Ipv4 String 转换到int</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../com/huazheng/tunny/tools/net/IPUtilTest.html" title="com.huazheng.tunny.tools.net中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/net/IPUtil.html" target="_top">框架</a></li>
<li><a href="IPUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

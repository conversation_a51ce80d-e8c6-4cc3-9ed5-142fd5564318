<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:30 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>WildcardMatcher</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="WildcardMatcher";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/text/TextValidatorTest.html" title="com.huazheng.tunny.tools.text中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/text/WildcardMatcherTest.html" title="com.huazheng.tunny.tools.text中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/text/WildcardMatcher.html" target="_top">框架</a></li>
<li><a href="WildcardMatcher.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.text</div>
<h2 title="类 WildcardMatcher" class="title">类 WildcardMatcher</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.text.WildcardMatcher</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">WildcardMatcher</span>
extends java.lang.Object</pre>
<div class="block">从Jodd移植，匹配以通配符比较字符串（比正则表达式简单），以及Ant Path风格如比较目录Path
 
 https://github.com/oblac/jodd/blob/master/jodd-core/src/main/java/jodd/util/Wildcard.java
 
 Checks whether a string or path matches a given wildcard pattern. Possible patterns allow to match single characters
 ('?') or any count of characters ('*'). Wildcard characters can be escaped (by an '\'). When matching path, deep tree
 wildcard also can be used ('**').
 <p>
 This method uses recursive matching, as in linux or windows. regexp works the same. This method is very fast,
 comparing to similar implementations.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/WildcardMatcher.html#PATH_MATCH">PATH_MATCH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected static com.google.common.base.Splitter</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/WildcardMatcher.html#PATH_SPLITTER">PATH_SPLITTER</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/WildcardMatcher.html#WildcardMatcher--">WildcardMatcher</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/WildcardMatcher.html#match-java.lang.CharSequence-java.lang.CharSequence-">match</a></span>(java.lang.CharSequence&nbsp;string,
     java.lang.CharSequence&nbsp;pattern)</code>
<div class="block">Checks whether a string matches a given wildcard pattern.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/WildcardMatcher.html#matchOne-java.lang.String-java.lang.String...-">matchOne</a></span>(java.lang.String&nbsp;src,
        java.lang.String...&nbsp;patterns)</code>
<div class="block">Matches string to at least one pattern.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/WildcardMatcher.html#matchPath-java.lang.String-java.lang.String-">matchPath</a></span>(java.lang.String&nbsp;path,
         java.lang.String&nbsp;pattern)</code>
<div class="block">Matches path against pattern using *, ?</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/WildcardMatcher.html#matchPathOne-java.lang.String-java.lang.String...-">matchPathOne</a></span>(java.lang.String&nbsp;platformDependentPath,
            java.lang.String...&nbsp;patterns)</code>
<div class="block">Matches path to at least one pattern.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>protected static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/WildcardMatcher.html#matchTokens-java.lang.String:A-java.lang.String:A-">matchTokens</a></span>(java.lang.String[]&nbsp;tokens,
           java.lang.String[]&nbsp;patterns)</code>
<div class="block">Match tokenized string and pattern.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="PATH_MATCH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PATH_MATCH</h4>
<pre>protected static final&nbsp;java.lang.String PATH_MATCH</pre>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.huazheng.tunny.tools.text.WildcardMatcher.PATH_MATCH">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="PATH_SPLITTER">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PATH_SPLITTER</h4>
<pre>protected static final&nbsp;com.google.common.base.Splitter PATH_SPLITTER</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="WildcardMatcher--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>WildcardMatcher</h4>
<pre>public&nbsp;WildcardMatcher()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="match-java.lang.CharSequence-java.lang.CharSequence-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>match</h4>
<pre>public static&nbsp;boolean&nbsp;match(java.lang.CharSequence&nbsp;string,
                            java.lang.CharSequence&nbsp;pattern)</pre>
<div class="block">Checks whether a string matches a given wildcard pattern.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>string</code> - input string</dd>
<dd><code>pattern</code> - pattern to match</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd><code>true</code> if string matches the pattern, otherwise <code>false</code></dd>
</dl>
</li>
</ul>
<a name="matchOne-java.lang.String-java.lang.String...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>matchOne</h4>
<pre>public static&nbsp;int&nbsp;matchOne(java.lang.String&nbsp;src,
                           java.lang.String...&nbsp;patterns)</pre>
<div class="block">Matches string to at least one pattern. Returns index of matched pattern, or <code>-1</code> otherwise.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/huazheng/tunny/tools/text/WildcardMatcher.html#match-java.lang.CharSequence-java.lang.CharSequence-"><code>match(CharSequence, CharSequence)</code></a></dd>
</dl>
</li>
</ul>
<a name="matchPathOne-java.lang.String-java.lang.String...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>matchPathOne</h4>
<pre>public static&nbsp;int&nbsp;matchPathOne(java.lang.String&nbsp;platformDependentPath,
                               java.lang.String...&nbsp;patterns)</pre>
<div class="block">Matches path to at least one pattern. Returns index of matched pattern or <code>-1</code> otherwise.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>#matchPath(String, String, char)</code></dd>
</dl>
</li>
</ul>
<a name="matchPath-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>matchPath</h4>
<pre>public static&nbsp;boolean&nbsp;matchPath(java.lang.String&nbsp;path,
                                java.lang.String&nbsp;pattern)</pre>
<div class="block">Matches path against pattern using *, ? and ** wildcards. Both path and the pattern are tokenized on path
 separators (both \ and /). '**' represents deep tree wildcard, as in Ant. The separator should match the
 corresponding path</div>
</li>
</ul>
<a name="matchTokens-java.lang.String:A-java.lang.String:A-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>matchTokens</h4>
<pre>protected static&nbsp;boolean&nbsp;matchTokens(java.lang.String[]&nbsp;tokens,
                                     java.lang.String[]&nbsp;patterns)</pre>
<div class="block">Match tokenized string and pattern.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/text/TextValidatorTest.html" title="com.huazheng.tunny.tools.text中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/text/WildcardMatcherTest.html" title="com.huazheng.tunny.tools.text中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/text/WildcardMatcher.html" target="_top">框架</a></li>
<li><a href="WildcardMatcher.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

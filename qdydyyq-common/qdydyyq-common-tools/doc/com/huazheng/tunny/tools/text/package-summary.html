<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:35 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>com.huazheng.tunny.tools.text</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.huazheng.tunny.tools.text";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li class="navBarCell1Rev">程序包</li>
<li>类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/security/package-summary.html">上一个程序包</a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/time/package-summary.html">下一个程序包</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/text/package-summary.html" target="_top">框架</a></li>
<li><a href="package-summary.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="程序包" class="title">程序包&nbsp;com.huazheng.tunny.tools.text</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="类概要表, 列表类和解释">
<caption><span>类概要</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">类</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/text/Charsets.html" title="com.huazheng.tunny.tools.text中的类">Charsets</a></td>
<td class="colLast">
<div class="block">尽量使用Charsets.UTF8而不是"UTF-8"，减少JDK里的Charset查找消耗.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/text/CsvUtil.html" title="com.huazheng.tunny.tools.text中的类">CsvUtil</a></td>
<td class="colLast">
<div class="block">从Jodd移植
 
 https://github.com/oblac/jodd/blob/master/jodd-core/src/main/java/jodd/util/CsvUtil.java
 
 Helps with CSV strings.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/text/CsvUtilTest.html" title="com.huazheng.tunny.tools.text中的类">CsvUtilTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/text/EncodeUtil.html" title="com.huazheng.tunny.tools.text中的类">EncodeUtil</a></td>
<td class="colLast">
<div class="block">string/url -> hex/base64 编解码工具集(via guava BaseEncoding)</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/text/EncodeUtilTest.html" title="com.huazheng.tunny.tools.text中的类">EncodeUtilTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/text/EscapeUtil.html" title="com.huazheng.tunny.tools.text中的类">EscapeUtil</a></td>
<td class="colLast">
<div class="block">转义工具集.
 
 1.URL 转义，转义后的URL可作为URL中的参数 (via JDK)
 
 2.xml/html 转义(via Commons-Lang StringEscapeUtils ,但已被废弃, 建议用Common-Text）
 
 比如 "bread" & "butter" 转化为 &quot;bread&quot; &amp; &quot;butter&quot;</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/text/EscapeUtilTest.html" title="com.huazheng.tunny.tools.text中的类">EscapeUtilTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/text/HashUtil.html" title="com.huazheng.tunny.tools.text中的类">HashUtil</a></td>
<td class="colLast">
<div class="block">封装各种Hash算法的工具类
 
 1.SHA-1, 安全性较高, 返回byte[](可用Encodes进一步被编码为Hex, Base64)
 
 性能优化，使用ThreadLocal的MessageDigest(from ElasticSearch)
 
 支持带salt并且进行迭代达到更高的安全性.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/text/HashUtilTest.html" title="com.huazheng.tunny.tools.text中的类">HashUtilTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/text/MoreStringUtil.html" title="com.huazheng.tunny.tools.text中的类">MoreStringUtil</a></td>
<td class="colLast">
<div class="block">尽量使用Common Lang StringUtils, 基本覆盖了所有类库的StringUtils
 
 本类仅补充少量额外方法, 尤其是针对char的运算
 
 1. split char/chars
 
 2.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/text/MoreStringUtilTest.html" title="com.huazheng.tunny.tools.text中的类">MoreStringUtilTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/text/StringBuilderHolder.html" title="com.huazheng.tunny.tools.text中的类">StringBuilderHolder</a></td>
<td class="colLast">
<div class="block">参考Netty的InternalThreadLocalMap 与 BigDecimal, 放在threadLocal中重用的StringBuilder, 节约StringBuilder内部的char[]
 
 参考文章：《StringBuilder在高性能场景下的正确用法》http://calvin1978.blogcn.com/articles/stringbuilder.html
 
 不过仅在String对象较大时才有明显效果，否则抵不上访问ThreadLocal的消耗.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/text/StringBuilderHolderTest.html" title="com.huazheng.tunny.tools.text中的类">StringBuilderHolderTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/text/TextValidator.html" title="com.huazheng.tunny.tools.text中的类">TextValidator</a></td>
<td class="colLast">
<div class="block">通过正则表达判断是否正确的URL， 邮箱，手机号，固定电话，身份证，邮箱等.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/text/TextValidatorTest.html" title="com.huazheng.tunny.tools.text中的类">TextValidatorTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/text/WildcardMatcher.html" title="com.huazheng.tunny.tools.text中的类">WildcardMatcher</a></td>
<td class="colLast">
<div class="block">从Jodd移植，匹配以通配符比较字符串（比正则表达式简单），以及Ant Path风格如比较目录Path
 
 https://github.com/oblac/jodd/blob/master/jodd-core/src/main/java/jodd/util/Wildcard.java
 
 Checks whether a string or path matches a given wildcard pattern.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/text/WildcardMatcherTest.html" title="com.huazheng.tunny.tools.text中的类">WildcardMatcherTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li class="navBarCell1Rev">程序包</li>
<li>类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/security/package-summary.html">上一个程序包</a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/time/package-summary.html">下一个程序包</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/text/package-summary.html" target="_top">框架</a></li>
<li><a href="package-summary.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

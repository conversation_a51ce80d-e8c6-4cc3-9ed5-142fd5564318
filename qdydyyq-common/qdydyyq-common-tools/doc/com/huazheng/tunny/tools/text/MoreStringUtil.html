<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:30 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>MoreStringUtil</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="MoreStringUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/text/HashUtilTest.html" title="com.huazheng.tunny.tools.text中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/text/MoreStringUtilTest.html" title="com.huazheng.tunny.tools.text中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/text/MoreStringUtil.html" target="_top">框架</a></li>
<li><a href="MoreStringUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.text</div>
<h2 title="类 MoreStringUtil" class="title">类 MoreStringUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.text.MoreStringUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">MoreStringUtil</span>
extends java.lang.Object</pre>
<div class="block">尽量使用Common Lang StringUtils, 基本覆盖了所有类库的StringUtils
 
 本类仅补充少量额外方法, 尤其是针对char的运算
 
 1. split char/chars
 
 2. 针对char的replace first/last, startWith,endWith 等</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/MoreStringUtil.html#MoreStringUtil--">MoreStringUtil</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static com.google.common.base.Splitter</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/MoreStringUtil.html#charsSplitter-java.lang.String-">charsSplitter</a></span>(java.lang.String&nbsp;separatorChars)</code>
<div class="block">使用多个可选的char作为分割符, 还可以设置omitEmptyStrings,trimResults等配置
 
 设置后的Splitter进行重用，不要每次创建</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/MoreStringUtil.html#endWith-java.lang.CharSequence-char-">endWith</a></span>(java.lang.CharSequence&nbsp;s,
       char&nbsp;c)</code>
<div class="block">判断字符串是否以字母结尾
 
 如果字符串为Null或空，返回false</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/MoreStringUtil.html#removeEnd-java.lang.String-char-">removeEnd</a></span>(java.lang.String&nbsp;s,
         char&nbsp;c)</code>
<div class="block">如果结尾字符为c, 去除掉该字符.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/MoreStringUtil.html#replaceFirst-java.lang.String-char-char-">replaceFirst</a></span>(java.lang.String&nbsp;s,
            char&nbsp;sub,
            char&nbsp;with)</code>
<div class="block">String 有replace(char,char)，但缺少单独replace first/last的</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/MoreStringUtil.html#replaceLast-java.lang.String-char-char-">replaceLast</a></span>(java.lang.String&nbsp;s,
           char&nbsp;sub,
           char&nbsp;with)</code>
<div class="block">String 有replace(char,char)替换全部char，但缺少单独replace first/last</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static java.util.List&lt;java.lang.String&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/MoreStringUtil.html#split-java.lang.String-char-">split</a></span>(java.lang.String&nbsp;str,
     char&nbsp;separatorChar)</code>
<div class="block">高性能的Split，针对char的分隔符号，比JDK String自带的高效.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static java.util.List&lt;java.lang.String&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/MoreStringUtil.html#split-java.lang.String-char-int-">split</a></span>(java.lang.String&nbsp;str,
     char&nbsp;separatorChar,
     int&nbsp;expectParts)</code>
<div class="block">高性能的Split，针对char的分隔符号，比JDK String自带的高效.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/MoreStringUtil.html#startWith-java.lang.CharSequence-char-">startWith</a></span>(java.lang.CharSequence&nbsp;s,
         char&nbsp;c)</code>
<div class="block">判断字符串是否以字母开头
 
 如果字符串为Null或空，返回false</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/MoreStringUtil.html#utf8EncodedLength-java.lang.CharSequence-">utf8EncodedLength</a></span>(java.lang.CharSequence&nbsp;sequence)</code>
<div class="block">计算字符串被UTF8编码后的字节数 via guava</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="MoreStringUtil--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>MoreStringUtil</h4>
<pre>public&nbsp;MoreStringUtil()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="split-java.lang.String-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>split</h4>
<pre>public static&nbsp;java.util.List&lt;java.lang.String&gt;&nbsp;split(java.lang.String&nbsp;str,
                                                     char&nbsp;separatorChar)</pre>
<div class="block">高性能的Split，针对char的分隔符号，比JDK String自带的高效.
 
 copy from Commons Lange 3.5 StringUtils 并做优化</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/huazheng/tunny/tools/text/MoreStringUtil.html#split-java.lang.String-char-int-"><code>split(String, char, int)</code></a></dd>
</dl>
</li>
</ul>
<a name="split-java.lang.String-char-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>split</h4>
<pre>public static&nbsp;java.util.List&lt;java.lang.String&gt;&nbsp;split(java.lang.String&nbsp;str,
                                                     char&nbsp;separatorChar,
                                                     int&nbsp;expectParts)</pre>
<div class="block">高性能的Split，针对char的分隔符号，比JDK String自带的高效.
 
 copy from Commons Lange 3.5 StringUtils, 做如下优化:
 
 1. 最后不做数组转换，直接返回List.
 
 2. 可设定List初始大小.
 
 3. preserveAllTokens 取默认值false</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>expectParts</code> - 预估分割后的List大小，初始化数据更精准</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>如果为null返回null, 如果为""返回空数组</dd>
</dl>
</li>
</ul>
<a name="charsSplitter-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>charsSplitter</h4>
<pre>public static&nbsp;com.google.common.base.Splitter&nbsp;charsSplitter(java.lang.String&nbsp;separatorChars)</pre>
<div class="block">使用多个可选的char作为分割符, 还可以设置omitEmptyStrings,trimResults等配置
 
 设置后的Splitter进行重用，不要每次创建</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>separatorChars</code> - 比如Unix/Windows的路径分割符 "/\\"</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Splitter</code></dd>
</dl>
</li>
</ul>
<a name="replaceFirst-java.lang.String-char-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>replaceFirst</h4>
<pre>public static&nbsp;java.lang.String&nbsp;replaceFirst(java.lang.String&nbsp;s,
                                            char&nbsp;sub,
                                            char&nbsp;with)</pre>
<div class="block">String 有replace(char,char)，但缺少单独replace first/last的</div>
</li>
</ul>
<a name="replaceLast-java.lang.String-char-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>replaceLast</h4>
<pre>public static&nbsp;java.lang.String&nbsp;replaceLast(java.lang.String&nbsp;s,
                                           char&nbsp;sub,
                                           char&nbsp;with)</pre>
<div class="block">String 有replace(char,char)替换全部char，但缺少单独replace first/last</div>
</li>
</ul>
<a name="startWith-java.lang.CharSequence-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startWith</h4>
<pre>public static&nbsp;boolean&nbsp;startWith(java.lang.CharSequence&nbsp;s,
                                char&nbsp;c)</pre>
<div class="block">判断字符串是否以字母开头
 
 如果字符串为Null或空，返回false</div>
</li>
</ul>
<a name="endWith-java.lang.CharSequence-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>endWith</h4>
<pre>public static&nbsp;boolean&nbsp;endWith(java.lang.CharSequence&nbsp;s,
                              char&nbsp;c)</pre>
<div class="block">判断字符串是否以字母结尾
 
 如果字符串为Null或空，返回false</div>
</li>
</ul>
<a name="removeEnd-java.lang.String-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeEnd</h4>
<pre>public static&nbsp;java.lang.String&nbsp;removeEnd(java.lang.String&nbsp;s,
                                         char&nbsp;c)</pre>
<div class="block">如果结尾字符为c, 去除掉该字符.</div>
</li>
</ul>
<a name="utf8EncodedLength-java.lang.CharSequence-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>utf8EncodedLength</h4>
<pre>public static&nbsp;int&nbsp;utf8EncodedLength(java.lang.CharSequence&nbsp;sequence)</pre>
<div class="block">计算字符串被UTF8编码后的字节数 via guava</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Utf8.encodedLength(CharSequence)</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/text/HashUtilTest.html" title="com.huazheng.tunny.tools.text中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/text/MoreStringUtilTest.html" title="com.huazheng.tunny.tools.text中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/text/MoreStringUtil.html" target="_top">框架</a></li>
<li><a href="MoreStringUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:30 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>HashUtil</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HashUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9,"i14":9,"i15":9,"i16":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/text/EscapeUtilTest.html" title="com.huazheng.tunny.tools.text中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/text/HashUtilTest.html" title="com.huazheng.tunny.tools.text中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/text/HashUtil.html" target="_top">框架</a></li>
<li><a href="HashUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.text</div>
<h2 title="类 HashUtil" class="title">类 HashUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.text.HashUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">HashUtil</span>
extends java.lang.Object</pre>
<div class="block">封装各种Hash算法的工具类
 
 1.SHA-1, 安全性较高, 返回byte[](可用Encodes进一步被编码为Hex, Base64)
 
 性能优化，使用ThreadLocal的MessageDigest(from ElasticSearch)
 
 支持带salt并且进行迭代达到更高的安全性.
 
 MD5的安全性较低, 只在文件Checksum时支持.
 
 2.crc32, murmur32这些不追求安全性, 性能较高, 返回int.
 
 其中crc32基于JDK, murmurhash基于guava</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/HashUtil.html#MURMUR_SEED">MURMUR_SEED</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/HashUtil.html#HashUtil--">HashUtil</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/HashUtil.html#crc32AsInt-byte:A-">crc32AsInt</a></span>(byte[]&nbsp;input)</code>
<div class="block">对输入字符串进行crc32散列返回int, 返回值有可能是负数.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/HashUtil.html#crc32AsInt-java.lang.String-">crc32AsInt</a></span>(java.lang.String&nbsp;input)</code>
<div class="block">对输入字符串进行crc32散列返回int, 返回值有可能是负数.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/HashUtil.html#crc32AsLong-byte:A-">crc32AsLong</a></span>(byte[]&nbsp;input)</code>
<div class="block">对输入字符串进行crc32散列，与php兼容，在64bit系统下返回永远是正数的long
 
 Guava也有crc32实现, 但返回值无法返回long，所以统一使用JDK默认实现</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/HashUtil.html#crc32AsLong-java.lang.String-">crc32AsLong</a></span>(java.lang.String&nbsp;input)</code>
<div class="block">对输入字符串进行crc32散列，与php兼容，在64bit系统下返回永远是正数的long
 
 Guava也有crc32实现, 但返回值无法返回long，所以统一使用JDK默认实现</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/HashUtil.html#generateSalt-int-">generateSalt</a></span>(int&nbsp;numBytes)</code>
<div class="block">用SecureRandom生成随机的byte[]作为salt.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/HashUtil.html#md5File-java.io.InputStream-">md5File</a></span>(java.io.InputStream&nbsp;input)</code>
<div class="block">对文件进行md5散列，被破解后MD5已较少人用.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/HashUtil.html#murmur128AsLong-byte:A-">murmur128AsLong</a></span>(byte[]&nbsp;input)</code>
<div class="block">对输入字符串进行murmur128散列, 返回值可能是负数</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/HashUtil.html#murmur128AsLong-java.lang.String-">murmur128AsLong</a></span>(java.lang.String&nbsp;input)</code>
<div class="block">对输入字符串进行murmur128散列, 返回值可能是负数</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/HashUtil.html#murmur32AsInt-byte:A-">murmur32AsInt</a></span>(byte[]&nbsp;input)</code>
<div class="block">对输入字符串进行murmur32散列, 返回值可能是负数</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/HashUtil.html#murmur32AsInt-java.lang.String-">murmur32AsInt</a></span>(java.lang.String&nbsp;input)</code>
<div class="block">对输入字符串进行murmur32散列, 返回值可能是负数</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/HashUtil.html#sha1-byte:A-">sha1</a></span>(byte[]&nbsp;input)</code>
<div class="block">对输入字符串进行sha1散列.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/HashUtil.html#sha1-byte:A-byte:A-">sha1</a></span>(byte[]&nbsp;input,
    byte[]&nbsp;salt)</code>
<div class="block">对输入字符串进行sha1散列，带salt达到更高的安全性.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/HashUtil.html#sha1-byte:A-byte:A-int-">sha1</a></span>(byte[]&nbsp;input,
    byte[]&nbsp;salt,
    int&nbsp;iterations)</code>
<div class="block">对输入字符串进行sha1散列，带salt而且迭代达到更高更高的安全性.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/HashUtil.html#sha1-java.lang.String-">sha1</a></span>(java.lang.String&nbsp;input)</code>
<div class="block">对输入字符串进行sha1散列, 编码默认为UTF8.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/HashUtil.html#sha1-java.lang.String-byte:A-">sha1</a></span>(java.lang.String&nbsp;input,
    byte[]&nbsp;salt)</code>
<div class="block">对输入字符串进行sha1散列，带salt达到更高的安全性.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/HashUtil.html#sha1-java.lang.String-byte:A-int-">sha1</a></span>(java.lang.String&nbsp;input,
    byte[]&nbsp;salt,
    int&nbsp;iterations)</code>
<div class="block">对输入字符串进行sha1散列，带salt而且迭代达到更高更高的安全性.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/HashUtil.html#sha1File-java.io.InputStream-">sha1File</a></span>(java.io.InputStream&nbsp;input)</code>
<div class="block">对文件进行sha1散列.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="MURMUR_SEED">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>MURMUR_SEED</h4>
<pre>public static final&nbsp;int MURMUR_SEED</pre>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.huazheng.tunny.tools.text.HashUtil.MURMUR_SEED">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="HashUtil--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HashUtil</h4>
<pre>public&nbsp;HashUtil()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="sha1-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sha1</h4>
<pre>public static&nbsp;byte[]&nbsp;sha1(byte[]&nbsp;input)</pre>
<div class="block">对输入字符串进行sha1散列.</div>
</li>
</ul>
<a name="sha1-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sha1</h4>
<pre>public static&nbsp;byte[]&nbsp;sha1(java.lang.String&nbsp;input)</pre>
<div class="block">对输入字符串进行sha1散列, 编码默认为UTF8.</div>
</li>
</ul>
<a name="sha1-byte:A-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sha1</h4>
<pre>public static&nbsp;byte[]&nbsp;sha1(byte[]&nbsp;input,
                          byte[]&nbsp;salt)</pre>
<div class="block">对输入字符串进行sha1散列，带salt达到更高的安全性.</div>
</li>
</ul>
<a name="sha1-java.lang.String-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sha1</h4>
<pre>public static&nbsp;byte[]&nbsp;sha1(java.lang.String&nbsp;input,
                          byte[]&nbsp;salt)</pre>
<div class="block">对输入字符串进行sha1散列，带salt达到更高的安全性.</div>
</li>
</ul>
<a name="sha1-byte:A-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sha1</h4>
<pre>public static&nbsp;byte[]&nbsp;sha1(byte[]&nbsp;input,
                          byte[]&nbsp;salt,
                          int&nbsp;iterations)</pre>
<div class="block">对输入字符串进行sha1散列，带salt而且迭代达到更高更高的安全性.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/huazheng/tunny/tools/text/HashUtil.html#generateSalt-int-"><code>generateSalt(int)</code></a></dd>
</dl>
</li>
</ul>
<a name="sha1-java.lang.String-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sha1</h4>
<pre>public static&nbsp;byte[]&nbsp;sha1(java.lang.String&nbsp;input,
                          byte[]&nbsp;salt,
                          int&nbsp;iterations)</pre>
<div class="block">对输入字符串进行sha1散列，带salt而且迭代达到更高更高的安全性.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/huazheng/tunny/tools/text/HashUtil.html#generateSalt-int-"><code>generateSalt(int)</code></a></dd>
</dl>
</li>
</ul>
<a name="generateSalt-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>generateSalt</h4>
<pre>public static&nbsp;byte[]&nbsp;generateSalt(int&nbsp;numBytes)</pre>
<div class="block">用SecureRandom生成随机的byte[]作为salt.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>numBytes</code> - salt数组的大小</dd>
</dl>
</li>
</ul>
<a name="sha1File-java.io.InputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sha1File</h4>
<pre>public static&nbsp;byte[]&nbsp;sha1File(java.io.InputStream&nbsp;input)
                       throws java.io.IOException</pre>
<div class="block">对文件进行sha1散列.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="md5File-java.io.InputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>md5File</h4>
<pre>public static&nbsp;byte[]&nbsp;md5File(java.io.InputStream&nbsp;input)
                      throws java.io.IOException</pre>
<div class="block">对文件进行md5散列，被破解后MD5已较少人用.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="crc32AsInt-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>crc32AsInt</h4>
<pre>public static&nbsp;int&nbsp;crc32AsInt(java.lang.String&nbsp;input)</pre>
<div class="block">对输入字符串进行crc32散列返回int, 返回值有可能是负数.
 
 Guava也有crc32实现, 但返回值无法返回long，所以统一使用JDK默认实现</div>
</li>
</ul>
<a name="crc32AsInt-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>crc32AsInt</h4>
<pre>public static&nbsp;int&nbsp;crc32AsInt(byte[]&nbsp;input)</pre>
<div class="block">对输入字符串进行crc32散列返回int, 返回值有可能是负数.
 
 Guava也有crc32实现, 但返回值无法返回long，所以统一使用JDK默认实现</div>
</li>
</ul>
<a name="crc32AsLong-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>crc32AsLong</h4>
<pre>public static&nbsp;long&nbsp;crc32AsLong(java.lang.String&nbsp;input)</pre>
<div class="block">对输入字符串进行crc32散列，与php兼容，在64bit系统下返回永远是正数的long
 
 Guava也有crc32实现, 但返回值无法返回long，所以统一使用JDK默认实现</div>
</li>
</ul>
<a name="crc32AsLong-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>crc32AsLong</h4>
<pre>public static&nbsp;long&nbsp;crc32AsLong(byte[]&nbsp;input)</pre>
<div class="block">对输入字符串进行crc32散列，与php兼容，在64bit系统下返回永远是正数的long
 
 Guava也有crc32实现, 但返回值无法返回long，所以统一使用JDK默认实现</div>
</li>
</ul>
<a name="murmur32AsInt-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>murmur32AsInt</h4>
<pre>public static&nbsp;int&nbsp;murmur32AsInt(byte[]&nbsp;input)</pre>
<div class="block">对输入字符串进行murmur32散列, 返回值可能是负数</div>
</li>
</ul>
<a name="murmur32AsInt-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>murmur32AsInt</h4>
<pre>public static&nbsp;int&nbsp;murmur32AsInt(java.lang.String&nbsp;input)</pre>
<div class="block">对输入字符串进行murmur32散列, 返回值可能是负数</div>
</li>
</ul>
<a name="murmur128AsLong-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>murmur128AsLong</h4>
<pre>public static&nbsp;long&nbsp;murmur128AsLong(byte[]&nbsp;input)</pre>
<div class="block">对输入字符串进行murmur128散列, 返回值可能是负数</div>
</li>
</ul>
<a name="murmur128AsLong-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>murmur128AsLong</h4>
<pre>public static&nbsp;long&nbsp;murmur128AsLong(java.lang.String&nbsp;input)</pre>
<div class="block">对输入字符串进行murmur128散列, 返回值可能是负数</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/text/EscapeUtilTest.html" title="com.huazheng.tunny.tools.text中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/text/HashUtilTest.html" title="com.huazheng.tunny.tools.text中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/text/HashUtil.html" target="_top">框架</a></li>
<li><a href="HashUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

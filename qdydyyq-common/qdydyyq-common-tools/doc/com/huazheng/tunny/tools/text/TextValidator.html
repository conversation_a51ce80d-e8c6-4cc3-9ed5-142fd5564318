<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:30 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>TextValidator</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TextValidator";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/text/StringBuilderHolderTest.html" title="com.huazheng.tunny.tools.text中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/text/TextValidatorTest.html" title="com.huazheng.tunny.tools.text中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/text/TextValidator.html" target="_top">框架</a></li>
<li><a href="TextValidator.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.text</div>
<h2 title="类 TextValidator" class="title">类 TextValidator</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.text.TextValidator</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">TextValidator</span>
extends java.lang.Object</pre>
<div class="block">通过正则表达判断是否正确的URL， 邮箱，手机号，固定电话，身份证，邮箱等.
 
 从AndroidUtilCode的RegexUtils移植, 性能优化将正则表达式为预编译, 并修改了TEL的正则表达式.
 
 https://github.com/Blankj/AndroidUtilCode/blob/master/utilcode/src/main/java/com/blankj/utilcode/util/RegexUtils.java
 https://github.com/Blankj/AndroidUtilCode/blob/master/utilcode/src/main/java/com/blankj/utilcode/constant/RegexConstants.java</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/TextValidator.html#REGEX_MOBILE_EXACT">REGEX_MOBILE_EXACT</a></span></code>
<div class="block">正则：手机号（精确）, 已知3位前缀＋8位数字
 
 移动：134(0-8)、135、136、137、138、139、147、150、151、152、157、158、159、178、182、183、184、187、188、198
 
 
 联通：130、131、132、145、155、156、166、171、175、176、185、186
 
 
 电信：133、153、173、177、180、181、189、199
 
 
 全球星：1349
 
 
 虚拟运营商：170
 </div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/TextValidator.html#TextValidator--">TextValidator</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/TextValidator.html#isDate-java.lang.CharSequence-">isDate</a></span>(java.lang.CharSequence&nbsp;input)</code>
<div class="block">验证yyyy-MM-dd格式的日期校验，已考虑平闰年</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/TextValidator.html#isEmail-java.lang.CharSequence-">isEmail</a></span>(java.lang.CharSequence&nbsp;input)</code>
<div class="block">验证邮箱</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/TextValidator.html#isIdCard-java.lang.CharSequence-">isIdCard</a></span>(java.lang.CharSequence&nbsp;input)</code>
<div class="block">验证15或18位身份证号码</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/TextValidator.html#isIp-java.lang.CharSequence-">isIp</a></span>(java.lang.CharSequence&nbsp;input)</code>
<div class="block">验证IP地址</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/TextValidator.html#isMatch-java.util.regex.Pattern-java.lang.CharSequence-">isMatch</a></span>(java.util.regex.Pattern&nbsp;pattern,
       java.lang.CharSequence&nbsp;input)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/TextValidator.html#isMobileExact-java.lang.CharSequence-">isMobileExact</a></span>(java.lang.CharSequence&nbsp;input)</code>
<div class="block">验证手机号（精确）</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/TextValidator.html#isMobileSimple-java.lang.CharSequence-">isMobileSimple</a></span>(java.lang.CharSequence&nbsp;input)</code>
<div class="block">验证手机号（简单）</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/TextValidator.html#isTel-java.lang.CharSequence-">isTel</a></span>(java.lang.CharSequence&nbsp;input)</code>
<div class="block">验证固定电话号码</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/text/TextValidator.html#isUrl-java.lang.CharSequence-">isUrl</a></span>(java.lang.CharSequence&nbsp;input)</code>
<div class="block">验证URL</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="REGEX_MOBILE_EXACT">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>REGEX_MOBILE_EXACT</h4>
<pre>public static final&nbsp;java.lang.String REGEX_MOBILE_EXACT</pre>
<div class="block">正则：手机号（精确）, 已知3位前缀＋8位数字
 <p>
 移动：134(0-8)、135、136、137、138、139、147、150、151、152、157、158、159、178、182、183、184、187、188、198
 </p>
 <p>
 联通：130、131、132、145、155、156、166、171、175、176、185、186
 </p>
 <p>
 电信：133、153、173、177、180、181、189、199
 </p>
 <p>
 全球星：1349
 </p>
 <p>
 虚拟运营商：170
 </p></div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.huazheng.tunny.tools.text.TextValidator.REGEX_MOBILE_EXACT">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="TextValidator--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TextValidator</h4>
<pre>public&nbsp;TextValidator()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="isMobileSimple-java.lang.CharSequence-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMobileSimple</h4>
<pre>public static&nbsp;boolean&nbsp;isMobileSimple(java.lang.CharSequence&nbsp;input)</pre>
<div class="block">验证手机号（简单）</div>
</li>
</ul>
<a name="isMobileExact-java.lang.CharSequence-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMobileExact</h4>
<pre>public static&nbsp;boolean&nbsp;isMobileExact(java.lang.CharSequence&nbsp;input)</pre>
<div class="block">验证手机号（精确）</div>
</li>
</ul>
<a name="isTel-java.lang.CharSequence-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isTel</h4>
<pre>public static&nbsp;boolean&nbsp;isTel(java.lang.CharSequence&nbsp;input)</pre>
<div class="block">验证固定电话号码</div>
</li>
</ul>
<a name="isIdCard-java.lang.CharSequence-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isIdCard</h4>
<pre>public static&nbsp;boolean&nbsp;isIdCard(java.lang.CharSequence&nbsp;input)</pre>
<div class="block">验证15或18位身份证号码</div>
</li>
</ul>
<a name="isEmail-java.lang.CharSequence-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEmail</h4>
<pre>public static&nbsp;boolean&nbsp;isEmail(java.lang.CharSequence&nbsp;input)</pre>
<div class="block">验证邮箱</div>
</li>
</ul>
<a name="isUrl-java.lang.CharSequence-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isUrl</h4>
<pre>public static&nbsp;boolean&nbsp;isUrl(java.lang.CharSequence&nbsp;input)</pre>
<div class="block">验证URL</div>
</li>
</ul>
<a name="isDate-java.lang.CharSequence-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDate</h4>
<pre>public static&nbsp;boolean&nbsp;isDate(java.lang.CharSequence&nbsp;input)</pre>
<div class="block">验证yyyy-MM-dd格式的日期校验，已考虑平闰年</div>
</li>
</ul>
<a name="isIp-java.lang.CharSequence-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isIp</h4>
<pre>public static&nbsp;boolean&nbsp;isIp(java.lang.CharSequence&nbsp;input)</pre>
<div class="block">验证IP地址</div>
</li>
</ul>
<a name="isMatch-java.util.regex.Pattern-java.lang.CharSequence-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isMatch</h4>
<pre>public static&nbsp;boolean&nbsp;isMatch(java.util.regex.Pattern&nbsp;pattern,
                              java.lang.CharSequence&nbsp;input)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/text/StringBuilderHolderTest.html" title="com.huazheng.tunny.tools.text中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/text/TextValidatorTest.html" title="com.huazheng.tunny.tools.text中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/text/TextValidator.html" target="_top">框架</a></li>
<li><a href="TextValidator.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

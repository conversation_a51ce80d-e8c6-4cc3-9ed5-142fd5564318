<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:32 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>MathUtil</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="MathUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9,"i14":9,"i15":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../com/huazheng/tunny/tools/number/MathUtilTest.html" title="com.huazheng.tunny.tools.number中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/number/MathUtil.html" target="_top">框架</a></li>
<li><a href="MathUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.number</div>
<h2 title="类 MathUtil" class="title">类 MathUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.number.MathUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">MathUtil</span>
extends java.lang.Object</pre>
<div class="block">数学相关工具类.包括
 
 1. 2的倍数的计算
 
 2. 其他函数如安全的取模，可控制取整方向的相除，乘方，开方等。</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/MathUtil.html#MathUtil--">MathUtil</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/MathUtil.html#divide-int-int-java.math.RoundingMode-">divide</a></span>(int&nbsp;p,
      int&nbsp;q,
      java.math.RoundingMode&nbsp;mode)</code>
<div class="block">能控制rounding方向的int相除.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/MathUtil.html#divide-long-long-java.math.RoundingMode-">divide</a></span>(long&nbsp;p,
      long&nbsp;q,
      java.math.RoundingMode&nbsp;mode)</code>
<div class="block">能控制rounding方向的long相除
 
 jdk的'/'运算符，直接向下取整</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/MathUtil.html#isPowerOfTwo-int-">isPowerOfTwo</a></span>(int&nbsp;value)</code>
<div class="block">是否2的倍数</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/MathUtil.html#isPowerOfTwo-long-">isPowerOfTwo</a></span>(long&nbsp;value)</code>
<div class="block">是否2的倍数</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/MathUtil.html#mod-int-int-">mod</a></span>(int&nbsp;x,
   int&nbsp;m)</code>
<div class="block">保证结果为正数的取模.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/MathUtil.html#mod-long-int-">mod</a></span>(long&nbsp;x,
   int&nbsp;m)</code>
<div class="block">保证结果为正数的取模</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/MathUtil.html#mod-long-long-">mod</a></span>(long&nbsp;x,
   long&nbsp;m)</code>
<div class="block">保证结果为正数的取模.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/MathUtil.html#modByPowerOfTwo-int-int-">modByPowerOfTwo</a></span>(int&nbsp;value,
               int&nbsp;mod)</code>
<div class="block">当模为2的倍数时，用比取模块更快的方式计算.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/MathUtil.html#nextPowerOfTwo-int-">nextPowerOfTwo</a></span>(int&nbsp;value)</code>
<div class="block">往上找出最接近的2的倍数，比如15返回16， 17返回32.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/MathUtil.html#nextPowerOfTwo-long-">nextPowerOfTwo</a></span>(long&nbsp;value)</code>
<div class="block">往上找出最接近的2的倍数，比如15返回16， 17返回32.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/MathUtil.html#pow-int-int-">pow</a></span>(int&nbsp;b,
   int&nbsp;k)</code>
<div class="block">平方</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/MathUtil.html#pow-long-int-">pow</a></span>(long&nbsp;b,
   int&nbsp;k)</code>
<div class="block">平方</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/MathUtil.html#previousPowerOfTwo-int-">previousPowerOfTwo</a></span>(int&nbsp;value)</code>
<div class="block">往下找出最接近2的倍数，比如15返回8， 17返回16.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/MathUtil.html#previousPowerOfTwo-long-">previousPowerOfTwo</a></span>(long&nbsp;value)</code>
<div class="block">往下找出最接近2的倍数，比如15返回8， 17返回16.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/MathUtil.html#sqrt-int-java.math.RoundingMode-">sqrt</a></span>(int&nbsp;x,
    java.math.RoundingMode&nbsp;mode)</code>
<div class="block">开方</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/MathUtil.html#sqrt-long-java.math.RoundingMode-">sqrt</a></span>(long&nbsp;x,
    java.math.RoundingMode&nbsp;mode)</code>
<div class="block">开方</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="MathUtil--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>MathUtil</h4>
<pre>public&nbsp;MathUtil()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="nextPowerOfTwo-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nextPowerOfTwo</h4>
<pre>public static&nbsp;int&nbsp;nextPowerOfTwo(int&nbsp;value)</pre>
<div class="block">往上找出最接近的2的倍数，比如15返回16， 17返回32.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>value</code> - 必须为正数，否则抛出异常.</dd>
</dl>
</li>
</ul>
<a name="nextPowerOfTwo-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nextPowerOfTwo</h4>
<pre>public static&nbsp;long&nbsp;nextPowerOfTwo(long&nbsp;value)</pre>
<div class="block">往上找出最接近的2的倍数，比如15返回16， 17返回32.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>value</code> - 必须为正数，否则抛出异常.</dd>
</dl>
</li>
</ul>
<a name="previousPowerOfTwo-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>previousPowerOfTwo</h4>
<pre>public static&nbsp;int&nbsp;previousPowerOfTwo(int&nbsp;value)</pre>
<div class="block">往下找出最接近2的倍数，比如15返回8， 17返回16.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>value</code> - 必须为正数，否则抛出异常.</dd>
</dl>
</li>
</ul>
<a name="previousPowerOfTwo-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>previousPowerOfTwo</h4>
<pre>public static&nbsp;long&nbsp;previousPowerOfTwo(long&nbsp;value)</pre>
<div class="block">往下找出最接近2的倍数，比如15返回8， 17返回16.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>value</code> - 必须为正数，否则抛出异常.</dd>
</dl>
</li>
</ul>
<a name="isPowerOfTwo-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPowerOfTwo</h4>
<pre>public static&nbsp;boolean&nbsp;isPowerOfTwo(int&nbsp;value)</pre>
<div class="block">是否2的倍数</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>value</code> - 不是正数时总是返回false</dd>
</dl>
</li>
</ul>
<a name="isPowerOfTwo-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPowerOfTwo</h4>
<pre>public static&nbsp;boolean&nbsp;isPowerOfTwo(long&nbsp;value)</pre>
<div class="block">是否2的倍数</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>value</code> - <=0 时总是返回false</dd>
</dl>
</li>
</ul>
<a name="modByPowerOfTwo-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>modByPowerOfTwo</h4>
<pre>public static&nbsp;int&nbsp;modByPowerOfTwo(int&nbsp;value,
                                  int&nbsp;mod)</pre>
<div class="block">当模为2的倍数时，用比取模块更快的方式计算.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>value</code> - 可以为负数，比如 －1 mod 16 = 15</dd>
</dl>
</li>
</ul>
<a name="mod-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mod</h4>
<pre>public static&nbsp;int&nbsp;mod(int&nbsp;x,
                      int&nbsp;m)</pre>
<div class="block">保证结果为正数的取模.
 
 如果(v = x/m) <0，v+=m.</div>
</li>
</ul>
<a name="mod-long-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mod</h4>
<pre>public static&nbsp;long&nbsp;mod(long&nbsp;x,
                       long&nbsp;m)</pre>
<div class="block">保证结果为正数的取模.
 
 如果(v = x/m) <0，v+=m.</div>
</li>
</ul>
<a name="mod-long-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mod</h4>
<pre>public static&nbsp;int&nbsp;mod(long&nbsp;x,
                      int&nbsp;m)</pre>
<div class="block">保证结果为正数的取模</div>
</li>
</ul>
<a name="divide-int-int-java.math.RoundingMode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>divide</h4>
<pre>public static&nbsp;int&nbsp;divide(int&nbsp;p,
                         int&nbsp;q,
                         java.math.RoundingMode&nbsp;mode)</pre>
<div class="block">能控制rounding方向的int相除.
 
 jdk的'/'运算符，直接向下取整</div>
</li>
</ul>
<a name="divide-long-long-java.math.RoundingMode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>divide</h4>
<pre>public static&nbsp;long&nbsp;divide(long&nbsp;p,
                          long&nbsp;q,
                          java.math.RoundingMode&nbsp;mode)</pre>
<div class="block">能控制rounding方向的long相除
 
 jdk的'/'运算符，直接向下取整</div>
</li>
</ul>
<a name="pow-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pow</h4>
<pre>public static&nbsp;int&nbsp;pow(int&nbsp;b,
                      int&nbsp;k)</pre>
<div class="block">平方</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>k</code> - 平方次数, 不能为负数, k=0时返回1.</dd>
</dl>
</li>
</ul>
<a name="pow-long-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pow</h4>
<pre>public static&nbsp;long&nbsp;pow(long&nbsp;b,
                       int&nbsp;k)</pre>
<div class="block">平方</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>k</code> - 平方次数,不能为负数, k=0时返回1.</dd>
</dl>
</li>
</ul>
<a name="sqrt-int-java.math.RoundingMode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sqrt</h4>
<pre>public static&nbsp;int&nbsp;sqrt(int&nbsp;x,
                       java.math.RoundingMode&nbsp;mode)</pre>
<div class="block">开方</div>
</li>
</ul>
<a name="sqrt-long-java.math.RoundingMode-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>sqrt</h4>
<pre>public static&nbsp;long&nbsp;sqrt(long&nbsp;x,
                        java.math.RoundingMode&nbsp;mode)</pre>
<div class="block">开方</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../com/huazheng/tunny/tools/number/MathUtilTest.html" title="com.huazheng.tunny.tools.number中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/number/MathUtil.html" target="_top">框架</a></li>
<li><a href="MathUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:32 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>NumberUtil</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="NumberUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9,"i14":9,"i15":9,"i16":9,"i17":9,"i18":9,"i19":9,"i20":9,"i21":9,"i22":9,"i23":9,"i24":9,"i25":9,"i26":9,"i27":9,"i28":9,"i29":9,"i30":9,"i31":9,"i32":9,"i33":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/number/MathUtilTest.html" title="com.huazheng.tunny.tools.number中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtilTest.html" title="com.huazheng.tunny.tools.number中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/number/NumberUtil.html" target="_top">框架</a></li>
<li><a href="NumberUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.number</div>
<h2 title="类 NumberUtil" class="title">类 NumberUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.number.NumberUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">NumberUtil</span>
extends java.lang.Object</pre>
<div class="block">数字的工具类.
 
 1.原始类型数字与byte[]的双向转换(via Guava)
 
 2.判断字符串是否数字, 是否16进制字符串(via Common Lang)
 
 3.10机制/16进制字符串 与 原始类型数字/数字对象 的双向转换(参考Common Lang自写)</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#NumberUtil--">NumberUtil</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#equalsWithin-double-double-">equalsWithin</a></span>(double&nbsp;d1,
            double&nbsp;d2)</code>
<div class="block">因为double的精度问题, 允许两个double在0.00001内的误差为相等。</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#equalsWithin-double-double-double-">equalsWithin</a></span>(double&nbsp;d1,
            double&nbsp;d2,
            double&nbsp;epsilon)</code>
<div class="block">因为double的精度问题, 允许两个double在epsilon内的误差为相等</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#hexToIntObject-java.lang.String-">hexToIntObject</a></span>(java.lang.String&nbsp;str)</code>
<div class="block">将16进制的String转化为Integer.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#hexToIntObject-java.lang.String-java.lang.Integer-">hexToIntObject</a></span>(java.lang.String&nbsp;str,
              java.lang.Integer&nbsp;defaultValue)</code>
<div class="block">将16进制的String转化为Integer，出错时返回默认值.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static java.lang.Long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#hexToLongObject-java.lang.String-">hexToLongObject</a></span>(java.lang.String&nbsp;str)</code>
<div class="block">将16进制的String转化为Long
 
 当str为空或非数字字符串时抛NumberFormatException</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static java.lang.Long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#hexToLongObject-java.lang.String-java.lang.Long-">hexToLongObject</a></span>(java.lang.String&nbsp;str,
               java.lang.Long&nbsp;defaultValue)</code>
<div class="block">将16进制的String转化为Long，出错时返回默认值.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#isHexNumber-java.lang.String-">isHexNumber</a></span>(java.lang.String&nbsp;value)</code>
<div class="block">判断字符串是否16进制</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#isNumber-java.lang.String-">isNumber</a></span>(java.lang.String&nbsp;str)</code>
<div class="block">判断字符串是否合法数字</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#to2DigitString-double-">to2DigitString</a></span>(double&nbsp;d)</code>
<div class="block">输出格式化为小数后两位的double字符串</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#toBytes-double-">toBytes</a></span>(double&nbsp;val)</code>
<div class="block">copy from ElasticSearch Numbers</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#toBytes-int-">toBytes</a></span>(int&nbsp;value)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#toBytes-long-">toBytes</a></span>(long&nbsp;value)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#toDouble-byte:A-">toDouble</a></span>(byte[]&nbsp;bytes)</code>
<div class="block">copy from ElasticSearch Numbers</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#toDouble-java.lang.String-">toDouble</a></span>(java.lang.String&nbsp;str)</code>
<div class="block">将10进制的String安全的转化为double.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#toDouble-java.lang.String-double-">toDouble</a></span>(java.lang.String&nbsp;str,
        double&nbsp;defaultValue)</code>
<div class="block">将10进制的String安全的转化为double.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>static java.lang.Double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#toDoubleObject-java.lang.String-">toDoubleObject</a></span>(java.lang.String&nbsp;str)</code>
<div class="block">将10进制的String安全的转化为Double.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>static java.lang.Double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#toDoubleObject-java.lang.String-java.lang.Double-">toDoubleObject</a></span>(java.lang.String&nbsp;str,
              java.lang.Double&nbsp;defaultValue)</code>
<div class="block">将10进制的String安全的转化为Long.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#toInt-byte:A-">toInt</a></span>(byte[]&nbsp;bytes)</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#toInt-java.lang.String-">toInt</a></span>(java.lang.String&nbsp;str)</code>
<div class="block">将10进制的String转化为int.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#toInt-java.lang.String-int-">toInt</a></span>(java.lang.String&nbsp;str,
     int&nbsp;defaultValue)</code>
<div class="block">将10进制的String安全的转化为int.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#toInt32-long-">toInt32</a></span>(long&nbsp;x)</code>
<div class="block">安全的将小于Integer.MAX的long转为int，否则抛出IllegalArgumentException异常</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>static java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#toIntObject-java.lang.String-">toIntObject</a></span>(java.lang.String&nbsp;str)</code>
<div class="block">将10进制的String安全的转化为Integer.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>static java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#toIntObject-java.lang.String-java.lang.Integer-">toIntObject</a></span>(java.lang.String&nbsp;str,
           java.lang.Integer&nbsp;defaultValue)</code>
<div class="block">将10进制的String安全的转化为Integer.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#toLong-byte:A-">toLong</a></span>(byte[]&nbsp;bytes)</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#toLong-java.lang.String-">toLong</a></span>(java.lang.String&nbsp;str)</code>
<div class="block">将10进制的String安全的转化为long.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#toLong-java.lang.String-long-">toLong</a></span>(java.lang.String&nbsp;str,
      long&nbsp;defaultValue)</code>
<div class="block">将10进制的String安全的转化为long.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>static java.lang.Long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#toLongObject-java.lang.String-">toLongObject</a></span>(java.lang.String&nbsp;str)</code>
<div class="block">将10进制的String安全的转化为Long.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>static java.lang.Long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#toLongObject-java.lang.String-java.lang.Long-">toLongObject</a></span>(java.lang.String&nbsp;str,
            java.lang.Long&nbsp;defaultValue)</code>
<div class="block">将10进制的String安全的转化为Long.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#toString-double-">toString</a></span>(double&nbsp;d)</code>&nbsp;</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#toString-java.lang.Double-">toString</a></span>(java.lang.Double&nbsp;d)</code>&nbsp;</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#toString-int-">toString</a></span>(int&nbsp;i)</code>&nbsp;</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#toString-java.lang.Integer-">toString</a></span>(java.lang.Integer&nbsp;i)</code>&nbsp;</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#toString-long-">toString</a></span>(long&nbsp;l)</code>&nbsp;</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtil.html#toString-java.lang.Long-">toString</a></span>(java.lang.Long&nbsp;l)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="NumberUtil--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>NumberUtil</h4>
<pre>public&nbsp;NumberUtil()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="equalsWithin-double-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>equalsWithin</h4>
<pre>public static&nbsp;boolean&nbsp;equalsWithin(double&nbsp;d1,
                                   double&nbsp;d2)</pre>
<div class="block">因为double的精度问题, 允许两个double在0.00001内的误差为相等。</div>
</li>
</ul>
<a name="equalsWithin-double-double-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>equalsWithin</h4>
<pre>public static&nbsp;boolean&nbsp;equalsWithin(double&nbsp;d1,
                                   double&nbsp;d2,
                                   double&nbsp;epsilon)</pre>
<div class="block">因为double的精度问题, 允许两个double在epsilon内的误差为相等</div>
</li>
</ul>
<a name="toBytes-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toBytes</h4>
<pre>public static&nbsp;byte[]&nbsp;toBytes(int&nbsp;value)</pre>
</li>
</ul>
<a name="toBytes-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toBytes</h4>
<pre>public static&nbsp;byte[]&nbsp;toBytes(long&nbsp;value)</pre>
</li>
</ul>
<a name="toBytes-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toBytes</h4>
<pre>public static&nbsp;byte[]&nbsp;toBytes(double&nbsp;val)</pre>
<div class="block">copy from ElasticSearch Numbers</div>
</li>
</ul>
<a name="toInt-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toInt</h4>
<pre>public static&nbsp;int&nbsp;toInt(byte[]&nbsp;bytes)</pre>
</li>
</ul>
<a name="toLong-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toLong</h4>
<pre>public static&nbsp;long&nbsp;toLong(byte[]&nbsp;bytes)</pre>
</li>
</ul>
<a name="toDouble-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toDouble</h4>
<pre>public static&nbsp;double&nbsp;toDouble(byte[]&nbsp;bytes)</pre>
<div class="block">copy from ElasticSearch Numbers</div>
</li>
</ul>
<a name="isNumber-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isNumber</h4>
<pre>public static&nbsp;boolean&nbsp;isNumber(java.lang.String&nbsp;str)</pre>
<div class="block">判断字符串是否合法数字</div>
</li>
</ul>
<a name="isHexNumber-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isHexNumber</h4>
<pre>public static&nbsp;boolean&nbsp;isHexNumber(java.lang.String&nbsp;value)</pre>
<div class="block">判断字符串是否16进制</div>
</li>
</ul>
<a name="toInt-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toInt</h4>
<pre>public static&nbsp;int&nbsp;toInt(java.lang.String&nbsp;str)</pre>
<div class="block">将10进制的String转化为int.
 
 当str为空或非数字字符串时抛NumberFormatException</div>
</li>
</ul>
<a name="toInt-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toInt</h4>
<pre>public static&nbsp;int&nbsp;toInt(java.lang.String&nbsp;str,
                        int&nbsp;defaultValue)</pre>
<div class="block">将10进制的String安全的转化为int.
 
 当str为空或非数字字符串时，返回default值.</div>
</li>
</ul>
<a name="toLong-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toLong</h4>
<pre>public static&nbsp;long&nbsp;toLong(java.lang.String&nbsp;str)</pre>
<div class="block">将10进制的String安全的转化为long.
 
 当str或非数字字符串时抛NumberFormatException</div>
</li>
</ul>
<a name="toLong-java.lang.String-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toLong</h4>
<pre>public static&nbsp;long&nbsp;toLong(java.lang.String&nbsp;str,
                          long&nbsp;defaultValue)</pre>
<div class="block">将10进制的String安全的转化为long.
 
 当str为空或非数字字符串时，返回default值</div>
</li>
</ul>
<a name="toDouble-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toDouble</h4>
<pre>public static&nbsp;double&nbsp;toDouble(java.lang.String&nbsp;str)</pre>
<div class="block">将10进制的String安全的转化为double.
 
 当str为空或非数字字符串时抛NumberFormatException</div>
</li>
</ul>
<a name="toDouble-java.lang.String-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toDouble</h4>
<pre>public static&nbsp;double&nbsp;toDouble(java.lang.String&nbsp;str,
                              double&nbsp;defaultValue)</pre>
<div class="block">将10进制的String安全的转化为double.
 
 当str为空或非数字字符串时，返回default值</div>
</li>
</ul>
<a name="toIntObject-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toIntObject</h4>
<pre>public static&nbsp;java.lang.Integer&nbsp;toIntObject(java.lang.String&nbsp;str)</pre>
<div class="block">将10进制的String安全的转化为Integer.
 
 当str为空或非数字字符串时抛NumberFormatException</div>
</li>
</ul>
<a name="toIntObject-java.lang.String-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toIntObject</h4>
<pre>public static&nbsp;java.lang.Integer&nbsp;toIntObject(java.lang.String&nbsp;str,
                                            java.lang.Integer&nbsp;defaultValue)</pre>
<div class="block">将10进制的String安全的转化为Integer.
 
 当str为空或非数字字符串时，返回default值</div>
</li>
</ul>
<a name="toLongObject-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toLongObject</h4>
<pre>public static&nbsp;java.lang.Long&nbsp;toLongObject(java.lang.String&nbsp;str)</pre>
<div class="block">将10进制的String安全的转化为Long.
 
 当str为空或非数字字符串时抛NumberFormatException</div>
</li>
</ul>
<a name="toLongObject-java.lang.String-java.lang.Long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toLongObject</h4>
<pre>public static&nbsp;java.lang.Long&nbsp;toLongObject(java.lang.String&nbsp;str,
                                          java.lang.Long&nbsp;defaultValue)</pre>
<div class="block">将10进制的String安全的转化为Long.
 
 当str为空或非数字字符串时，返回default值</div>
</li>
</ul>
<a name="toDoubleObject-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toDoubleObject</h4>
<pre>public static&nbsp;java.lang.Double&nbsp;toDoubleObject(java.lang.String&nbsp;str)</pre>
<div class="block">将10进制的String安全的转化为Double.
 
 当str为空或非数字字符串时抛NumberFormatException</div>
</li>
</ul>
<a name="toDoubleObject-java.lang.String-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toDoubleObject</h4>
<pre>public static&nbsp;java.lang.Double&nbsp;toDoubleObject(java.lang.String&nbsp;str,
                                              java.lang.Double&nbsp;defaultValue)</pre>
<div class="block">将10进制的String安全的转化为Long.
 
 当str为空或非数字字符串时，返回default值</div>
</li>
</ul>
<a name="hexToIntObject-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hexToIntObject</h4>
<pre>public static&nbsp;java.lang.Integer&nbsp;hexToIntObject(java.lang.String&nbsp;str)</pre>
<div class="block">将16进制的String转化为Integer.
 
 当str为空或非数字字符串时抛NumberFormatException</div>
</li>
</ul>
<a name="hexToIntObject-java.lang.String-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hexToIntObject</h4>
<pre>public static&nbsp;java.lang.Integer&nbsp;hexToIntObject(java.lang.String&nbsp;str,
                                               java.lang.Integer&nbsp;defaultValue)</pre>
<div class="block">将16进制的String转化为Integer，出错时返回默认值.</div>
</li>
</ul>
<a name="hexToLongObject-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hexToLongObject</h4>
<pre>public static&nbsp;java.lang.Long&nbsp;hexToLongObject(java.lang.String&nbsp;str)</pre>
<div class="block">将16进制的String转化为Long
 
 当str为空或非数字字符串时抛NumberFormatException</div>
</li>
</ul>
<a name="hexToLongObject-java.lang.String-java.lang.Long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hexToLongObject</h4>
<pre>public static&nbsp;java.lang.Long&nbsp;hexToLongObject(java.lang.String&nbsp;str,
                                             java.lang.Long&nbsp;defaultValue)</pre>
<div class="block">将16进制的String转化为Long，出错时返回默认值.</div>
</li>
</ul>
<a name="toString-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toString</h4>
<pre>public static&nbsp;java.lang.String&nbsp;toString(int&nbsp;i)</pre>
</li>
</ul>
<a name="toString-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toString</h4>
<pre>public static&nbsp;java.lang.String&nbsp;toString(java.lang.Integer&nbsp;i)</pre>
</li>
</ul>
<a name="toString-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toString</h4>
<pre>public static&nbsp;java.lang.String&nbsp;toString(long&nbsp;l)</pre>
</li>
</ul>
<a name="toString-java.lang.Long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toString</h4>
<pre>public static&nbsp;java.lang.String&nbsp;toString(java.lang.Long&nbsp;l)</pre>
</li>
</ul>
<a name="toString-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toString</h4>
<pre>public static&nbsp;java.lang.String&nbsp;toString(double&nbsp;d)</pre>
</li>
</ul>
<a name="toString-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toString</h4>
<pre>public static&nbsp;java.lang.String&nbsp;toString(java.lang.Double&nbsp;d)</pre>
</li>
</ul>
<a name="to2DigitString-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>to2DigitString</h4>
<pre>public static&nbsp;java.lang.String&nbsp;to2DigitString(double&nbsp;d)</pre>
<div class="block">输出格式化为小数后两位的double字符串</div>
</li>
</ul>
<a name="toInt32-long-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>toInt32</h4>
<pre>public static&nbsp;int&nbsp;toInt32(long&nbsp;x)</pre>
<div class="block">安全的将小于Integer.MAX的long转为int，否则抛出IllegalArgumentException异常</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/number/MathUtilTest.html" title="com.huazheng.tunny.tools.number中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtilTest.html" title="com.huazheng.tunny.tools.number中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/number/NumberUtil.html" target="_top">框架</a></li>
<li><a href="NumberUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:31 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>DateUtil</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DateUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9,"i14":9,"i15":9,"i16":9,"i17":9,"i18":9,"i19":9,"i20":9,"i21":9,"i22":9,"i23":9,"i24":9,"i25":9,"i26":9,"i27":9,"i28":9,"i29":9,"i30":9,"i31":9,"i32":9,"i33":9,"i34":9,"i35":9,"i36":9,"i37":9,"i38":9,"i39":9,"i40":9,"i41":9,"i42":9,"i43":9,"i44":9,"i45":9,"i46":9,"i47":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/time/DateFormatUtilTest.html" title="com.huazheng.tunny.tools.time中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/time/DateUtilTest.html" title="com.huazheng.tunny.tools.time中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/time/DateUtil.html" target="_top">框架</a></li>
<li><a href="DateUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.time</div>
<h2 title="类 DateUtil" class="title">类 DateUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.time.DateUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">DateUtil</span>
extends java.lang.Object</pre>
<div class="block">日期工具类.
 
 在不方便使用joda-time时，使用本类降低Date处理的复杂度与性能消耗, 封装Common Lang及移植Jodd的最常用日期方法</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#MILLIS_PER_DAY">MILLIS_PER_DAY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#MILLIS_PER_HOUR">MILLIS_PER_HOUR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#MILLIS_PER_MINUTE">MILLIS_PER_MINUTE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#MILLIS_PER_SECOND">MILLIS_PER_SECOND</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#DateUtil--">DateUtil</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#addDays-java.util.Date-int-">addDays</a></span>(java.util.Date&nbsp;date,
       int&nbsp;amount)</code>
<div class="block">加一天</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#addHours-java.util.Date-int-">addHours</a></span>(java.util.Date&nbsp;date,
        int&nbsp;amount)</code>
<div class="block">加一小时</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#addMinutes-java.util.Date-int-">addMinutes</a></span>(java.util.Date&nbsp;date,
          int&nbsp;amount)</code>
<div class="block">加一分钟</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#addMonths-java.util.Date-int-">addMonths</a></span>(java.util.Date&nbsp;date,
         int&nbsp;amount)</code>
<div class="block">加一月</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#addSeconds-java.util.Date-int-">addSeconds</a></span>(java.util.Date&nbsp;date,
          int&nbsp;amount)</code>
<div class="block">终于到了，续一秒.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#addWeeks-java.util.Date-int-">addWeeks</a></span>(java.util.Date&nbsp;date,
        int&nbsp;amount)</code>
<div class="block">加一周</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#beginOfDate-java.util.Date-">beginOfDate</a></span>(java.util.Date&nbsp;date)</code>
<div class="block">2016-11-10 07:33:23, 则返回2016-11-10 00:00:00</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#beginOfHour-java.util.Date-">beginOfHour</a></span>(java.util.Date&nbsp;date)</code>
<div class="block">2016-12-10 07:33:23, 则返回2016-12-10 07:00:00</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#beginOfMinute-java.util.Date-">beginOfMinute</a></span>(java.util.Date&nbsp;date)</code>
<div class="block">2016-12-10 07:33:23, 则返回2016-12-10 07:33:00</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#beginOfMonth-java.util.Date-">beginOfMonth</a></span>(java.util.Date&nbsp;date)</code>
<div class="block">2016-11-10 07:33:23, 则返回2016-11-1 00:00:00</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#beginOfWeek-java.util.Date-">beginOfWeek</a></span>(java.util.Date&nbsp;date)</code>
<div class="block">2017-1-20 07:33:23, 则返回2017-1-16 00:00:00</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#beginOfYear-java.util.Date-">beginOfYear</a></span>(java.util.Date&nbsp;date)</code>
<div class="block">2016-11-10 07:33:23, 则返回2016-1-1 00:00:00</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#endOfDate-java.util.Date-">endOfDate</a></span>(java.util.Date&nbsp;date)</code>
<div class="block">2017-1-23 07:33:23, 则返回2017-1-23 23:59:59.999</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#endOfHour-java.util.Date-">endOfHour</a></span>(java.util.Date&nbsp;date)</code>
<div class="block">2017-1-23 07:33:23, 则返回2017-1-23 07:59:59.999</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#endOfMinute-java.util.Date-">endOfMinute</a></span>(java.util.Date&nbsp;date)</code>
<div class="block">2017-1-23 07:33:23, 则返回2017-1-23 07:33:59.999</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#endOfMonth-java.util.Date-">endOfMonth</a></span>(java.util.Date&nbsp;date)</code>
<div class="block">2016-11-10 07:33:23, 则返回2016-11-30 23:59:59.999</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#endOfWeek-java.util.Date-">endOfWeek</a></span>(java.util.Date&nbsp;date)</code>
<div class="block">2017-1-20 07:33:23, 则返回2017-1-22 23:59:59.999</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#endOfYear-java.util.Date-">endOfYear</a></span>(java.util.Date&nbsp;date)</code>
<div class="block">2016-11-10 07:33:23, 则返回2016-12-31 23:59:59.999</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#getDayOfWeek-java.util.Date-">getDayOfWeek</a></span>(java.util.Date&nbsp;date)</code>
<div class="block">获得日期是一周的第几天.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#getDayOfYear-java.util.Date-">getDayOfYear</a></span>(java.util.Date&nbsp;date)</code>
<div class="block">获得日期是一年的第几天，返回值从1开始</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#getMonthLength-java.util.Date-">getMonthLength</a></span>(java.util.Date&nbsp;date)</code>
<div class="block">获取某个月有多少天, 考虑闰年等因数, 移植Jodd Core的TimeUtil</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#getMonthLength-int-int-">getMonthLength</a></span>(int&nbsp;year,
              int&nbsp;month)</code>
<div class="block">获取某个月有多少天, 考虑闰年等因数, 移植Jodd Core的TimeUtil</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#getWeekOfMonth-java.util.Date-">getWeekOfMonth</a></span>(java.util.Date&nbsp;date)</code>
<div class="block">获得日期是一月的第几周，返回值从1开始.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#getWeekOfYear-java.util.Date-">getWeekOfYear</a></span>(java.util.Date&nbsp;date)</code>
<div class="block">获得日期是一年的第几周，返回值从1开始.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#isBetween-java.util.Date-java.util.Date-java.util.Date-">isBetween</a></span>(java.util.Date&nbsp;date,
         java.util.Date&nbsp;start,
         java.util.Date&nbsp;end)</code>
<div class="block">判断日期是否在范围内，包含相等的日期</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#isLeapYear-java.util.Date-">isLeapYear</a></span>(java.util.Date&nbsp;date)</code>
<div class="block">是否闰年.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#isLeapYear-int-">isLeapYear</a></span>(int&nbsp;y)</code>
<div class="block">是否闰年，copy from Jodd Core的TimeUtil
 
 参数是公元计数, 如2016</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#isSameDay-java.util.Date-java.util.Date-">isSameDay</a></span>(java.util.Date&nbsp;date1,
         java.util.Date&nbsp;date2)</code>
<div class="block">是否同一天.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#isSameTime-java.util.Date-java.util.Date-">isSameTime</a></span>(java.util.Date&nbsp;date1,
          java.util.Date&nbsp;date2)</code>
<div class="block">是否同一时刻.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#nextDate-java.util.Date-">nextDate</a></span>(java.util.Date&nbsp;date)</code>
<div class="block">2016-11-10 07:33:23, 则返回2016-11-11 00:00:00</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#nextHour-java.util.Date-">nextHour</a></span>(java.util.Date&nbsp;date)</code>
<div class="block">2016-12-10 07:33:23, 则返回2016-12-10 08:00:00</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#nextMinute-java.util.Date-">nextMinute</a></span>(java.util.Date&nbsp;date)</code>
<div class="block">2016-12-10 07:33:23, 则返回2016-12-10 07:34:00</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#nextMonth-java.util.Date-">nextMonth</a></span>(java.util.Date&nbsp;date)</code>
<div class="block">2016-11-10 07:33:23, 则返回2016-12-1 00:00:00</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#nextWeek-java.util.Date-">nextWeek</a></span>(java.util.Date&nbsp;date)</code>
<div class="block">2017-1-23 07:33:23, 则返回2017-1-22 00:00:00</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#nextYear-java.util.Date-">nextYear</a></span>(java.util.Date&nbsp;date)</code>
<div class="block">2016-11-10 07:33:23, 则返回2017-1-1 00:00:00</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#setDays-java.util.Date-int-">setDays</a></span>(java.util.Date&nbsp;date,
       int&nbsp;amount)</code>
<div class="block">设置日期, 1-31.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#setHours-java.util.Date-int-">setHours</a></span>(java.util.Date&nbsp;date,
        int&nbsp;amount)</code>
<div class="block">设置小时, 0-23.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#setMilliseconds-java.util.Date-int-">setMilliseconds</a></span>(java.util.Date&nbsp;date,
               int&nbsp;amount)</code>
<div class="block">设置毫秒.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#setMinutes-java.util.Date-int-">setMinutes</a></span>(java.util.Date&nbsp;date,
          int&nbsp;amount)</code>
<div class="block">设置分钟, 0-59.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#setMonths-java.util.Date-int-">setMonths</a></span>(java.util.Date&nbsp;date,
         int&nbsp;amount)</code>
<div class="block">设置月份, 1-12.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#setSeconds-java.util.Date-int-">setSeconds</a></span>(java.util.Date&nbsp;date,
          int&nbsp;amount)</code>
<div class="block">设置秒, 0-59.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#setYears-java.util.Date-int-">setYears</a></span>(java.util.Date&nbsp;date,
        int&nbsp;amount)</code>
<div class="block">设置年份, 公元纪年.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#subDays-java.util.Date-int-">subDays</a></span>(java.util.Date&nbsp;date,
       int&nbsp;amount)</code>
<div class="block">减一天</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#subHours-java.util.Date-int-">subHours</a></span>(java.util.Date&nbsp;date,
        int&nbsp;amount)</code>
<div class="block">减一小时</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#subMinutes-java.util.Date-int-">subMinutes</a></span>(java.util.Date&nbsp;date,
          int&nbsp;amount)</code>
<div class="block">减一分钟</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#subMonths-java.util.Date-int-">subMonths</a></span>(java.util.Date&nbsp;date,
         int&nbsp;amount)</code>
<div class="block">减一月</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#subSeconds-java.util.Date-int-">subSeconds</a></span>(java.util.Date&nbsp;date,
          int&nbsp;amount)</code>
<div class="block">减一秒.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html#subWeeks-java.util.Date-int-">subWeeks</a></span>(java.util.Date&nbsp;date,
        int&nbsp;amount)</code>
<div class="block">减一周</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="MILLIS_PER_SECOND">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MILLIS_PER_SECOND</h4>
<pre>public static final&nbsp;long MILLIS_PER_SECOND</pre>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.huazheng.tunny.tools.time.DateUtil.MILLIS_PER_SECOND">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="MILLIS_PER_MINUTE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MILLIS_PER_MINUTE</h4>
<pre>public static final&nbsp;long MILLIS_PER_MINUTE</pre>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.huazheng.tunny.tools.time.DateUtil.MILLIS_PER_MINUTE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="MILLIS_PER_HOUR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MILLIS_PER_HOUR</h4>
<pre>public static final&nbsp;long MILLIS_PER_HOUR</pre>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.huazheng.tunny.tools.time.DateUtil.MILLIS_PER_HOUR">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="MILLIS_PER_DAY">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>MILLIS_PER_DAY</h4>
<pre>public static final&nbsp;long MILLIS_PER_DAY</pre>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.huazheng.tunny.tools.time.DateUtil.MILLIS_PER_DAY">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="DateUtil--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DateUtil</h4>
<pre>public&nbsp;DateUtil()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="isSameDay-java.util.Date-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isSameDay</h4>
<pre>public static&nbsp;boolean&nbsp;isSameDay(java.util.Date&nbsp;date1,
                                java.util.Date&nbsp;date2)</pre>
<div class="block">是否同一天.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>DateUtils.isSameDay(Date, Date)</code></dd>
</dl>
</li>
</ul>
<a name="isSameTime-java.util.Date-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isSameTime</h4>
<pre>public static&nbsp;boolean&nbsp;isSameTime(java.util.Date&nbsp;date1,
                                 java.util.Date&nbsp;date2)</pre>
<div class="block">是否同一时刻.</div>
</li>
</ul>
<a name="isBetween-java.util.Date-java.util.Date-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBetween</h4>
<pre>public static&nbsp;boolean&nbsp;isBetween(java.util.Date&nbsp;date,
                                java.util.Date&nbsp;start,
                                java.util.Date&nbsp;end)</pre>
<div class="block">判断日期是否在范围内，包含相等的日期</div>
</li>
</ul>
<a name="addMonths-java.util.Date-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addMonths</h4>
<pre>public static&nbsp;java.util.Date&nbsp;addMonths(java.util.Date&nbsp;date,
                                       int&nbsp;amount)</pre>
<div class="block">加一月</div>
</li>
</ul>
<a name="subMonths-java.util.Date-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>subMonths</h4>
<pre>public static&nbsp;java.util.Date&nbsp;subMonths(java.util.Date&nbsp;date,
                                       int&nbsp;amount)</pre>
<div class="block">减一月</div>
</li>
</ul>
<a name="addWeeks-java.util.Date-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addWeeks</h4>
<pre>public static&nbsp;java.util.Date&nbsp;addWeeks(java.util.Date&nbsp;date,
                                      int&nbsp;amount)</pre>
<div class="block">加一周</div>
</li>
</ul>
<a name="subWeeks-java.util.Date-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>subWeeks</h4>
<pre>public static&nbsp;java.util.Date&nbsp;subWeeks(java.util.Date&nbsp;date,
                                      int&nbsp;amount)</pre>
<div class="block">减一周</div>
</li>
</ul>
<a name="addDays-java.util.Date-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addDays</h4>
<pre>public static&nbsp;java.util.Date&nbsp;addDays(java.util.Date&nbsp;date,
                                     int&nbsp;amount)</pre>
<div class="block">加一天</div>
</li>
</ul>
<a name="subDays-java.util.Date-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>subDays</h4>
<pre>public static&nbsp;java.util.Date&nbsp;subDays(java.util.Date&nbsp;date,
                                     int&nbsp;amount)</pre>
<div class="block">减一天</div>
</li>
</ul>
<a name="addHours-java.util.Date-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addHours</h4>
<pre>public static&nbsp;java.util.Date&nbsp;addHours(java.util.Date&nbsp;date,
                                      int&nbsp;amount)</pre>
<div class="block">加一小时</div>
</li>
</ul>
<a name="subHours-java.util.Date-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>subHours</h4>
<pre>public static&nbsp;java.util.Date&nbsp;subHours(java.util.Date&nbsp;date,
                                      int&nbsp;amount)</pre>
<div class="block">减一小时</div>
</li>
</ul>
<a name="addMinutes-java.util.Date-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addMinutes</h4>
<pre>public static&nbsp;java.util.Date&nbsp;addMinutes(java.util.Date&nbsp;date,
                                        int&nbsp;amount)</pre>
<div class="block">加一分钟</div>
</li>
</ul>
<a name="subMinutes-java.util.Date-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>subMinutes</h4>
<pre>public static&nbsp;java.util.Date&nbsp;subMinutes(java.util.Date&nbsp;date,
                                        int&nbsp;amount)</pre>
<div class="block">减一分钟</div>
</li>
</ul>
<a name="addSeconds-java.util.Date-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addSeconds</h4>
<pre>public static&nbsp;java.util.Date&nbsp;addSeconds(java.util.Date&nbsp;date,
                                        int&nbsp;amount)</pre>
<div class="block">终于到了，续一秒.</div>
</li>
</ul>
<a name="subSeconds-java.util.Date-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>subSeconds</h4>
<pre>public static&nbsp;java.util.Date&nbsp;subSeconds(java.util.Date&nbsp;date,
                                        int&nbsp;amount)</pre>
<div class="block">减一秒.</div>
</li>
</ul>
<a name="setYears-java.util.Date-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setYears</h4>
<pre>public static&nbsp;java.util.Date&nbsp;setYears(java.util.Date&nbsp;date,
                                      int&nbsp;amount)</pre>
<div class="block">设置年份, 公元纪年.</div>
</li>
</ul>
<a name="setMonths-java.util.Date-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMonths</h4>
<pre>public static&nbsp;java.util.Date&nbsp;setMonths(java.util.Date&nbsp;date,
                                       int&nbsp;amount)</pre>
<div class="block">设置月份, 1-12.</div>
</li>
</ul>
<a name="setDays-java.util.Date-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDays</h4>
<pre>public static&nbsp;java.util.Date&nbsp;setDays(java.util.Date&nbsp;date,
                                     int&nbsp;amount)</pre>
<div class="block">设置日期, 1-31.</div>
</li>
</ul>
<a name="setHours-java.util.Date-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHours</h4>
<pre>public static&nbsp;java.util.Date&nbsp;setHours(java.util.Date&nbsp;date,
                                      int&nbsp;amount)</pre>
<div class="block">设置小时, 0-23.</div>
</li>
</ul>
<a name="setMinutes-java.util.Date-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMinutes</h4>
<pre>public static&nbsp;java.util.Date&nbsp;setMinutes(java.util.Date&nbsp;date,
                                        int&nbsp;amount)</pre>
<div class="block">设置分钟, 0-59.</div>
</li>
</ul>
<a name="setSeconds-java.util.Date-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSeconds</h4>
<pre>public static&nbsp;java.util.Date&nbsp;setSeconds(java.util.Date&nbsp;date,
                                        int&nbsp;amount)</pre>
<div class="block">设置秒, 0-59.</div>
</li>
</ul>
<a name="setMilliseconds-java.util.Date-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMilliseconds</h4>
<pre>public static&nbsp;java.util.Date&nbsp;setMilliseconds(java.util.Date&nbsp;date,
                                             int&nbsp;amount)</pre>
<div class="block">设置毫秒.</div>
</li>
</ul>
<a name="getDayOfWeek-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDayOfWeek</h4>
<pre>public static&nbsp;int&nbsp;getDayOfWeek(java.util.Date&nbsp;date)</pre>
<div class="block">获得日期是一周的第几天. 已改为中国习惯，1 是Monday，而不是Sundays.</div>
</li>
</ul>
<a name="getDayOfYear-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDayOfYear</h4>
<pre>public static&nbsp;int&nbsp;getDayOfYear(java.util.Date&nbsp;date)</pre>
<div class="block">获得日期是一年的第几天，返回值从1开始</div>
</li>
</ul>
<a name="getWeekOfMonth-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWeekOfMonth</h4>
<pre>public static&nbsp;int&nbsp;getWeekOfMonth(java.util.Date&nbsp;date)</pre>
<div class="block">获得日期是一月的第几周，返回值从1开始.
 
 开始的一周，只要有一天在那个月里都算. 已改为中国习惯，1 是Monday，而不是Sunday</div>
</li>
</ul>
<a name="getWeekOfYear-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWeekOfYear</h4>
<pre>public static&nbsp;int&nbsp;getWeekOfYear(java.util.Date&nbsp;date)</pre>
<div class="block">获得日期是一年的第几周，返回值从1开始.
 
 开始的一周，只要有一天在那一年里都算.已改为中国习惯，1 是Monday，而不是Sunday</div>
</li>
</ul>
<a name="beginOfYear-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>beginOfYear</h4>
<pre>public static&nbsp;java.util.Date&nbsp;beginOfYear(java.util.Date&nbsp;date)</pre>
<div class="block">2016-11-10 07:33:23, 则返回2016-1-1 00:00:00</div>
</li>
</ul>
<a name="endOfYear-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>endOfYear</h4>
<pre>public static&nbsp;java.util.Date&nbsp;endOfYear(java.util.Date&nbsp;date)</pre>
<div class="block">2016-11-10 07:33:23, 则返回2016-12-31 23:59:59.999</div>
</li>
</ul>
<a name="nextYear-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nextYear</h4>
<pre>public static&nbsp;java.util.Date&nbsp;nextYear(java.util.Date&nbsp;date)</pre>
<div class="block">2016-11-10 07:33:23, 则返回2017-1-1 00:00:00</div>
</li>
</ul>
<a name="beginOfMonth-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>beginOfMonth</h4>
<pre>public static&nbsp;java.util.Date&nbsp;beginOfMonth(java.util.Date&nbsp;date)</pre>
<div class="block">2016-11-10 07:33:23, 则返回2016-11-1 00:00:00</div>
</li>
</ul>
<a name="endOfMonth-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>endOfMonth</h4>
<pre>public static&nbsp;java.util.Date&nbsp;endOfMonth(java.util.Date&nbsp;date)</pre>
<div class="block">2016-11-10 07:33:23, 则返回2016-11-30 23:59:59.999</div>
</li>
</ul>
<a name="nextMonth-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nextMonth</h4>
<pre>public static&nbsp;java.util.Date&nbsp;nextMonth(java.util.Date&nbsp;date)</pre>
<div class="block">2016-11-10 07:33:23, 则返回2016-12-1 00:00:00</div>
</li>
</ul>
<a name="beginOfWeek-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>beginOfWeek</h4>
<pre>public static&nbsp;java.util.Date&nbsp;beginOfWeek(java.util.Date&nbsp;date)</pre>
<div class="block">2017-1-20 07:33:23, 则返回2017-1-16 00:00:00</div>
</li>
</ul>
<a name="endOfWeek-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>endOfWeek</h4>
<pre>public static&nbsp;java.util.Date&nbsp;endOfWeek(java.util.Date&nbsp;date)</pre>
<div class="block">2017-1-20 07:33:23, 则返回2017-1-22 23:59:59.999</div>
</li>
</ul>
<a name="nextWeek-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nextWeek</h4>
<pre>public static&nbsp;java.util.Date&nbsp;nextWeek(java.util.Date&nbsp;date)</pre>
<div class="block">2017-1-23 07:33:23, 则返回2017-1-22 00:00:00</div>
</li>
</ul>
<a name="beginOfDate-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>beginOfDate</h4>
<pre>public static&nbsp;java.util.Date&nbsp;beginOfDate(java.util.Date&nbsp;date)</pre>
<div class="block">2016-11-10 07:33:23, 则返回2016-11-10 00:00:00</div>
</li>
</ul>
<a name="endOfDate-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>endOfDate</h4>
<pre>public static&nbsp;java.util.Date&nbsp;endOfDate(java.util.Date&nbsp;date)</pre>
<div class="block">2017-1-23 07:33:23, 则返回2017-1-23 23:59:59.999</div>
</li>
</ul>
<a name="nextDate-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nextDate</h4>
<pre>public static&nbsp;java.util.Date&nbsp;nextDate(java.util.Date&nbsp;date)</pre>
<div class="block">2016-11-10 07:33:23, 则返回2016-11-11 00:00:00</div>
</li>
</ul>
<a name="beginOfHour-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>beginOfHour</h4>
<pre>public static&nbsp;java.util.Date&nbsp;beginOfHour(java.util.Date&nbsp;date)</pre>
<div class="block">2016-12-10 07:33:23, 则返回2016-12-10 07:00:00</div>
</li>
</ul>
<a name="endOfHour-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>endOfHour</h4>
<pre>public static&nbsp;java.util.Date&nbsp;endOfHour(java.util.Date&nbsp;date)</pre>
<div class="block">2017-1-23 07:33:23, 则返回2017-1-23 07:59:59.999</div>
</li>
</ul>
<a name="nextHour-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nextHour</h4>
<pre>public static&nbsp;java.util.Date&nbsp;nextHour(java.util.Date&nbsp;date)</pre>
<div class="block">2016-12-10 07:33:23, 则返回2016-12-10 08:00:00</div>
</li>
</ul>
<a name="beginOfMinute-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>beginOfMinute</h4>
<pre>public static&nbsp;java.util.Date&nbsp;beginOfMinute(java.util.Date&nbsp;date)</pre>
<div class="block">2016-12-10 07:33:23, 则返回2016-12-10 07:33:00</div>
</li>
</ul>
<a name="endOfMinute-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>endOfMinute</h4>
<pre>public static&nbsp;java.util.Date&nbsp;endOfMinute(java.util.Date&nbsp;date)</pre>
<div class="block">2017-1-23 07:33:23, 则返回2017-1-23 07:33:59.999</div>
</li>
</ul>
<a name="nextMinute-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nextMinute</h4>
<pre>public static&nbsp;java.util.Date&nbsp;nextMinute(java.util.Date&nbsp;date)</pre>
<div class="block">2016-12-10 07:33:23, 则返回2016-12-10 07:34:00</div>
</li>
</ul>
<a name="isLeapYear-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isLeapYear</h4>
<pre>public static&nbsp;boolean&nbsp;isLeapYear(java.util.Date&nbsp;date)</pre>
<div class="block">是否闰年.</div>
</li>
</ul>
<a name="isLeapYear-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isLeapYear</h4>
<pre>public static&nbsp;boolean&nbsp;isLeapYear(int&nbsp;y)</pre>
<div class="block">是否闰年，copy from Jodd Core的TimeUtil
 
 参数是公元计数, 如2016</div>
</li>
</ul>
<a name="getMonthLength-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMonthLength</h4>
<pre>public static&nbsp;int&nbsp;getMonthLength(java.util.Date&nbsp;date)</pre>
<div class="block">获取某个月有多少天, 考虑闰年等因数, 移植Jodd Core的TimeUtil</div>
</li>
</ul>
<a name="getMonthLength-int-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getMonthLength</h4>
<pre>public static&nbsp;int&nbsp;getMonthLength(int&nbsp;year,
                                 int&nbsp;month)</pre>
<div class="block">获取某个月有多少天, 考虑闰年等因数, 移植Jodd Core的TimeUtil</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/time/DateFormatUtilTest.html" title="com.huazheng.tunny.tools.time中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/time/DateUtilTest.html" title="com.huazheng.tunny.tools.time中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/time/DateUtil.html" target="_top">框架</a></li>
<li><a href="DateUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

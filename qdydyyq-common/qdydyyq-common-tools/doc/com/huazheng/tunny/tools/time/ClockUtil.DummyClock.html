<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:31 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ClockUtil.DummyClock</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ClockUtil.DummyClock";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtil.DefaultClock.html" title="com.huazheng.tunny.tools.time中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtilTest.html" title="com.huazheng.tunny.tools.time中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/time/ClockUtil.DummyClock.html" target="_top">框架</a></li>
<li><a href="ClockUtil.DummyClock.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.time</div>
<h2 title="类 ClockUtil.DummyClock" class="title">类 ClockUtil.DummyClock</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.time.ClockUtil.DummyClock</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtil.Clock.html" title="com.huazheng.tunny.tools.time中的接口">ClockUtil.Clock</a></dd>
</dl>
<dl>
<dt>封闭类:</dt>
<dd><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtil.html" title="com.huazheng.tunny.tools.time中的类">ClockUtil</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">ClockUtil.DummyClock</span>
extends java.lang.Object
implements <a href="../../../../../com/huazheng/tunny/tools/time/ClockUtil.Clock.html" title="com.huazheng.tunny.tools.time中的接口">ClockUtil.Clock</a></pre>
<div class="block">可配置的时间提供者，用于测试.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtil.DummyClock.html#DummyClock--">DummyClock</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtil.DummyClock.html#DummyClock-java.util.Date-">DummyClock</a></span>(java.util.Date&nbsp;date)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtil.DummyClock.html#DummyClock-long-">DummyClock</a></span>(long&nbsp;time)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtil.DummyClock.html#currentDate--">currentDate</a></span>()</code>
<div class="block">系统当前时间</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtil.DummyClock.html#currentTimeMillis--">currentTimeMillis</a></span>()</code>
<div class="block">系统当前时间戳</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtil.DummyClock.html#decreaseTime-int-">decreaseTime</a></span>(int&nbsp;millis)</code>
<div class="block">滚动时间.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtil.DummyClock.html#increaseTime-int-">increaseTime</a></span>(int&nbsp;millis)</code>
<div class="block">滚动时间.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtil.DummyClock.html#nanoTime--">nanoTime</a></span>()</code>
<div class="block">获取nanotime</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtil.DummyClock.html#setNanoTime-long-">setNanoTime</a></span>(long&nbsp;nanoTime)</code>
<div class="block">设置nanotime.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtil.DummyClock.html#updateNow-java.util.Date-">updateNow</a></span>(java.util.Date&nbsp;newDate)</code>
<div class="block">重新设置日期.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtil.DummyClock.html#updateNow-long-">updateNow</a></span>(long&nbsp;newTime)</code>
<div class="block">重新设置时间.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="DummyClock--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DummyClock</h4>
<pre>public&nbsp;DummyClock()</pre>
</li>
</ul>
<a name="DummyClock-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DummyClock</h4>
<pre>public&nbsp;DummyClock(java.util.Date&nbsp;date)</pre>
</li>
</ul>
<a name="DummyClock-long-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DummyClock</h4>
<pre>public&nbsp;DummyClock(long&nbsp;time)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="currentDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>currentDate</h4>
<pre>public&nbsp;java.util.Date&nbsp;currentDate()</pre>
<div class="block"><span class="descfrmTypeLabel">从接口复制的说明:&nbsp;<code><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtil.Clock.html#currentDate--">ClockUtil.Clock</a></code></span></div>
<div class="block">系统当前时间</div>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtil.Clock.html#currentDate--">currentDate</a></code>&nbsp;在接口中&nbsp;<code><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtil.Clock.html" title="com.huazheng.tunny.tools.time中的接口">ClockUtil.Clock</a></code></dd>
</dl>
</li>
</ul>
<a name="currentTimeMillis--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>currentTimeMillis</h4>
<pre>public&nbsp;long&nbsp;currentTimeMillis()</pre>
<div class="block"><span class="descfrmTypeLabel">从接口复制的说明:&nbsp;<code><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtil.Clock.html#currentTimeMillis--">ClockUtil.Clock</a></code></span></div>
<div class="block">系统当前时间戳</div>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtil.Clock.html#currentTimeMillis--">currentTimeMillis</a></code>&nbsp;在接口中&nbsp;<code><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtil.Clock.html" title="com.huazheng.tunny.tools.time中的接口">ClockUtil.Clock</a></code></dd>
</dl>
</li>
</ul>
<a name="nanoTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nanoTime</h4>
<pre>public&nbsp;long&nbsp;nanoTime()</pre>
<div class="block">获取nanotime</div>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtil.Clock.html#nanoTime--">nanoTime</a></code>&nbsp;在接口中&nbsp;<code><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtil.Clock.html" title="com.huazheng.tunny.tools.time中的接口">ClockUtil.Clock</a></code></dd>
</dl>
</li>
</ul>
<a name="updateNow-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateNow</h4>
<pre>public&nbsp;void&nbsp;updateNow(java.util.Date&nbsp;newDate)</pre>
<div class="block">重新设置日期.</div>
</li>
</ul>
<a name="updateNow-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateNow</h4>
<pre>public&nbsp;void&nbsp;updateNow(long&nbsp;newTime)</pre>
<div class="block">重新设置时间.</div>
</li>
</ul>
<a name="increaseTime-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>increaseTime</h4>
<pre>public&nbsp;void&nbsp;increaseTime(int&nbsp;millis)</pre>
<div class="block">滚动时间.</div>
</li>
</ul>
<a name="decreaseTime-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>decreaseTime</h4>
<pre>public&nbsp;void&nbsp;decreaseTime(int&nbsp;millis)</pre>
<div class="block">滚动时间.</div>
</li>
</ul>
<a name="setNanoTime-long-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setNanoTime</h4>
<pre>public&nbsp;void&nbsp;setNanoTime(long&nbsp;nanoTime)</pre>
<div class="block">设置nanotime.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtil.DefaultClock.html" title="com.huazheng.tunny.tools.time中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtilTest.html" title="com.huazheng.tunny.tools.time中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/time/ClockUtil.DummyClock.html" target="_top">框架</a></li>
<li><a href="ClockUtil.DummyClock.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

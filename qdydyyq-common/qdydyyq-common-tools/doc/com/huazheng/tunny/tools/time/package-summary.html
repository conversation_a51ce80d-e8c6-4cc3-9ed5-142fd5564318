<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:35 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>com.huazheng.tunny.tools.time</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.huazheng.tunny.tools.time";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li class="navBarCell1Rev">程序包</li>
<li>类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/text/package-summary.html">上一个程序包</a></li>
<li><a href="../../../../../com/vip/vjtools/test/data/package-summary.html">下一个程序包</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/time/package-summary.html" target="_top">框架</a></li>
<li><a href="package-summary.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="程序包" class="title">程序包&nbsp;com.huazheng.tunny.tools.time</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="接口概要表, 列表接口和解释">
<caption><span>接口概要</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">接口</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtil.Clock.html" title="com.huazheng.tunny.tools.time中的接口">ClockUtil.Clock</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="类概要表, 列表类和解释">
<caption><span>类概要</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">类</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/time/CachingDateFormatter.html" title="com.huazheng.tunny.tools.time中的类">CachingDateFormatter</a></td>
<td class="colLast">
<div class="block">DateFormat.format()消耗较大，如果时间戳是递增的，而且同一单位内有多次format()，使用用本类减少重复调用.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/time/CachingDatFormatterTest.html" title="com.huazheng.tunny.tools.time中的类">CachingDatFormatterTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtil.html" title="com.huazheng.tunny.tools.time中的类">ClockUtil</a></td>
<td class="colLast">
<div class="block">日期提供者, 使用它而不是直接取得系统时间, 方便测试.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtil.DefaultClock.html" title="com.huazheng.tunny.tools.time中的类">ClockUtil.DefaultClock</a></td>
<td class="colLast">
<div class="block">默认时间提供者，返回当前的时间，线程安全。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtil.DummyClock.html" title="com.huazheng.tunny.tools.time中的类">ClockUtil.DummyClock</a></td>
<td class="colLast">
<div class="block">可配置的时间提供者，用于测试.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtilTest.html" title="com.huazheng.tunny.tools.time中的类">ClockUtilTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/time/DateFormatUtil.html" title="com.huazheng.tunny.tools.time中的类">DateFormatUtil</a></td>
<td class="colLast">
<div class="block">Date的parse()与format(), 采用Apache Common Lang中线程安全, 性能更佳的FastDateFormat
 
 注意Common Lang版本，3.5版才使用StringBuilder，3.4及以前使用StringBuffer.
 
 1.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/time/DateFormatUtilTest.html" title="com.huazheng.tunny.tools.time中的类">DateFormatUtilTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></td>
<td class="colLast">
<div class="block">日期工具类.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/time/DateUtilTest.html" title="com.huazheng.tunny.tools.time中的类">DateUtilTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li class="navBarCell1Rev">程序包</li>
<li>类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/text/package-summary.html">上一个程序包</a></li>
<li><a href="../../../../../com/vip/vjtools/test/data/package-summary.html">下一个程序包</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/time/package-summary.html" target="_top">框架</a></li>
<li><a href="package-summary.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

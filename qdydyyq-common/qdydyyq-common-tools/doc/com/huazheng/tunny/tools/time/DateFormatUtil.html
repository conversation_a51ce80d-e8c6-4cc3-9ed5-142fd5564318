<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:31 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>DateFormatUtil</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DateFormatUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtilTest.html" title="com.huazheng.tunny.tools.time中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/time/DateFormatUtilTest.html" title="com.huazheng.tunny.tools.time中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/time/DateFormatUtil.html" target="_top">框架</a></li>
<li><a href="DateFormatUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.time</div>
<h2 title="类 DateFormatUtil" class="title">类 DateFormatUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.time.DateFormatUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">DateFormatUtil</span>
extends java.lang.Object</pre>
<div class="block">Date的parse()与format(), 采用Apache Common Lang中线程安全, 性能更佳的FastDateFormat
 
 注意Common Lang版本，3.5版才使用StringBuilder，3.4及以前使用StringBuffer.
 
 1. 常用格式的FastDateFormat定义, 常用格式直接使用这些FastDateFormat
 
 2. 日期格式不固定时的String<->Date 转换函数.
 
 3. 打印时间间隔，如"01:10:10"，以及用户友好的版本，比如"刚刚"，"10分钟前"</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>FastDateFormat.parse(String)</code>, 
<code>FastDateFormat.format(java.util.Date)</code>, 
<code>FastDateFormat.format(long)</code></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static org.apache.commons.lang3.time.FastDateFormat</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateFormatUtil.html#DEFAULT_FORMAT">DEFAULT_FORMAT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static org.apache.commons.lang3.time.FastDateFormat</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateFormatUtil.html#DEFAULT_ON_SECOND_FORMAT">DEFAULT_ON_SECOND_FORMAT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static org.apache.commons.lang3.time.FastDateFormat</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateFormatUtil.html#ISO_FORMAT">ISO_FORMAT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static org.apache.commons.lang3.time.FastDateFormat</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateFormatUtil.html#ISO_ON_DATE_FORMAT">ISO_ON_DATE_FORMAT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static org.apache.commons.lang3.time.FastDateFormat</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateFormatUtil.html#ISO_ON_SECOND_FORMAT">ISO_ON_SECOND_FORMAT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateFormatUtil.html#PATTERN_DEFAULT">PATTERN_DEFAULT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateFormatUtil.html#PATTERN_DEFAULT_ON_SECOND">PATTERN_DEFAULT_ON_SECOND</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateFormatUtil.html#PATTERN_ISO">PATTERN_ISO</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateFormatUtil.html#PATTERN_ISO_ON_DATE">PATTERN_ISO_ON_DATE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateFormatUtil.html#PATTERN_ISO_ON_SECOND">PATTERN_ISO_ON_SECOND</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateFormatUtil.html#DateFormatUtil--">DateFormatUtil</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateFormatUtil.html#formatDate-java.lang.String-java.util.Date-">formatDate</a></span>(java.lang.String&nbsp;pattern,
          java.util.Date&nbsp;date)</code>
<div class="block">格式化日期, 仅用于pattern不固定的情况.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateFormatUtil.html#formatDate-java.lang.String-long-">formatDate</a></span>(java.lang.String&nbsp;pattern,
          long&nbsp;date)</code>
<div class="block">格式化日期, 仅用于不固定pattern不固定的情况.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateFormatUtil.html#formatDuration-java.util.Date-java.util.Date-">formatDuration</a></span>(java.util.Date&nbsp;startDate,
              java.util.Date&nbsp;endDate)</code>
<div class="block">按HH:mm:ss.SSS格式，格式化时间间隔.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateFormatUtil.html#formatDuration-long-">formatDuration</a></span>(long&nbsp;durationMillis)</code>
<div class="block">按HH:mm:ss.SSS格式，格式化时间间隔
 
 单位为毫秒，必须大于0，可大于1天</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateFormatUtil.html#formatDurationOnSecond-java.util.Date-java.util.Date-">formatDurationOnSecond</a></span>(java.util.Date&nbsp;startDate,
                      java.util.Date&nbsp;endDate)</code>
<div class="block">按HH:mm:ss格式，格式化时间间隔
 
 endDate必须大于startDate，间隔可大于1天</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateFormatUtil.html#formatDurationOnSecond-long-">formatDurationOnSecond</a></span>(long&nbsp;durationMillis)</code>
<div class="block">按HH:mm:ss格式，格式化时间间隔
 
 单位为毫秒，必须大于0，可大于1天</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateFormatUtil.html#formatFriendlyTimeSpanByNow-java.util.Date-">formatFriendlyTimeSpanByNow</a></span>(java.util.Date&nbsp;date)</code>
<div class="block">打印用户友好的，与当前时间相比的时间差，如刚刚，5分钟前，今天XXX，昨天XXX
 
 copy from AndroidUtilCode</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateFormatUtil.html#formatFriendlyTimeSpanByNow-long-">formatFriendlyTimeSpanByNow</a></span>(long&nbsp;timeStampMillis)</code>
<div class="block">打印用户友好的，与当前时间相比的时间差，如刚刚，5分钟前，今天XXX，昨天XXX
 
 copy from AndroidUtilCode</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/time/DateFormatUtil.html#parseDate-java.lang.String-java.lang.String-">parseDate</a></span>(java.lang.String&nbsp;pattern,
         java.lang.String&nbsp;dateString)</code>
<div class="block">分析日期字符串, 仅用于pattern不固定的情况.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="PATTERN_ISO">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PATTERN_ISO</h4>
<pre>public static final&nbsp;java.lang.String PATTERN_ISO</pre>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.huazheng.tunny.tools.time.DateFormatUtil.PATTERN_ISO">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="PATTERN_ISO_ON_SECOND">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PATTERN_ISO_ON_SECOND</h4>
<pre>public static final&nbsp;java.lang.String PATTERN_ISO_ON_SECOND</pre>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.huazheng.tunny.tools.time.DateFormatUtil.PATTERN_ISO_ON_SECOND">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="PATTERN_ISO_ON_DATE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PATTERN_ISO_ON_DATE</h4>
<pre>public static final&nbsp;java.lang.String PATTERN_ISO_ON_DATE</pre>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.huazheng.tunny.tools.time.DateFormatUtil.PATTERN_ISO_ON_DATE">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="PATTERN_DEFAULT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PATTERN_DEFAULT</h4>
<pre>public static final&nbsp;java.lang.String PATTERN_DEFAULT</pre>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.huazheng.tunny.tools.time.DateFormatUtil.PATTERN_DEFAULT">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="PATTERN_DEFAULT_ON_SECOND">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PATTERN_DEFAULT_ON_SECOND</h4>
<pre>public static final&nbsp;java.lang.String PATTERN_DEFAULT_ON_SECOND</pre>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.huazheng.tunny.tools.time.DateFormatUtil.PATTERN_DEFAULT_ON_SECOND">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="ISO_FORMAT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO_FORMAT</h4>
<pre>public static final&nbsp;org.apache.commons.lang3.time.FastDateFormat ISO_FORMAT</pre>
</li>
</ul>
<a name="ISO_ON_SECOND_FORMAT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO_ON_SECOND_FORMAT</h4>
<pre>public static final&nbsp;org.apache.commons.lang3.time.FastDateFormat ISO_ON_SECOND_FORMAT</pre>
</li>
</ul>
<a name="ISO_ON_DATE_FORMAT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO_ON_DATE_FORMAT</h4>
<pre>public static final&nbsp;org.apache.commons.lang3.time.FastDateFormat ISO_ON_DATE_FORMAT</pre>
</li>
</ul>
<a name="DEFAULT_FORMAT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DEFAULT_FORMAT</h4>
<pre>public static final&nbsp;org.apache.commons.lang3.time.FastDateFormat DEFAULT_FORMAT</pre>
</li>
</ul>
<a name="DEFAULT_ON_SECOND_FORMAT">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DEFAULT_ON_SECOND_FORMAT</h4>
<pre>public static final&nbsp;org.apache.commons.lang3.time.FastDateFormat DEFAULT_ON_SECOND_FORMAT</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="DateFormatUtil--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DateFormatUtil</h4>
<pre>public&nbsp;DateFormatUtil()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="parseDate-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseDate</h4>
<pre>public static&nbsp;java.util.Date&nbsp;parseDate(java.lang.String&nbsp;pattern,
                                       java.lang.String&nbsp;dateString)
                                throws java.text.ParseException</pre>
<div class="block">分析日期字符串, 仅用于pattern不固定的情况.
 
 否则直接使用DateFormats中封装好的FastDateFormat.
 
 FastDateFormat.getInstance()已经做了缓存，不会每次创建对象，但直接使用对象仍然能减少在缓存中的查找.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.text.ParseException</code></dd>
</dl>
</li>
</ul>
<a name="formatDate-java.lang.String-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>formatDate</h4>
<pre>public static&nbsp;java.lang.String&nbsp;formatDate(java.lang.String&nbsp;pattern,
                                          java.util.Date&nbsp;date)</pre>
<div class="block">格式化日期, 仅用于pattern不固定的情况.
 
 否则直接使用本类中封装好的FastDateFormat.
 
 FastDateFormat.getInstance()已经做了缓存，不会每次创建对象，但直接使用对象仍然能减少在缓存中的查找.</div>
</li>
</ul>
<a name="formatDate-java.lang.String-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>formatDate</h4>
<pre>public static&nbsp;java.lang.String&nbsp;formatDate(java.lang.String&nbsp;pattern,
                                          long&nbsp;date)</pre>
<div class="block">格式化日期, 仅用于不固定pattern不固定的情况.
 
 否否则直接使用本类中封装好的FastDateFormat.
 
 FastDateFormat.getInstance()已经做了缓存，不会每次创建对象，但直接使用对象仍然能减少在缓存中的查找.</div>
</li>
</ul>
<a name="formatDuration-java.util.Date-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>formatDuration</h4>
<pre>public static&nbsp;java.lang.String&nbsp;formatDuration(java.util.Date&nbsp;startDate,
                                              java.util.Date&nbsp;endDate)</pre>
<div class="block">按HH:mm:ss.SSS格式，格式化时间间隔.
 
 endDate必须大于startDate，间隔可大于1天，</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>DurationFormatUtils</code></dd>
</dl>
</li>
</ul>
<a name="formatDuration-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>formatDuration</h4>
<pre>public static&nbsp;java.lang.String&nbsp;formatDuration(long&nbsp;durationMillis)</pre>
<div class="block">按HH:mm:ss.SSS格式，格式化时间间隔
 
 单位为毫秒，必须大于0，可大于1天</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>DurationFormatUtils</code></dd>
</dl>
</li>
</ul>
<a name="formatDurationOnSecond-java.util.Date-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>formatDurationOnSecond</h4>
<pre>public static&nbsp;java.lang.String&nbsp;formatDurationOnSecond(java.util.Date&nbsp;startDate,
                                                      java.util.Date&nbsp;endDate)</pre>
<div class="block">按HH:mm:ss格式，格式化时间间隔
 
 endDate必须大于startDate，间隔可大于1天</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>DurationFormatUtils</code></dd>
</dl>
</li>
</ul>
<a name="formatDurationOnSecond-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>formatDurationOnSecond</h4>
<pre>public static&nbsp;java.lang.String&nbsp;formatDurationOnSecond(long&nbsp;durationMillis)</pre>
<div class="block">按HH:mm:ss格式，格式化时间间隔
 
 单位为毫秒，必须大于0，可大于1天</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>DurationFormatUtils</code></dd>
</dl>
</li>
</ul>
<a name="formatFriendlyTimeSpanByNow-java.util.Date-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>formatFriendlyTimeSpanByNow</h4>
<pre>public static&nbsp;java.lang.String&nbsp;formatFriendlyTimeSpanByNow(java.util.Date&nbsp;date)</pre>
<div class="block">打印用户友好的，与当前时间相比的时间差，如刚刚，5分钟前，今天XXX，昨天XXX
 
 copy from AndroidUtilCode</div>
</li>
</ul>
<a name="formatFriendlyTimeSpanByNow-long-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>formatFriendlyTimeSpanByNow</h4>
<pre>public static&nbsp;java.lang.String&nbsp;formatFriendlyTimeSpanByNow(long&nbsp;timeStampMillis)</pre>
<div class="block">打印用户友好的，与当前时间相比的时间差，如刚刚，5分钟前，今天XXX，昨天XXX
 
 copy from AndroidUtilCode</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/time/ClockUtilTest.html" title="com.huazheng.tunny.tools.time中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/time/DateFormatUtilTest.html" title="com.huazheng.tunny.tools.time中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/time/DateFormatUtil.html" target="_top">框架</a></li>
<li><a href="DateFormatUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:33 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>XmlMapper</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="XmlMapper";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/mapper/JsonMapperTest.TestBean.html" title="com.huazheng.tunny.tools.mapper中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/mapper/XmlMapper.CollectionWrapper.html" title="com.huazheng.tunny.tools.mapper中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/mapper/XmlMapper.html" target="_top">框架</a></li>
<li><a href="XmlMapper.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.mapper</div>
<h2 title="类 XmlMapper" class="title">类 XmlMapper</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.mapper.XmlMapper</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">XmlMapper</span>
extends java.lang.Object</pre>
<div class="block">使用Jaxb2.0实现XML<->Java Object的Mapper.
 
 在创建时需要设定所有需要序列化的Root对象的Class.
 特别支持Root对象是Collection的情形.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>嵌套类概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="嵌套类概要表, 列表嵌套类和解释">
<caption><span>嵌套类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">类和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/mapper/XmlMapper.CollectionWrapper.html" title="com.huazheng.tunny.tools.mapper中的类">XmlMapper.CollectionWrapper</a></span></code>
<div class="block">封装Root Element 是 Collection的情况.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/mapper/XmlMapper.html#XmlMapper--">XmlMapper</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static javax.xml.bind.Marshaller</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/mapper/XmlMapper.html#createMarshaller-java.lang.Class-java.lang.String-">createMarshaller</a></span>(java.lang.Class&nbsp;clazz,
                java.lang.String&nbsp;encoding)</code>
<div class="block">创建Marshaller并设定encoding(可为null).</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static javax.xml.bind.Unmarshaller</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/mapper/XmlMapper.html#createUnmarshaller-java.lang.Class-">createUnmarshaller</a></span>(java.lang.Class&nbsp;clazz)</code>
<div class="block">创建UnMarshaller.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/mapper/XmlMapper.html#fromXml-java.lang.String-java.lang.Class-">fromXml</a></span>(java.lang.String&nbsp;xml,
       java.lang.Class&lt;T&gt;&nbsp;clazz)</code>
<div class="block">Xml->Java Object.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>protected static javax.xml.bind.JAXBContext</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/mapper/XmlMapper.html#getJaxbContext-java.lang.Class-">getJaxbContext</a></span>(java.lang.Class&nbsp;clazz)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/mapper/XmlMapper.html#toXml-java.util.Collection-java.lang.String-java.lang.Class-">toXml</a></span>(java.util.Collection&lt;?&gt;&nbsp;root,
     java.lang.String&nbsp;rootName,
     java.lang.Class&nbsp;clazz)</code>
<div class="block">Java Collection->Xml without encoding, 特别支持Root Element是Collection的情形.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/mapper/XmlMapper.html#toXml-java.util.Collection-java.lang.String-java.lang.Class-java.lang.String-">toXml</a></span>(java.util.Collection&lt;?&gt;&nbsp;root,
     java.lang.String&nbsp;rootName,
     java.lang.Class&nbsp;clazz,
     java.lang.String&nbsp;encoding)</code>
<div class="block">Java Collection->Xml with encoding, 特别支持Root Element是Collection的情形.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/mapper/XmlMapper.html#toXml-java.lang.Object-">toXml</a></span>(java.lang.Object&nbsp;root)</code>
<div class="block">Java Object->Xml without encoding.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/mapper/XmlMapper.html#toXml-java.lang.Object-java.lang.Class-java.lang.String-">toXml</a></span>(java.lang.Object&nbsp;root,
     java.lang.Class&nbsp;clazz,
     java.lang.String&nbsp;encoding)</code>
<div class="block">Java Object->Xml with encoding.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/mapper/XmlMapper.html#toXml-java.lang.Object-java.lang.String-">toXml</a></span>(java.lang.Object&nbsp;root,
     java.lang.String&nbsp;encoding)</code>
<div class="block">Java Object->Xml with encoding.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="XmlMapper--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>XmlMapper</h4>
<pre>public&nbsp;XmlMapper()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="toXml-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toXml</h4>
<pre>public static&nbsp;java.lang.String&nbsp;toXml(java.lang.Object&nbsp;root)</pre>
<div class="block">Java Object->Xml without encoding.</div>
</li>
</ul>
<a name="toXml-java.lang.Object-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toXml</h4>
<pre>public static&nbsp;java.lang.String&nbsp;toXml(java.lang.Object&nbsp;root,
                                     java.lang.String&nbsp;encoding)</pre>
<div class="block">Java Object->Xml with encoding.</div>
</li>
</ul>
<a name="toXml-java.lang.Object-java.lang.Class-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toXml</h4>
<pre>public static&nbsp;java.lang.String&nbsp;toXml(java.lang.Object&nbsp;root,
                                     java.lang.Class&nbsp;clazz,
                                     java.lang.String&nbsp;encoding)</pre>
<div class="block">Java Object->Xml with encoding.</div>
</li>
</ul>
<a name="toXml-java.util.Collection-java.lang.String-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toXml</h4>
<pre>public static&nbsp;java.lang.String&nbsp;toXml(java.util.Collection&lt;?&gt;&nbsp;root,
                                     java.lang.String&nbsp;rootName,
                                     java.lang.Class&nbsp;clazz)</pre>
<div class="block">Java Collection->Xml without encoding, 特别支持Root Element是Collection的情形.</div>
</li>
</ul>
<a name="toXml-java.util.Collection-java.lang.String-java.lang.Class-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toXml</h4>
<pre>public static&nbsp;java.lang.String&nbsp;toXml(java.util.Collection&lt;?&gt;&nbsp;root,
                                     java.lang.String&nbsp;rootName,
                                     java.lang.Class&nbsp;clazz,
                                     java.lang.String&nbsp;encoding)</pre>
<div class="block">Java Collection->Xml with encoding, 特别支持Root Element是Collection的情形.</div>
</li>
</ul>
<a name="fromXml-java.lang.String-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fromXml</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;T&nbsp;fromXml(java.lang.String&nbsp;xml,
                            java.lang.Class&lt;T&gt;&nbsp;clazz)</pre>
<div class="block">Xml->Java Object.</div>
</li>
</ul>
<a name="createMarshaller-java.lang.Class-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createMarshaller</h4>
<pre>public static&nbsp;javax.xml.bind.Marshaller&nbsp;createMarshaller(java.lang.Class&nbsp;clazz,
                                                         java.lang.String&nbsp;encoding)</pre>
<div class="block">创建Marshaller并设定encoding(可为null).
 线程不安全，需要每次创建或pooling。</div>
</li>
</ul>
<a name="createUnmarshaller-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createUnmarshaller</h4>
<pre>public static&nbsp;javax.xml.bind.Unmarshaller&nbsp;createUnmarshaller(java.lang.Class&nbsp;clazz)</pre>
<div class="block">创建UnMarshaller.
 线程不安全，需要每次创建或pooling。</div>
</li>
</ul>
<a name="getJaxbContext-java.lang.Class-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getJaxbContext</h4>
<pre>protected static&nbsp;javax.xml.bind.JAXBContext&nbsp;getJaxbContext(java.lang.Class&nbsp;clazz)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/mapper/JsonMapperTest.TestBean.html" title="com.huazheng.tunny.tools.mapper中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/mapper/XmlMapper.CollectionWrapper.html" title="com.huazheng.tunny.tools.mapper中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/mapper/XmlMapper.html" target="_top">框架</a></li>
<li><a href="XmlMapper.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

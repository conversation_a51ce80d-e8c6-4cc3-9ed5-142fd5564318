<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:35 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>com.huazheng.tunny.tools.mapper</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.huazheng.tunny.tools.mapper";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li class="navBarCell1Rev">程序包</li>
<li>类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/io/type/package-summary.html">上一个程序包</a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/net/package-summary.html">下一个程序包</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/mapper/package-summary.html" target="_top">框架</a></li>
<li><a href="package-summary.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="程序包" class="title">程序包&nbsp;com.huazheng.tunny.tools.mapper</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="类概要表, 列表类和解释">
<caption><span>类概要</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">类</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/mapper/BeanMapper.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapper</a></td>
<td class="colLast">
<div class="block">实现深度的BeanOfClasssA<->BeanOfClassB复制
 
 不要使用Apache Common BeanUtils进行类复制，每次就行反射查询对象的属性列表, 非常缓慢.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/mapper/BeanMapperTest.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/mapper/BeanMapperTest.Student.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.Student</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/mapper/BeanMapperTest.StudentVO.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.StudentVO</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/mapper/BeanMapperTest.Teacher.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.Teacher</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/mapper/BeanMapperTest.TeacherVO.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.TeacherVO</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/mapper/JsonMapper.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapper</a></td>
<td class="colLast">
<div class="block">简单封装Jackson，实现JSON String<->Java Object转换的Mapper.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/mapper/JsonMapperTest.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapperTest</a></td>
<td class="colLast">
<div class="block">测试Jackson对Object,Map,List,数组,枚举,日期类等的持久化.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/mapper/JsonMapperTest.TestBean.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapperTest.TestBean</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/mapper/XmlMapper.html" title="com.huazheng.tunny.tools.mapper中的类">XmlMapper</a></td>
<td class="colLast">
<div class="block">使用Jaxb2.0实现XML<->Java Object的Mapper.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/mapper/XmlMapper.CollectionWrapper.html" title="com.huazheng.tunny.tools.mapper中的类">XmlMapper.CollectionWrapper</a></td>
<td class="colLast">
<div class="block">封装Root Element 是 Collection的情况.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/mapper/XmlMapperTest.html" title="com.huazheng.tunny.tools.mapper中的类">XmlMapperTest</a></td>
<td class="colLast">
<div class="block">演示基于JAXB2.0的Java对象-XML转换及Dom4j的使用.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li class="navBarCell1Rev">程序包</li>
<li>类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/io/type/package-summary.html">上一个程序包</a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/net/package-summary.html">下一个程序包</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/mapper/package-summary.html" target="_top">框架</a></li>
<li><a href="package-summary.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:32 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>JsonMapper</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="JsonMapper";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":9,"i3":10,"i4":10,"i5":10,"i6":10,"i7":9,"i8":9,"i9":10,"i10":10,"i11":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/mapper/BeanMapperTest.TeacherVO.html" title="com.huazheng.tunny.tools.mapper中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/mapper/JsonMapperTest.html" title="com.huazheng.tunny.tools.mapper中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/mapper/JsonMapper.html" target="_top">框架</a></li>
<li><a href="JsonMapper.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.mapper</div>
<h2 title="类 JsonMapper" class="title">类 JsonMapper</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.mapper.JsonMapper</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">JsonMapper</span>
extends java.lang.Object</pre>
<div class="block">简单封装Jackson，实现JSON String<->Java Object转换的Mapper.
 
 可以直接使用公共示例JsonMapper.INSTANCE, 也可以使用不同的builder函数创建实例，封装不同的输出风格,
 
 不要使用GSON, 在对象稍大时非常缓慢.
 
 注意: 需要参考本模块的POM文件，显式引用jackson.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/huazheng/tunny/tools/mapper/JsonMapper.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapper</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/mapper/JsonMapper.html#INSTANCE">INSTANCE</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/mapper/JsonMapper.html#JsonMapper--">JsonMapper</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/mapper/JsonMapper.html#JsonMapper-com.fasterxml.jackson.annotation.JsonInclude.Include-">JsonMapper</a></span>(com.fasterxml.jackson.annotation.JsonInclude.Include&nbsp;include)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>com.fasterxml.jackson.databind.JavaType</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/mapper/JsonMapper.html#buildCollectionType-java.lang.Class-java.lang.Class-">buildCollectionType</a></span>(java.lang.Class&lt;? extends java.util.Collection&gt;&nbsp;collectionClass,
                   java.lang.Class&lt;?&gt;&nbsp;elementClass)</code>
<div class="block">构造Collection类型.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>com.fasterxml.jackson.databind.JavaType</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/mapper/JsonMapper.html#buildMapType-java.lang.Class-java.lang.Class-java.lang.Class-">buildMapType</a></span>(java.lang.Class&lt;? extends java.util.Map&gt;&nbsp;mapClass,
            java.lang.Class&lt;?&gt;&nbsp;keyClass,
            java.lang.Class&lt;?&gt;&nbsp;valueClass)</code>
<div class="block">构造Map类型.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/huazheng/tunny/tools/mapper/JsonMapper.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapper</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/mapper/JsonMapper.html#defaultMapper--">defaultMapper</a></span>()</code>
<div class="block">默认的全部输出的Mapper, 区别于INSTANCE，可以做进一步的配置</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/mapper/JsonMapper.html#enableEnumUseToString--">enableEnumUseToString</a></span>()</code>
<div class="block">設定是否使用Enum的toString函數來讀寫Enum, 為False時時使用Enum的name()函數來讀寫Enum, 默認為False.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/mapper/JsonMapper.html#fromJson-java.lang.String-java.lang.Class-">fromJson</a></span>(java.lang.String&nbsp;jsonString,
        java.lang.Class&lt;T&gt;&nbsp;clazz)</code>
<div class="block">反序列化POJO或简单Collection如List<String>.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/mapper/JsonMapper.html#fromJson-java.lang.String-com.fasterxml.jackson.databind.JavaType-">fromJson</a></span>(java.lang.String&nbsp;jsonString,
        com.fasterxml.jackson.databind.JavaType&nbsp;javaType)</code>
<div class="block">反序列化复杂Collection如List<Bean>, contructCollectionType()或contructMapType()构造类型, 然后调用本函数.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>com.fasterxml.jackson.databind.ObjectMapper</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/mapper/JsonMapper.html#getMapper--">getMapper</a></span>()</code>
<div class="block">取出Mapper做进一步的设置或使用其他序列化API.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/huazheng/tunny/tools/mapper/JsonMapper.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapper</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/mapper/JsonMapper.html#nonEmptyMapper--">nonEmptyMapper</a></span>()</code>
<div class="block">创建只输出非Null且非Empty(如List.isEmpty)的属性到Json字符串的Mapper.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/huazheng/tunny/tools/mapper/JsonMapper.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapper</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/mapper/JsonMapper.html#nonNullMapper--">nonNullMapper</a></span>()</code>
<div class="block">创建只输出非Null的属性到Json字符串的Mapper.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/mapper/JsonMapper.html#toJson-java.lang.Object-">toJson</a></span>(java.lang.Object&nbsp;object)</code>
<div class="block">Object可以是POJO，也可以是Collection或数组。</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/mapper/JsonMapper.html#toJsonP-java.lang.String-java.lang.Object-">toJsonP</a></span>(java.lang.String&nbsp;functionName,
       java.lang.Object&nbsp;object)</code>
<div class="block">輸出JSONP格式數據.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/mapper/JsonMapper.html#update-java.lang.String-java.lang.Object-">update</a></span>(java.lang.String&nbsp;jsonString,
      java.lang.Object&nbsp;object)</code>
<div class="block">当JSON里只含有Bean的部分属性時，更新一個已存在Bean，只覆盖該部分的属性.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="INSTANCE">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>INSTANCE</h4>
<pre>public static final&nbsp;<a href="../../../../../com/huazheng/tunny/tools/mapper/JsonMapper.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapper</a> INSTANCE</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="JsonMapper--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>JsonMapper</h4>
<pre>public&nbsp;JsonMapper()</pre>
</li>
</ul>
<a name="JsonMapper-com.fasterxml.jackson.annotation.JsonInclude.Include-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>JsonMapper</h4>
<pre>public&nbsp;JsonMapper(com.fasterxml.jackson.annotation.JsonInclude.Include&nbsp;include)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="nonNullMapper--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nonNullMapper</h4>
<pre>public static&nbsp;<a href="../../../../../com/huazheng/tunny/tools/mapper/JsonMapper.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapper</a>&nbsp;nonNullMapper()</pre>
<div class="block">创建只输出非Null的属性到Json字符串的Mapper.</div>
</li>
</ul>
<a name="nonEmptyMapper--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nonEmptyMapper</h4>
<pre>public static&nbsp;<a href="../../../../../com/huazheng/tunny/tools/mapper/JsonMapper.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapper</a>&nbsp;nonEmptyMapper()</pre>
<div class="block">创建只输出非Null且非Empty(如List.isEmpty)的属性到Json字符串的Mapper.
 
 注意，要小心使用, 特别留意empty的情况.</div>
</li>
</ul>
<a name="defaultMapper--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>defaultMapper</h4>
<pre>public static&nbsp;<a href="../../../../../com/huazheng/tunny/tools/mapper/JsonMapper.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapper</a>&nbsp;defaultMapper()</pre>
<div class="block">默认的全部输出的Mapper, 区别于INSTANCE，可以做进一步的配置</div>
</li>
</ul>
<a name="toJson-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toJson</h4>
<pre>public&nbsp;java.lang.String&nbsp;toJson(java.lang.Object&nbsp;object)</pre>
<div class="block">Object可以是POJO，也可以是Collection或数组。 如果对象为Null, 返回"null". 如果集合为空集合, 返回"[]".</div>
</li>
</ul>
<a name="fromJson-java.lang.String-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fromJson</h4>
<pre>public&nbsp;&lt;T&gt;&nbsp;T&nbsp;fromJson(java.lang.String&nbsp;jsonString,
                      java.lang.Class&lt;T&gt;&nbsp;clazz)</pre>
<div class="block">反序列化POJO或简单Collection如List<String>.
 
 如果JSON字符串为Null或"null"字符串, 返回Null. 如果JSON字符串为"[]", 返回空集合.
 
 如需反序列化复杂Collection如List<MyBean>, 请使用fromJson(String, JavaType)</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../com/huazheng/tunny/tools/mapper/JsonMapper.html#fromJson-java.lang.String-com.fasterxml.jackson.databind.JavaType-"><code>fromJson(String, JavaType)</code></a></dd>
</dl>
</li>
</ul>
<a name="fromJson-java.lang.String-com.fasterxml.jackson.databind.JavaType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fromJson</h4>
<pre>public&nbsp;&lt;T&gt;&nbsp;T&nbsp;fromJson(java.lang.String&nbsp;jsonString,
                      com.fasterxml.jackson.databind.JavaType&nbsp;javaType)</pre>
<div class="block">反序列化复杂Collection如List<Bean>, contructCollectionType()或contructMapType()构造类型, 然后调用本函数.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>#createCollectionType(Class, Class...)</code></dd>
</dl>
</li>
</ul>
<a name="buildCollectionType-java.lang.Class-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>buildCollectionType</h4>
<pre>public&nbsp;com.fasterxml.jackson.databind.JavaType&nbsp;buildCollectionType(java.lang.Class&lt;? extends java.util.Collection&gt;&nbsp;collectionClass,
                                                                   java.lang.Class&lt;?&gt;&nbsp;elementClass)</pre>
<div class="block">构造Collection类型.</div>
</li>
</ul>
<a name="buildMapType-java.lang.Class-java.lang.Class-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>buildMapType</h4>
<pre>public&nbsp;com.fasterxml.jackson.databind.JavaType&nbsp;buildMapType(java.lang.Class&lt;? extends java.util.Map&gt;&nbsp;mapClass,
                                                            java.lang.Class&lt;?&gt;&nbsp;keyClass,
                                                            java.lang.Class&lt;?&gt;&nbsp;valueClass)</pre>
<div class="block">构造Map类型.</div>
</li>
</ul>
<a name="update-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>update</h4>
<pre>public&nbsp;void&nbsp;update(java.lang.String&nbsp;jsonString,
                   java.lang.Object&nbsp;object)</pre>
<div class="block">当JSON里只含有Bean的部分属性時，更新一個已存在Bean，只覆盖該部分的属性.</div>
</li>
</ul>
<a name="toJsonP-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toJsonP</h4>
<pre>public&nbsp;java.lang.String&nbsp;toJsonP(java.lang.String&nbsp;functionName,
                                java.lang.Object&nbsp;object)</pre>
<div class="block">輸出JSONP格式數據.</div>
</li>
</ul>
<a name="enableEnumUseToString--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enableEnumUseToString</h4>
<pre>public&nbsp;void&nbsp;enableEnumUseToString()</pre>
<div class="block">設定是否使用Enum的toString函數來讀寫Enum, 為False時時使用Enum的name()函數來讀寫Enum, 默認為False. 注意本函數一定要在Mapper創建後, 所有的讀寫動作之前調用.</div>
</li>
</ul>
<a name="getMapper--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getMapper</h4>
<pre>public&nbsp;com.fasterxml.jackson.databind.ObjectMapper&nbsp;getMapper()</pre>
<div class="block">取出Mapper做进一步的设置或使用其他序列化API.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/mapper/BeanMapperTest.TeacherVO.html" title="com.huazheng.tunny.tools.mapper中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/mapper/JsonMapperTest.html" title="com.huazheng.tunny.tools.mapper中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/mapper/JsonMapper.html" target="_top">框架</a></li>
<li><a href="JsonMapper.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

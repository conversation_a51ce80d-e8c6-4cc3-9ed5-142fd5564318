<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:35 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>com.huazheng.tunny.tools.mapper</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<h1 class="bar"><a href="../../../../../com/huazheng/tunny/tools/mapper/package-summary.html" target="classFrame">com.huazheng.tunny.tools.mapper</a></h1>
<div class="indexContainer">
<h2 title="类">类</h2>
<ul title="类">
<li><a href="BeanMapper.html" title="com.huazheng.tunny.tools.mapper中的类" target="classFrame">BeanMapper</a></li>
<li><a href="BeanMapperTest.html" title="com.huazheng.tunny.tools.mapper中的类" target="classFrame">BeanMapperTest</a></li>
<li><a href="BeanMapperTest.Student.html" title="com.huazheng.tunny.tools.mapper中的类" target="classFrame">BeanMapperTest.Student</a></li>
<li><a href="BeanMapperTest.StudentVO.html" title="com.huazheng.tunny.tools.mapper中的类" target="classFrame">BeanMapperTest.StudentVO</a></li>
<li><a href="BeanMapperTest.Teacher.html" title="com.huazheng.tunny.tools.mapper中的类" target="classFrame">BeanMapperTest.Teacher</a></li>
<li><a href="BeanMapperTest.TeacherVO.html" title="com.huazheng.tunny.tools.mapper中的类" target="classFrame">BeanMapperTest.TeacherVO</a></li>
<li><a href="JsonMapper.html" title="com.huazheng.tunny.tools.mapper中的类" target="classFrame">JsonMapper</a></li>
<li><a href="JsonMapperTest.html" title="com.huazheng.tunny.tools.mapper中的类" target="classFrame">JsonMapperTest</a></li>
<li><a href="JsonMapperTest.TestBean.html" title="com.huazheng.tunny.tools.mapper中的类" target="classFrame">JsonMapperTest.TestBean</a></li>
<li><a href="XmlMapper.html" title="com.huazheng.tunny.tools.mapper中的类" target="classFrame">XmlMapper</a></li>
<li><a href="XmlMapper.CollectionWrapper.html" title="com.huazheng.tunny.tools.mapper中的类" target="classFrame">XmlMapper.CollectionWrapper</a></li>
<li><a href="XmlMapperTest.html" title="com.huazheng.tunny.tools.mapper中的类" target="classFrame">XmlMapperTest</a></li>
</ul>
</div>
</body>
</html>

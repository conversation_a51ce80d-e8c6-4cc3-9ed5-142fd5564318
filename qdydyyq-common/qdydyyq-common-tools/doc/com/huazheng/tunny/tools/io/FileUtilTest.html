<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:31 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>FileUtilTest</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="FileUtilTest";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/io/IOUtil.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/io/FileUtilTest.html" target="_top">框架</a></li>
<li><a href="FileUtilTest.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.io</div>
<h2 title="类 FileUtilTest" class="title">类 FileUtilTest</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.io.FileUtilTest</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">FileUtilTest</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtilTest.html#FileUtilTest--">FileUtilTest</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtilTest.html#fileExist--">fileExist</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtilTest.html#getName--">getName</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtilTest.html#opDir--">opDir</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtilTest.html#opFiles--">opFiles</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtilTest.html#readWrite--">readWrite</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtilTest.html#testAsBufferedReader--">testAsBufferedReader</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtilTest.html#testAsBufferedWriter--">testAsBufferedWriter</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtilTest.html#testAsInputStream--">testAsInputStream</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtilTest.html#testAsOututStream--">testAsOututStream</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtilTest.html#testCopy--">testCopy</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtilTest.html#testGetFileExtension--">testGetFileExtension</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtilTest.html#testIsDirExists--">testIsDirExists</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtilTest.html#testIsFileExists--">testIsFileExists</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtilTest.html#testMakesureDirExists--">testMakesureDirExists</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="FileUtilTest--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>FileUtilTest</h4>
<pre>public&nbsp;FileUtilTest()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="readWrite--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readWrite</h4>
<pre>public&nbsp;void&nbsp;readWrite()
               throws java.io.IOException</pre>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="opFiles--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>opFiles</h4>
<pre>public&nbsp;void&nbsp;opFiles()
             throws java.io.IOException</pre>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="opDir--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>opDir</h4>
<pre>public&nbsp;void&nbsp;opDir()
           throws java.io.IOException</pre>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="fileExist--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fileExist</h4>
<pre>public&nbsp;void&nbsp;fileExist()
               throws java.io.IOException</pre>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;void&nbsp;getName()</pre>
</li>
</ul>
<a name="testAsInputStream--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>testAsInputStream</h4>
<pre>public&nbsp;void&nbsp;testAsInputStream()
                       throws java.lang.Exception</pre>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.lang.Exception</code></dd>
</dl>
</li>
</ul>
<a name="testAsOututStream--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>testAsOututStream</h4>
<pre>public&nbsp;void&nbsp;testAsOututStream()
                       throws java.lang.Exception</pre>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.lang.Exception</code></dd>
</dl>
</li>
</ul>
<a name="testAsBufferedReader--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>testAsBufferedReader</h4>
<pre>public&nbsp;void&nbsp;testAsBufferedReader()
                          throws java.lang.Exception</pre>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.lang.Exception</code></dd>
</dl>
</li>
</ul>
<a name="testAsBufferedWriter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>testAsBufferedWriter</h4>
<pre>public&nbsp;void&nbsp;testAsBufferedWriter()
                          throws java.lang.Exception</pre>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.lang.Exception</code></dd>
</dl>
</li>
</ul>
<a name="testCopy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>testCopy</h4>
<pre>public&nbsp;void&nbsp;testCopy()
              throws java.lang.Exception</pre>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.lang.Exception</code></dd>
</dl>
</li>
</ul>
<a name="testMakesureDirExists--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>testMakesureDirExists</h4>
<pre>public&nbsp;void&nbsp;testMakesureDirExists()
                           throws java.lang.Exception</pre>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.lang.Exception</code></dd>
</dl>
</li>
</ul>
<a name="testIsFileExists--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>testIsFileExists</h4>
<pre>public&nbsp;void&nbsp;testIsFileExists()
                      throws java.lang.Exception</pre>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.lang.Exception</code></dd>
</dl>
</li>
</ul>
<a name="testGetFileExtension--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>testGetFileExtension</h4>
<pre>public&nbsp;void&nbsp;testGetFileExtension()
                          throws java.lang.Exception</pre>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.lang.Exception</code></dd>
</dl>
</li>
</ul>
<a name="testIsDirExists--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>testIsDirExists</h4>
<pre>public&nbsp;void&nbsp;testIsDirExists()
                     throws java.lang.Exception</pre>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.lang.Exception</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/io/IOUtil.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/io/FileUtilTest.html" target="_top">框架</a></li>
<li><a href="FileUtilTest.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

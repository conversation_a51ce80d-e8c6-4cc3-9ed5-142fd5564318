<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:31 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>FileUtil</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="FileUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9,"i14":9,"i15":9,"i16":9,"i17":9,"i18":9,"i19":9,"i20":9,"i21":9,"i22":9,"i23":9,"i24":9,"i25":9,"i26":9,"i27":9,"i28":9,"i29":9,"i30":9,"i31":9,"i32":9,"i33":9,"i34":9,"i35":9,"i36":9,"i37":9,"i38":9,"i39":9,"i40":9,"i41":9,"i42":9,"i43":9,"i44":9,"i45":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/io/FileTreeWalkerTest.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/io/FileUtilTest.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/io/FileUtil.html" target="_top">框架</a></li>
<li><a href="FileUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.io</div>
<h2 title="类 FileUtil" class="title">类 FileUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.io.FileUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">FileUtil</span>
extends java.lang.Object</pre>
<div class="block">关于文件的工具集.
 
 主要是调用JDK自带的Files工具类，少量代码调用Guava Files。 固定encoding为UTF8.
 
 1.文件读写
 
 2.文件及目录操作</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#FileUtil--">FileUtil</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#append-java.lang.CharSequence-java.io.File-">append</a></span>(java.lang.CharSequence&nbsp;data,
      java.io.File&nbsp;file)</code>
<div class="block">追加String到File.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static java.io.BufferedReader</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#asBufferedReader-java.nio.file.Path-">asBufferedReader</a></span>(java.nio.file.Path&nbsp;path)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static java.io.BufferedReader</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#asBufferedReader-java.lang.String-">asBufferedReader</a></span>(java.lang.String&nbsp;fileName)</code>
<div class="block">获取File的BufferedReader.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static java.io.BufferedWriter</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#asBufferedWriter-java.nio.file.Path-">asBufferedWriter</a></span>(java.nio.file.Path&nbsp;path)</code>
<div class="block">获取File的BufferedWriter.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static java.io.BufferedWriter</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#asBufferedWriter-java.lang.String-">asBufferedWriter</a></span>(java.lang.String&nbsp;fileName)</code>
<div class="block">获取File的BufferedWriter.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static java.io.InputStream</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#asInputStream-java.io.File-">asInputStream</a></span>(java.io.File&nbsp;file)</code>
<div class="block">打开文件为InputStream.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static java.io.InputStream</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#asInputStream-java.nio.file.Path-">asInputStream</a></span>(java.nio.file.Path&nbsp;path)</code>
<div class="block">打开文件为InputStream.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static java.io.InputStream</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#asInputStream-java.lang.String-">asInputStream</a></span>(java.lang.String&nbsp;fileName)</code>
<div class="block">打开文件为InputStream.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static java.io.OutputStream</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#asOututStream-java.io.File-">asOututStream</a></span>(java.io.File&nbsp;file)</code>
<div class="block">打开文件为OutputStream.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static java.io.OutputStream</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#asOututStream-java.nio.file.Path-">asOututStream</a></span>(java.nio.file.Path&nbsp;path)</code>
<div class="block">打开文件为OutputStream.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static java.io.OutputStream</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#asOututStream-java.lang.String-">asOututStream</a></span>(java.lang.String&nbsp;fileName)</code>
<div class="block">打开文件为OutputStream.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#copy-java.io.File-java.io.File-">copy</a></span>(java.io.File&nbsp;from,
    java.io.File&nbsp;to)</code>
<div class="block">复制文件或目录, not following links.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#copy-java.nio.file.Path-java.nio.file.Path-">copy</a></span>(java.nio.file.Path&nbsp;from,
    java.nio.file.Path&nbsp;to)</code>
<div class="block">复制文件或目录, not following links.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#copyDir-java.io.File-java.io.File-">copyDir</a></span>(java.io.File&nbsp;from,
       java.io.File&nbsp;to)</code>
<div class="block">复制目录</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#copyDir-java.nio.file.Path-java.nio.file.Path-">copyDir</a></span>(java.nio.file.Path&nbsp;from,
       java.nio.file.Path&nbsp;to)</code>
<div class="block">复制目录</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#copyFile-java.io.File-java.io.File-">copyFile</a></span>(java.io.File&nbsp;from,
        java.io.File&nbsp;to)</code>
<div class="block">文件复制.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#copyFile-java.nio.file.Path-java.nio.file.Path-">copyFile</a></span>(java.nio.file.Path&nbsp;from,
        java.nio.file.Path&nbsp;to)</code>
<div class="block">文件复制.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>static java.nio.file.Path</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#createTempDir--">createTempDir</a></span>()</code>
<div class="block">在临时目录创建临时目录，命名为${毫秒级时间戳}-${同一毫秒内的随机数}.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>static java.nio.file.Path</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#createTempFile--">createTempFile</a></span>()</code>
<div class="block">在临时目录创建临时文件，命名为tmp-${random.nextLong()}.tmp</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>static java.nio.file.Path</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#createTempFile-java.lang.String-java.lang.String-">createTempFile</a></span>(java.lang.String&nbsp;prefix,
              java.lang.String&nbsp;suffix)</code>
<div class="block">在临时目录创建临时文件，命名为${prefix}${random.nextLong()}${suffix}</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#deleteDir-java.io.File-">deleteDir</a></span>(java.io.File&nbsp;dir)</code>
<div class="block">删除目录及所有子目录/文件</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#deleteDir-java.nio.file.Path-">deleteDir</a></span>(java.nio.file.Path&nbsp;dir)</code>
<div class="block">删除目录及所有子目录/文件</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#deleteFile-java.io.File-">deleteFile</a></span>(java.io.File&nbsp;file)</code>
<div class="block">删除文件.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#deleteFile-java.nio.file.Path-">deleteFile</a></span>(java.nio.file.Path&nbsp;path)</code>
<div class="block">删除文件.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#getFileExtension-java.io.File-">getFileExtension</a></span>(java.io.File&nbsp;file)</code>
<div class="block">获取文件名的扩展名部分(不包含.)</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#getFileExtension-java.lang.String-">getFileExtension</a></span>(java.lang.String&nbsp;fullName)</code>
<div class="block">获取文件名的扩展名部分(不包含.)</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#getFileName-java.lang.String-">getFileName</a></span>(java.lang.String&nbsp;fullName)</code>
<div class="block">获取文件名(不包含路径)</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#isDirExists-java.io.File-">isDirExists</a></span>(java.io.File&nbsp;dir)</code>
<div class="block">判断目录是否存在, from Jodd</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#isDirExists-java.nio.file.Path-">isDirExists</a></span>(java.nio.file.Path&nbsp;dirPath)</code>&nbsp;</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#isDirExists-java.lang.String-">isDirExists</a></span>(java.lang.String&nbsp;dirPath)</code>
<div class="block">判断目录是否存在, from Jodd</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#isFileExists-java.io.File-">isFileExists</a></span>(java.io.File&nbsp;file)</code>
<div class="block">判断文件是否存在, from Jodd.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#isFileExists-java.nio.file.Path-">isFileExists</a></span>(java.nio.file.Path&nbsp;path)</code>
<div class="block">判断文件是否存在, from Jodd.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#isFileExists-java.lang.String-">isFileExists</a></span>(java.lang.String&nbsp;fileName)</code>
<div class="block">判断文件是否存在, from Jodd.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#makesureDirExists-java.io.File-">makesureDirExists</a></span>(java.io.File&nbsp;file)</code>
<div class="block">确保目录存在, 如不存在则创建</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#makesureDirExists-java.nio.file.Path-">makesureDirExists</a></span>(java.nio.file.Path&nbsp;dirPath)</code>
<div class="block">确保目录存在, 如不存在则创建.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#makesureDirExists-java.lang.String-">makesureDirExists</a></span>(java.lang.String&nbsp;dirPath)</code>
<div class="block">确保目录存在, 如不存在则创建</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#makesureParentDirExists-java.io.File-">makesureParentDirExists</a></span>(java.io.File&nbsp;file)</code>
<div class="block">确保父目录及其父目录直到根目录都已经创建.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#moveDir-java.io.File-java.io.File-">moveDir</a></span>(java.io.File&nbsp;from,
       java.io.File&nbsp;to)</code>
<div class="block">目录移动/重命名</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#moveFile-java.io.File-java.io.File-">moveFile</a></span>(java.io.File&nbsp;from,
        java.io.File&nbsp;to)</code>
<div class="block">文件移动/重命名.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#moveFile-java.nio.file.Path-java.nio.file.Path-">moveFile</a></span>(java.nio.file.Path&nbsp;from,
        java.nio.file.Path&nbsp;to)</code>
<div class="block">文件移动/重命名.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#toByteArray-java.io.File-">toByteArray</a></span>(java.io.File&nbsp;file)</code>
<div class="block">读取文件到byte[].</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>static java.util.List&lt;java.lang.String&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#toLines-java.io.File-">toLines</a></span>(java.io.File&nbsp;file)</code>
<div class="block">读取文件的每行内容到List<String>.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#toString-java.io.File-">toString</a></span>(java.io.File&nbsp;file)</code>
<div class="block">读取文件到String.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#touch-java.io.File-">touch</a></span>(java.io.File&nbsp;file)</code>
<div class="block">创建文件或更新时间戳.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#touch-java.lang.String-">touch</a></span>(java.lang.String&nbsp;filePath)</code>
<div class="block">创建文件或更新时间戳.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html#write-java.lang.CharSequence-java.io.File-">write</a></span>(java.lang.CharSequence&nbsp;data,
     java.io.File&nbsp;file)</code>
<div class="block">简单写入String到File.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="FileUtil--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>FileUtil</h4>
<pre>public&nbsp;FileUtil()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="toByteArray-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toByteArray</h4>
<pre>public static&nbsp;byte[]&nbsp;toByteArray(java.io.File&nbsp;file)
                          throws java.io.IOException</pre>
<div class="block">读取文件到byte[].</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Files#readAllBytes}</code></dd>
</dl>
</li>
</ul>
<a name="toString-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toString</h4>
<pre>public static&nbsp;java.lang.String&nbsp;toString(java.io.File&nbsp;file)
                                 throws java.io.IOException</pre>
<div class="block">读取文件到String.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="toLines-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toLines</h4>
<pre>public static&nbsp;java.util.List&lt;java.lang.String&gt;&nbsp;toLines(java.io.File&nbsp;file)
                                                throws java.io.IOException</pre>
<div class="block">读取文件的每行内容到List<String>.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Files#readAllLines}</code></dd>
</dl>
</li>
</ul>
<a name="write-java.lang.CharSequence-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>write</h4>
<pre>public static&nbsp;void&nbsp;write(java.lang.CharSequence&nbsp;data,
                         java.io.File&nbsp;file)
                  throws java.io.IOException</pre>
<div class="block">简单写入String到File.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="append-java.lang.CharSequence-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>append</h4>
<pre>public static&nbsp;void&nbsp;append(java.lang.CharSequence&nbsp;data,
                          java.io.File&nbsp;file)
                   throws java.io.IOException</pre>
<div class="block">追加String到File.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="asInputStream-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>asInputStream</h4>
<pre>public static&nbsp;java.io.InputStream&nbsp;asInputStream(java.lang.String&nbsp;fileName)
                                         throws java.io.IOException</pre>
<div class="block">打开文件为InputStream.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Files#newInputStream}</code></dd>
</dl>
</li>
</ul>
<a name="asInputStream-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>asInputStream</h4>
<pre>public static&nbsp;java.io.InputStream&nbsp;asInputStream(java.io.File&nbsp;file)
                                         throws java.io.IOException</pre>
<div class="block">打开文件为InputStream.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Files#newInputStream}</code></dd>
</dl>
</li>
</ul>
<a name="asInputStream-java.nio.file.Path-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>asInputStream</h4>
<pre>public static&nbsp;java.io.InputStream&nbsp;asInputStream(java.nio.file.Path&nbsp;path)
                                         throws java.io.IOException</pre>
<div class="block">打开文件为InputStream.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Files#newInputStream}</code></dd>
</dl>
</li>
</ul>
<a name="asOututStream-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>asOututStream</h4>
<pre>public static&nbsp;java.io.OutputStream&nbsp;asOututStream(java.lang.String&nbsp;fileName)
                                          throws java.io.IOException</pre>
<div class="block">打开文件为OutputStream.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Files#newOutputStream}</code></dd>
</dl>
</li>
</ul>
<a name="asOututStream-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>asOututStream</h4>
<pre>public static&nbsp;java.io.OutputStream&nbsp;asOututStream(java.io.File&nbsp;file)
                                          throws java.io.IOException</pre>
<div class="block">打开文件为OutputStream.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Files#newOutputStream}</code></dd>
</dl>
</li>
</ul>
<a name="asOututStream-java.nio.file.Path-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>asOututStream</h4>
<pre>public static&nbsp;java.io.OutputStream&nbsp;asOututStream(java.nio.file.Path&nbsp;path)
                                          throws java.io.IOException</pre>
<div class="block">打开文件为OutputStream.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Files#newOutputStream}</code></dd>
</dl>
</li>
</ul>
<a name="asBufferedReader-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>asBufferedReader</h4>
<pre>public static&nbsp;java.io.BufferedReader&nbsp;asBufferedReader(java.lang.String&nbsp;fileName)
                                               throws java.io.IOException</pre>
<div class="block">获取File的BufferedReader.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Files#newBufferedReader}</code></dd>
</dl>
</li>
</ul>
<a name="asBufferedReader-java.nio.file.Path-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>asBufferedReader</h4>
<pre>public static&nbsp;java.io.BufferedReader&nbsp;asBufferedReader(java.nio.file.Path&nbsp;path)
                                               throws java.io.IOException</pre>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="asBufferedWriter-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>asBufferedWriter</h4>
<pre>public static&nbsp;java.io.BufferedWriter&nbsp;asBufferedWriter(java.lang.String&nbsp;fileName)
                                               throws java.io.IOException</pre>
<div class="block">获取File的BufferedWriter.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Files#newBufferedWriter}</code></dd>
</dl>
</li>
</ul>
<a name="asBufferedWriter-java.nio.file.Path-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>asBufferedWriter</h4>
<pre>public static&nbsp;java.io.BufferedWriter&nbsp;asBufferedWriter(java.nio.file.Path&nbsp;path)
                                               throws java.io.IOException</pre>
<div class="block">获取File的BufferedWriter.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Files#newBufferedWriter}</code></dd>
</dl>
</li>
</ul>
<a name="copy-java.io.File-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copy</h4>
<pre>public static&nbsp;void&nbsp;copy(java.io.File&nbsp;from,
                        java.io.File&nbsp;to)
                 throws java.io.IOException</pre>
<div class="block">复制文件或目录, not following links.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>from</code> - 如果为null，或者是不存在的文件或目录，抛出异常.</dd>
<dd><code>to</code> - 如果为null，或者from是目录而to是已存在文件，或相反</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="copy-java.nio.file.Path-java.nio.file.Path-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copy</h4>
<pre>public static&nbsp;void&nbsp;copy(java.nio.file.Path&nbsp;from,
                        java.nio.file.Path&nbsp;to)
                 throws java.io.IOException</pre>
<div class="block">复制文件或目录, not following links.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>from</code> - 如果为null，或者是不存在的文件或目录，抛出异常.</dd>
<dd><code>to</code> - 如果为null，或者from是目录而to是已存在文件，或相反</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="copyFile-java.io.File-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copyFile</h4>
<pre>public static&nbsp;void&nbsp;copyFile(java.io.File&nbsp;from,
                            java.io.File&nbsp;to)
                     throws java.io.IOException</pre>
<div class="block">文件复制.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>from</code> - 如果为null，或文件不存在或者是目录，，抛出异常</dd>
<dd><code>to</code> - 如果to为null，或文件存在但是一个目录，抛出异常</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Files#copy}</code></dd>
</dl>
</li>
</ul>
<a name="copyFile-java.nio.file.Path-java.nio.file.Path-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copyFile</h4>
<pre>public static&nbsp;void&nbsp;copyFile(java.nio.file.Path&nbsp;from,
                            java.nio.file.Path&nbsp;to)
                     throws java.io.IOException</pre>
<div class="block">文件复制. @see <code>Files.copy(java.nio.file.Path, java.nio.file.Path, java.nio.file.CopyOption...)</code></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>from</code> - 如果为null，或文件不存在或者是目录，，抛出异常</dd>
<dd><code>to</code> - 如果to为null，或文件存在但是一个目录，抛出异常</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="copyDir-java.io.File-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copyDir</h4>
<pre>public static&nbsp;void&nbsp;copyDir(java.io.File&nbsp;from,
                           java.io.File&nbsp;to)
                    throws java.io.IOException</pre>
<div class="block">复制目录</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="copyDir-java.nio.file.Path-java.nio.file.Path-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copyDir</h4>
<pre>public static&nbsp;void&nbsp;copyDir(java.nio.file.Path&nbsp;from,
                           java.nio.file.Path&nbsp;to)
                    throws java.io.IOException</pre>
<div class="block">复制目录</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="moveFile-java.io.File-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>moveFile</h4>
<pre>public static&nbsp;void&nbsp;moveFile(java.io.File&nbsp;from,
                            java.io.File&nbsp;to)
                     throws java.io.IOException</pre>
<div class="block">文件移动/重命名.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Files#move}</code></dd>
</dl>
</li>
</ul>
<a name="moveFile-java.nio.file.Path-java.nio.file.Path-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>moveFile</h4>
<pre>public static&nbsp;void&nbsp;moveFile(java.nio.file.Path&nbsp;from,
                            java.nio.file.Path&nbsp;to)
                     throws java.io.IOException</pre>
<div class="block">文件移动/重命名.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Files#move}</code></dd>
</dl>
</li>
</ul>
<a name="moveDir-java.io.File-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>moveDir</h4>
<pre>public static&nbsp;void&nbsp;moveDir(java.io.File&nbsp;from,
                           java.io.File&nbsp;to)
                    throws java.io.IOException</pre>
<div class="block">目录移动/重命名</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="touch-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>touch</h4>
<pre>public static&nbsp;void&nbsp;touch(java.lang.String&nbsp;filePath)
                  throws java.io.IOException</pre>
<div class="block">创建文件或更新时间戳.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>com.google.common.io.Files#touch}</code></dd>
</dl>
</li>
</ul>
<a name="touch-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>touch</h4>
<pre>public static&nbsp;void&nbsp;touch(java.io.File&nbsp;file)
                  throws java.io.IOException</pre>
<div class="block">创建文件或更新时间戳.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>com.google.common.io.Files#touch}</code></dd>
</dl>
</li>
</ul>
<a name="deleteFile-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteFile</h4>
<pre>public static&nbsp;void&nbsp;deleteFile(java.io.File&nbsp;file)
                       throws java.io.IOException</pre>
<div class="block">删除文件.
 
 如果文件不存在或者是目录，则不做修改</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="deleteFile-java.nio.file.Path-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteFile</h4>
<pre>public static&nbsp;void&nbsp;deleteFile(java.nio.file.Path&nbsp;path)
                       throws java.io.IOException</pre>
<div class="block">删除文件.
 
 如果文件不存在或者是目录，则不做修改</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="deleteDir-java.nio.file.Path-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteDir</h4>
<pre>public static&nbsp;void&nbsp;deleteDir(java.nio.file.Path&nbsp;dir)
                      throws java.io.IOException</pre>
<div class="block">删除目录及所有子目录/文件</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Files#walkFileTree}</code></dd>
</dl>
</li>
</ul>
<a name="deleteDir-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteDir</h4>
<pre>public static&nbsp;void&nbsp;deleteDir(java.io.File&nbsp;dir)
                      throws java.io.IOException</pre>
<div class="block">删除目录及所有子目录/文件</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="isDirExists-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDirExists</h4>
<pre>public static&nbsp;boolean&nbsp;isDirExists(java.lang.String&nbsp;dirPath)</pre>
<div class="block">判断目录是否存在, from Jodd</div>
</li>
</ul>
<a name="isDirExists-java.nio.file.Path-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDirExists</h4>
<pre>public static&nbsp;boolean&nbsp;isDirExists(java.nio.file.Path&nbsp;dirPath)</pre>
</li>
</ul>
<a name="isDirExists-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDirExists</h4>
<pre>public static&nbsp;boolean&nbsp;isDirExists(java.io.File&nbsp;dir)</pre>
<div class="block">判断目录是否存在, from Jodd</div>
</li>
</ul>
<a name="makesureDirExists-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>makesureDirExists</h4>
<pre>public static&nbsp;void&nbsp;makesureDirExists(java.lang.String&nbsp;dirPath)
                              throws java.io.IOException</pre>
<div class="block">确保目录存在, 如不存在则创建</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="makesureDirExists-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>makesureDirExists</h4>
<pre>public static&nbsp;void&nbsp;makesureDirExists(java.io.File&nbsp;file)
                              throws java.io.IOException</pre>
<div class="block">确保目录存在, 如不存在则创建</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="makesureDirExists-java.nio.file.Path-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>makesureDirExists</h4>
<pre>public static&nbsp;void&nbsp;makesureDirExists(java.nio.file.Path&nbsp;dirPath)
                              throws java.io.IOException</pre>
<div class="block">确保目录存在, 如不存在则创建.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Files#createDirectories}</code></dd>
</dl>
</li>
</ul>
<a name="makesureParentDirExists-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>makesureParentDirExists</h4>
<pre>public static&nbsp;void&nbsp;makesureParentDirExists(java.io.File&nbsp;file)
                                    throws java.io.IOException</pre>
<div class="block">确保父目录及其父目录直到根目录都已经创建.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="isFileExists-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isFileExists</h4>
<pre>public static&nbsp;boolean&nbsp;isFileExists(java.lang.String&nbsp;fileName)</pre>
<div class="block">判断文件是否存在, from Jodd.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Files#exists}</code>, 
<code>Files#isRegularFile}</code></dd>
</dl>
</li>
</ul>
<a name="isFileExists-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isFileExists</h4>
<pre>public static&nbsp;boolean&nbsp;isFileExists(java.io.File&nbsp;file)</pre>
<div class="block">判断文件是否存在, from Jodd.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Files#exists}</code>, 
<code>Files#isRegularFile}</code></dd>
</dl>
</li>
</ul>
<a name="isFileExists-java.nio.file.Path-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isFileExists</h4>
<pre>public static&nbsp;boolean&nbsp;isFileExists(java.nio.file.Path&nbsp;path)</pre>
<div class="block">判断文件是否存在, from Jodd.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Files#exists}</code>, 
<code>Files#isRegularFile}</code></dd>
</dl>
</li>
</ul>
<a name="createTempDir--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTempDir</h4>
<pre>public static&nbsp;java.nio.file.Path&nbsp;createTempDir()
                                        throws java.io.IOException</pre>
<div class="block">在临时目录创建临时目录，命名为${毫秒级时间戳}-${同一毫秒内的随机数}.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Files#createTempDirectory}</code></dd>
</dl>
</li>
</ul>
<a name="createTempFile--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTempFile</h4>
<pre>public static&nbsp;java.nio.file.Path&nbsp;createTempFile()
                                         throws java.io.IOException</pre>
<div class="block">在临时目录创建临时文件，命名为tmp-${random.nextLong()}.tmp</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Files#createTempFile}</code></dd>
</dl>
</li>
</ul>
<a name="createTempFile-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTempFile</h4>
<pre>public static&nbsp;java.nio.file.Path&nbsp;createTempFile(java.lang.String&nbsp;prefix,
                                                java.lang.String&nbsp;suffix)
                                         throws java.io.IOException</pre>
<div class="block">在临时目录创建临时文件，命名为${prefix}${random.nextLong()}${suffix}</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Files#createTempFile}</code></dd>
</dl>
</li>
</ul>
<a name="getFileName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFileName</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getFileName(java.lang.String&nbsp;fullName)</pre>
<div class="block">获取文件名(不包含路径)</div>
</li>
</ul>
<a name="getFileExtension-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFileExtension</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getFileExtension(java.io.File&nbsp;file)</pre>
<div class="block">获取文件名的扩展名部分(不包含.)</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>com.google.common.io.Files#getFileExtension}</code></dd>
</dl>
</li>
</ul>
<a name="getFileExtension-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getFileExtension</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getFileExtension(java.lang.String&nbsp;fullName)</pre>
<div class="block">获取文件名的扩展名部分(不包含.)</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>com.google.common.io.Files#getFileExtension}</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/io/FileTreeWalkerTest.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/io/FileUtilTest.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/io/FileUtil.html" target="_top">框架</a></li>
<li><a href="FileUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:31 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>FileTreeWalker</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="FileTreeWalker";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/io/FilePathUtilTest.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/io/FileTreeWalker.AntPathFilter.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/io/FileTreeWalker.html" target="_top">框架</a></li>
<li><a href="FileTreeWalker.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.io</div>
<h2 title="类 FileTreeWalker" class="title">类 FileTreeWalker</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.io.FileTreeWalker</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">FileTreeWalker</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>嵌套类概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="嵌套类概要表, 列表嵌套类和解释">
<caption><span>嵌套类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">类和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileTreeWalker.AntPathFilter.html" title="com.huazheng.tunny.tools.io中的类">FileTreeWalker.AntPathFilter</a></span></code>
<div class="block">以ant风格的path为filter，配合fileTreeTraverser使用.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileTreeWalker.FileExtensionFilter.html" title="com.huazheng.tunny.tools.io中的类">FileTreeWalker.FileExtensionFilter</a></span></code>
<div class="block">以文件名后缀做filter，配合fileTreeTraverser使用</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileTreeWalker.RegexFileNameFilter.html" title="com.huazheng.tunny.tools.io中的类">FileTreeWalker.RegexFileNameFilter</a></span></code>
<div class="block">以文件名正则表达式为filter，配合fileTreeTraverser使用</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileTreeWalker.WildcardFileNameFilter.html" title="com.huazheng.tunny.tools.io中的类">FileTreeWalker.WildcardFileNameFilter</a></span></code>
<div class="block">以文件名通配符为filter，配合fileTreeTraverser使用.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileTreeWalker.html#FileTreeWalker--">FileTreeWalker</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static com.google.common.collect.TreeTraverser&lt;java.io.File&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileTreeWalker.html#fileTreeTraverser--">fileTreeTraverser</a></span>()</code>
<div class="block">直接使用Guava的TreeTraverser，获得更大的灵活度, 比如加入各类filter，前序/后序的选择，一边遍历一边操作
 
 
 FileUtil.fileTreeTraverser().preOrderTraversal(root).iterator();
 </div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static java.util.List&lt;java.io.File&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileTreeWalker.html#listAll-java.io.File-">listAll</a></span>(java.io.File&nbsp;rootDir)</code>
<div class="block">前序递归列出所有文件, 包含文件与目录，及根目录本身.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static java.util.List&lt;java.io.File&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileTreeWalker.html#listFile-java.io.File-">listFile</a></span>(java.io.File&nbsp;rootDir)</code>
<div class="block">前序递归列出所有文件, 只包含文件.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static java.util.List&lt;java.io.File&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileTreeWalker.html#listFileWithAntPath-java.io.File-java.lang.String-">listFileWithAntPath</a></span>(java.io.File&nbsp;rootDir,
                   java.lang.String&nbsp;antPathPattern)</code>
<div class="block">前序递归列出所有文件, 列出符合ant path风格表达式的文件
 
 如 ("/a/b/hello.txt", "he.*\.txt") 将被返回</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static java.util.List&lt;java.io.File&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileTreeWalker.html#listFileWithExtension-java.io.File-java.lang.String-">listFileWithExtension</a></span>(java.io.File&nbsp;rootDir,
                     java.lang.String&nbsp;extension)</code>
<div class="block">前序递归列出所有文件, 列出后缀名匹配的文件.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static java.util.List&lt;java.io.File&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileTreeWalker.html#listFileWithRegexFileName-java.io.File-java.lang.String-">listFileWithRegexFileName</a></span>(java.io.File&nbsp;rootDir,
                         java.lang.String&nbsp;regexFileNamePattern)</code>
<div class="block">前序递归列出所有文件, 列出文件名匹配正则表达式的文件
 
 如 ("/a/b/hello.txt", "he.*\.txt") 将被返回</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static java.util.List&lt;java.io.File&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/FileTreeWalker.html#listFileWithWildcardFileName-java.io.File-java.lang.String-">listFileWithWildcardFileName</a></span>(java.io.File&nbsp;rootDir,
                            java.lang.String&nbsp;fileNamePattern)</code>
<div class="block">前序递归列出所有文件, 列出文件名匹配通配符的文件
 
 如 ("/a/b/hello.txt", "he*") 将被返回</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="FileTreeWalker--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>FileTreeWalker</h4>
<pre>public&nbsp;FileTreeWalker()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="listAll-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>listAll</h4>
<pre>public static&nbsp;java.util.List&lt;java.io.File&gt;&nbsp;listAll(java.io.File&nbsp;rootDir)</pre>
<div class="block">前序递归列出所有文件, 包含文件与目录，及根目录本身.
 
 前序即先列出父目录，在列出子目录. 如要后序遍历, 直接使用Files.fileTreeTraverser()</div>
</li>
</ul>
<a name="listFile-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>listFile</h4>
<pre>public static&nbsp;java.util.List&lt;java.io.File&gt;&nbsp;listFile(java.io.File&nbsp;rootDir)</pre>
<div class="block">前序递归列出所有文件, 只包含文件.</div>
</li>
</ul>
<a name="listFileWithExtension-java.io.File-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>listFileWithExtension</h4>
<pre>public static&nbsp;java.util.List&lt;java.io.File&gt;&nbsp;listFileWithExtension(java.io.File&nbsp;rootDir,
                                                                 java.lang.String&nbsp;extension)</pre>
<div class="block">前序递归列出所有文件, 列出后缀名匹配的文件. （后缀名不包含.）</div>
</li>
</ul>
<a name="listFileWithWildcardFileName-java.io.File-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>listFileWithWildcardFileName</h4>
<pre>public static&nbsp;java.util.List&lt;java.io.File&gt;&nbsp;listFileWithWildcardFileName(java.io.File&nbsp;rootDir,
                                                                        java.lang.String&nbsp;fileNamePattern)</pre>
<div class="block">前序递归列出所有文件, 列出文件名匹配通配符的文件
 
 如 ("/a/b/hello.txt", "he*") 将被返回</div>
</li>
</ul>
<a name="listFileWithRegexFileName-java.io.File-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>listFileWithRegexFileName</h4>
<pre>public static&nbsp;java.util.List&lt;java.io.File&gt;&nbsp;listFileWithRegexFileName(java.io.File&nbsp;rootDir,
                                                                     java.lang.String&nbsp;regexFileNamePattern)</pre>
<div class="block">前序递归列出所有文件, 列出文件名匹配正则表达式的文件
 
 如 ("/a/b/hello.txt", "he.*\.txt") 将被返回</div>
</li>
</ul>
<a name="listFileWithAntPath-java.io.File-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>listFileWithAntPath</h4>
<pre>public static&nbsp;java.util.List&lt;java.io.File&gt;&nbsp;listFileWithAntPath(java.io.File&nbsp;rootDir,
                                                               java.lang.String&nbsp;antPathPattern)</pre>
<div class="block">前序递归列出所有文件, 列出符合ant path风格表达式的文件
 
 如 ("/a/b/hello.txt", "he.*\.txt") 将被返回</div>
</li>
</ul>
<a name="fileTreeTraverser--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>fileTreeTraverser</h4>
<pre>public static&nbsp;com.google.common.collect.TreeTraverser&lt;java.io.File&gt;&nbsp;fileTreeTraverser()</pre>
<div class="block">直接使用Guava的TreeTraverser，获得更大的灵活度, 比如加入各类filter，前序/后序的选择，一边遍历一边操作
 
 <pre>
 FileUtil.fileTreeTraverser().preOrderTraversal(root).iterator();
 </pre></div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/io/FilePathUtilTest.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/io/FileTreeWalker.AntPathFilter.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/io/FileTreeWalker.html" target="_top">框架</a></li>
<li><a href="FileTreeWalker.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

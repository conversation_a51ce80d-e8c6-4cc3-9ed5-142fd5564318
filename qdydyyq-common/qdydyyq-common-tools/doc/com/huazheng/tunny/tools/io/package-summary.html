<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:35 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>com.huazheng.tunny.tools.io</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.huazheng.tunny.tools.io";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li class="navBarCell1Rev">程序包</li>
<li>类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/concurrent/type/package-summary.html">上一个程序包</a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/io/type/package-summary.html">下一个程序包</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/io/package-summary.html" target="_top">框架</a></li>
<li><a href="package-summary.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="程序包" class="title">程序包&nbsp;com.huazheng.tunny.tools.io</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="类概要表, 列表类和解释">
<caption><span>类概要</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">类</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/io/FilePathUtil.html" title="com.huazheng.tunny.tools.io中的类">FilePathUtil</a></td>
<td class="colLast">
<div class="block">关于文件路径的工具集.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/io/FilePathUtilTest.html" title="com.huazheng.tunny.tools.io中的类">FilePathUtilTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/io/FileTreeWalker.html" title="com.huazheng.tunny.tools.io中的类">FileTreeWalker</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/io/FileTreeWalker.AntPathFilter.html" title="com.huazheng.tunny.tools.io中的类">FileTreeWalker.AntPathFilter</a></td>
<td class="colLast">
<div class="block">以ant风格的path为filter，配合fileTreeTraverser使用.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/io/FileTreeWalker.FileExtensionFilter.html" title="com.huazheng.tunny.tools.io中的类">FileTreeWalker.FileExtensionFilter</a></td>
<td class="colLast">
<div class="block">以文件名后缀做filter，配合fileTreeTraverser使用</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/io/FileTreeWalker.RegexFileNameFilter.html" title="com.huazheng.tunny.tools.io中的类">FileTreeWalker.RegexFileNameFilter</a></td>
<td class="colLast">
<div class="block">以文件名正则表达式为filter，配合fileTreeTraverser使用</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/io/FileTreeWalker.WildcardFileNameFilter.html" title="com.huazheng.tunny.tools.io中的类">FileTreeWalker.WildcardFileNameFilter</a></td>
<td class="colLast">
<div class="block">以文件名通配符为filter，配合fileTreeTraverser使用.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/io/FileTreeWalkerTest.html" title="com.huazheng.tunny.tools.io中的类">FileTreeWalkerTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></td>
<td class="colLast">
<div class="block">关于文件的工具集.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/io/FileUtilTest.html" title="com.huazheng.tunny.tools.io中的类">FileUtilTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/io/IOUtil.html" title="com.huazheng.tunny.tools.io中的类">IOUtil</a></td>
<td class="colLast">
<div class="block">IO Stream/Reader相关工具集.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/io/IOUtilTest.html" title="com.huazheng.tunny.tools.io中的类">IOUtilTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/io/ResourceUtil.html" title="com.huazheng.tunny.tools.io中的类">ResourceUtil</a></td>
<td class="colLast">
<div class="block">针对Jar包内的文件的工具类.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/io/ResourceUtilTest.html" title="com.huazheng.tunny.tools.io中的类">ResourceUtilTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/io/URLResourceTest.html" title="com.huazheng.tunny.tools.io中的类">URLResourceTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/huazheng/tunny/tools/io/URLResourceUtil.html" title="com.huazheng.tunny.tools.io中的类">URLResourceUtil</a></td>
<td class="colLast">
<div class="block">兼容文件url为无前缀, classpath:, file:// 三种方式的Resource读取工具集
 
 e.g: classpath:com/myapp/config.xml, file:///data/config.xml, /data/config.xml
 
 参考Spring ResourceUtils</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li class="navBarCell1Rev">程序包</li>
<li>类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/concurrent/type/package-summary.html">上一个程序包</a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/io/type/package-summary.html">下一个程序包</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/io/package-summary.html" target="_top">框架</a></li>
<li><a href="package-summary.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

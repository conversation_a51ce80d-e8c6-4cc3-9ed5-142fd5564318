<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:32 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ResourceUtil</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ResourceUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/io/IOUtilTest.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/io/ResourceUtilTest.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/io/ResourceUtil.html" target="_top">框架</a></li>
<li><a href="ResourceUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.io</div>
<h2 title="类 ResourceUtil" class="title">类 ResourceUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.io.ResourceUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ResourceUtil</span>
extends java.lang.Object</pre>
<div class="block">针对Jar包内的文件的工具类.
 <p>
 1.ClassLoader
 
 不指定contextClass时，优先使用Thread.getContextClassLoader()， 如果ContextClassLoader未设置则使用Guava Resources类的ClassLoader
 
 指定contextClass时，则直接使用该contextClass的ClassLoader.
 <p>
 2.路径
 
 不指定contextClass时，按URLClassLoader的实现, 从jar file中查找resourceName，
 
 所以resourceName无需以"/"打头即表示jar file中的根目录，带了"/" 反而导致JarFile.getEntry(resouceName)时没有返回.
 
 指定contextClass时，class.getResource()会先对name进行处理再交给classLoader，打头的"/"的会被去除，不以"/"打头则表示与该contextClass package的相对路径,
 会先转为绝对路径.
 <p>
 3.同名资源
 
 如果有多个同名资源，除非调用getResources()获取全部资源，否则在URLClassLoader中按ClassPath顺序打开第一个命中的Jar文件.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/ResourceUtil.html#ResourceUtil--">ResourceUtil</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static java.io.InputStream</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/ResourceUtil.html#asStream-java.lang.Class-java.lang.String-">asStream</a></span>(java.lang.Class&lt;?&gt;&nbsp;contextClass,
        java.lang.String&nbsp;resourceName)</code>
<div class="block">读取文件的每一行，读取规则见本类注释.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static java.io.InputStream</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/ResourceUtil.html#asStream-java.lang.String-">asStream</a></span>(java.lang.String&nbsp;resourceName)</code>
<div class="block">读取规则见本类注释.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static java.net.URL</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/ResourceUtil.html#asUrl-java.lang.Class-java.lang.String-">asUrl</a></span>(java.lang.Class&lt;?&gt;&nbsp;contextClass,
     java.lang.String&nbsp;resourceName)</code>
<div class="block">读取规则见本类注释.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static java.net.URL</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/ResourceUtil.html#asUrl-java.lang.String-">asUrl</a></span>(java.lang.String&nbsp;resourceName)</code>
<div class="block">读取规则见本类注释.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static java.util.List&lt;java.net.URL&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/ResourceUtil.html#getResourcesQuietly-java.lang.String-">getResourcesQuietly</a></span>(java.lang.String&nbsp;resourceName)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static java.util.List&lt;java.net.URL&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/ResourceUtil.html#getResourcesQuietly-java.lang.String-java.lang.ClassLoader-">getResourcesQuietly</a></span>(java.lang.String&nbsp;resourceName,
                   java.lang.ClassLoader&nbsp;contextClassLoader)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static java.util.List&lt;java.lang.String&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/ResourceUtil.html#toLines-java.lang.Class-java.lang.String-">toLines</a></span>(java.lang.Class&lt;?&gt;&nbsp;contextClass,
       java.lang.String&nbsp;resourceName)</code>
<div class="block">读取文件的每一行，读取规则见本类注释.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static java.util.List&lt;java.lang.String&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/ResourceUtil.html#toLines-java.lang.String-">toLines</a></span>(java.lang.String&nbsp;resourceName)</code>
<div class="block">读取文件的每一行，读取规则见本类注释.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/ResourceUtil.html#toString-java.lang.Class-java.lang.String-">toString</a></span>(java.lang.Class&lt;?&gt;&nbsp;contextClass,
        java.lang.String&nbsp;resourceName)</code>
<div class="block">读取文件的每一行，读取规则见本类注释.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/ResourceUtil.html#toString-java.lang.String-">toString</a></span>(java.lang.String&nbsp;resourceName)</code>
<div class="block">读取文件的每一行，读取规则见本类注释.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="ResourceUtil--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ResourceUtil</h4>
<pre>public&nbsp;ResourceUtil()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="asUrl-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>asUrl</h4>
<pre>public static&nbsp;java.net.URL&nbsp;asUrl(java.lang.String&nbsp;resourceName)</pre>
<div class="block">读取规则见本类注释.</div>
</li>
</ul>
<a name="asUrl-java.lang.Class-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>asUrl</h4>
<pre>public static&nbsp;java.net.URL&nbsp;asUrl(java.lang.Class&lt;?&gt;&nbsp;contextClass,
                                 java.lang.String&nbsp;resourceName)</pre>
<div class="block">读取规则见本类注释.</div>
</li>
</ul>
<a name="asStream-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>asStream</h4>
<pre>public static&nbsp;java.io.InputStream&nbsp;asStream(java.lang.String&nbsp;resourceName)
                                    throws java.io.IOException</pre>
<div class="block">读取规则见本类注释.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="asStream-java.lang.Class-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>asStream</h4>
<pre>public static&nbsp;java.io.InputStream&nbsp;asStream(java.lang.Class&lt;?&gt;&nbsp;contextClass,
                                           java.lang.String&nbsp;resourceName)
                                    throws java.io.IOException</pre>
<div class="block">读取文件的每一行，读取规则见本类注释.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="toString-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toString</h4>
<pre>public static&nbsp;java.lang.String&nbsp;toString(java.lang.String&nbsp;resourceName)
                                 throws java.io.IOException</pre>
<div class="block">读取文件的每一行，读取规则见本类注释.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="toString-java.lang.Class-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toString</h4>
<pre>public static&nbsp;java.lang.String&nbsp;toString(java.lang.Class&lt;?&gt;&nbsp;contextClass,
                                        java.lang.String&nbsp;resourceName)
                                 throws java.io.IOException</pre>
<div class="block">读取文件的每一行，读取规则见本类注释.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="toLines-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toLines</h4>
<pre>public static&nbsp;java.util.List&lt;java.lang.String&gt;&nbsp;toLines(java.lang.String&nbsp;resourceName)
                                                throws java.io.IOException</pre>
<div class="block">读取文件的每一行，读取规则见本类注释.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="toLines-java.lang.Class-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toLines</h4>
<pre>public static&nbsp;java.util.List&lt;java.lang.String&gt;&nbsp;toLines(java.lang.Class&lt;?&gt;&nbsp;contextClass,
                                                       java.lang.String&nbsp;resourceName)
                                                throws java.io.IOException</pre>
<div class="block">读取文件的每一行，读取规则见本类注释.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="getResourcesQuietly-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResourcesQuietly</h4>
<pre>public static&nbsp;java.util.List&lt;java.net.URL&gt;&nbsp;getResourcesQuietly(java.lang.String&nbsp;resourceName)</pre>
</li>
</ul>
<a name="getResourcesQuietly-java.lang.String-java.lang.ClassLoader-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getResourcesQuietly</h4>
<pre>public static&nbsp;java.util.List&lt;java.net.URL&gt;&nbsp;getResourcesQuietly(java.lang.String&nbsp;resourceName,
                                                               java.lang.ClassLoader&nbsp;contextClassLoader)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/io/IOUtilTest.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/io/ResourceUtilTest.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/io/ResourceUtil.html" target="_top">框架</a></li>
<li><a href="ResourceUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

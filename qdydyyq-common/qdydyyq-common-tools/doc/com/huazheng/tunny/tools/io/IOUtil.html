<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:32 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>IOUtil</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IOUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/io/FileUtilTest.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/io/IOUtilTest.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/io/IOUtil.html" target="_top">框架</a></li>
<li><a href="IOUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.io</div>
<h2 title="类 IOUtil" class="title">类 IOUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.io.IOUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">IOUtil</span>
extends java.lang.Object</pre>
<div class="block">IO Stream/Reader相关工具集. 固定encoding为UTF8.
 
 建议使用Apache Commons IO和Guava关于IO的工具类(com.google.common.io.*), 在未引入Commons IO时可以用本类做最基本的事情.
 
 <p>
 1. 安静关闭Closeable对象
 <p>
 2. 读出InputStream/Reader全部内容到String 或 List<String>
 <p>
 3. 读出InputStream一行内容到String
 <p>
 4. 将String写到OutputStream/Writer
 <p>
 5. InputStream/Reader与OutputStream/Writer之间复制的copy</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/IOUtil.html#IOUtil--">IOUtil</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/IOUtil.html#closeQuietly-java.io.Closeable-">closeQuietly</a></span>(java.io.Closeable&nbsp;closeable)</code>
<div class="block">在final中安静的关闭, 不再往外抛出异常避免影响原有异常，最常用函数.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/IOUtil.html#copy-java.io.InputStream-java.io.OutputStream-">copy</a></span>(java.io.InputStream&nbsp;input,
    java.io.OutputStream&nbsp;output)</code>
<div class="block">在InputStream与OutputStream间复制内容</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/IOUtil.html#copy-java.io.Reader-java.io.Writer-">copy</a></span>(java.io.Reader&nbsp;input,
    java.io.Writer&nbsp;output)</code>
<div class="block">在Reader与Writer间复制内容</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/IOUtil.html#readLine-java.io.InputStream-">readLine</a></span>(java.io.InputStream&nbsp;input)</code>
<div class="block">读取一行数据，比如System.in的用户输入</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/IOUtil.html#readLine-java.io.Reader-">readLine</a></span>(java.io.Reader&nbsp;reader)</code>
<div class="block">读取一行数据</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static java.io.BufferedReader</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/IOUtil.html#toBufferedReader-java.io.Reader-">toBufferedReader</a></span>(java.io.Reader&nbsp;reader)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static java.util.List&lt;java.lang.String&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/IOUtil.html#toLines-java.io.InputStream-">toLines</a></span>(java.io.InputStream&nbsp;input)</code>
<div class="block">简单读取Reader的每行内容到List<String></div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static java.util.List&lt;java.lang.String&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/IOUtil.html#toLines-java.io.Reader-">toLines</a></span>(java.io.Reader&nbsp;input)</code>
<div class="block">简单读取Reader的每行内容到List<String></div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/IOUtil.html#toString-java.io.InputStream-">toString</a></span>(java.io.InputStream&nbsp;input)</code>
<div class="block">简单读取InputStream到String.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/IOUtil.html#toString-java.io.Reader-">toString</a></span>(java.io.Reader&nbsp;input)</code>
<div class="block">简单读取Reader到String</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/IOUtil.html#write-java.lang.String-java.io.OutputStream-">write</a></span>(java.lang.String&nbsp;data,
     java.io.OutputStream&nbsp;output)</code>
<div class="block">简单写入String到OutputStream.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/io/IOUtil.html#write-java.lang.String-java.io.Writer-">write</a></span>(java.lang.String&nbsp;data,
     java.io.Writer&nbsp;output)</code>
<div class="block">简单写入String到Writer.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="IOUtil--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>IOUtil</h4>
<pre>public&nbsp;IOUtil()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="closeQuietly-java.io.Closeable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>closeQuietly</h4>
<pre>public static&nbsp;void&nbsp;closeQuietly(java.io.Closeable&nbsp;closeable)</pre>
<div class="block">在final中安静的关闭, 不再往外抛出异常避免影响原有异常，最常用函数. 同时兼容Closeable为空未实际创建的情况.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Closeables#close}</code></dd>
</dl>
</li>
</ul>
<a name="toString-java.io.InputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toString</h4>
<pre>public static&nbsp;java.lang.String&nbsp;toString(java.io.InputStream&nbsp;input)
                                 throws java.io.IOException</pre>
<div class="block">简单读取InputStream到String.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="toString-java.io.Reader-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toString</h4>
<pre>public static&nbsp;java.lang.String&nbsp;toString(java.io.Reader&nbsp;input)
                                 throws java.io.IOException</pre>
<div class="block">简单读取Reader到String</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>CharStreams#toString}</code></dd>
</dl>
</li>
</ul>
<a name="toLines-java.io.InputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toLines</h4>
<pre>public static&nbsp;java.util.List&lt;java.lang.String&gt;&nbsp;toLines(java.io.InputStream&nbsp;input)
                                                throws java.io.IOException</pre>
<div class="block">简单读取Reader的每行内容到List<String></div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="toLines-java.io.Reader-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toLines</h4>
<pre>public static&nbsp;java.util.List&lt;java.lang.String&gt;&nbsp;toLines(java.io.Reader&nbsp;input)
                                                throws java.io.IOException</pre>
<div class="block">简单读取Reader的每行内容到List<String></div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>CharStreams#readLines}</code></dd>
</dl>
</li>
</ul>
<a name="readLine-java.io.InputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readLine</h4>
<pre>public static&nbsp;java.lang.String&nbsp;readLine(java.io.InputStream&nbsp;input)
                                 throws java.io.IOException</pre>
<div class="block">读取一行数据，比如System.in的用户输入</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="readLine-java.io.Reader-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readLine</h4>
<pre>public static&nbsp;java.lang.String&nbsp;readLine(java.io.Reader&nbsp;reader)
                                 throws java.io.IOException</pre>
<div class="block">读取一行数据</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="write-java.lang.String-java.io.OutputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>write</h4>
<pre>public static&nbsp;void&nbsp;write(java.lang.String&nbsp;data,
                         java.io.OutputStream&nbsp;output)
                  throws java.io.IOException</pre>
<div class="block">简单写入String到OutputStream.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="write-java.lang.String-java.io.Writer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>write</h4>
<pre>public static&nbsp;void&nbsp;write(java.lang.String&nbsp;data,
                         java.io.Writer&nbsp;output)
                  throws java.io.IOException</pre>
<div class="block">简单写入String到Writer.</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="copy-java.io.Reader-java.io.Writer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copy</h4>
<pre>public static&nbsp;long&nbsp;copy(java.io.Reader&nbsp;input,
                        java.io.Writer&nbsp;output)
                 throws java.io.IOException</pre>
<div class="block">在Reader与Writer间复制内容</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>CharStreams#copy}</code></dd>
</dl>
</li>
</ul>
<a name="copy-java.io.InputStream-java.io.OutputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copy</h4>
<pre>public static&nbsp;long&nbsp;copy(java.io.InputStream&nbsp;input,
                        java.io.OutputStream&nbsp;output)
                 throws java.io.IOException</pre>
<div class="block">在InputStream与OutputStream间复制内容</div>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>ByteStreams#copy}</code></dd>
</dl>
</li>
</ul>
<a name="toBufferedReader-java.io.Reader-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>toBufferedReader</h4>
<pre>public static&nbsp;java.io.BufferedReader&nbsp;toBufferedReader(java.io.Reader&nbsp;reader)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/io/FileUtilTest.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/io/IOUtilTest.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/io/IOUtil.html" target="_top">框架</a></li>
<li><a href="IOUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

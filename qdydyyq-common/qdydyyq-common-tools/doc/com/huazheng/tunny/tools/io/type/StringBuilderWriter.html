<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:34 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>StringBuilderWriter</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="StringBuilderWriter";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li>下一个类</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/huazheng/tunny/tools/io/type/StringBuilderWriter.html" target="_top">框架</a></li>
<li><a href="StringBuilderWriter.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.java.io.Writer">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.io.type</div>
<h2 title="类 StringBuilderWriter" class="title">类 StringBuilderWriter</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.io.Writer</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.io.type.StringBuilderWriter</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>java.io.Closeable, java.io.Flushable, java.io.Serializable, java.lang.Appendable, java.lang.AutoCloseable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">StringBuilderWriter</span>
extends java.io.Writer
implements java.io.Serializable</pre>
<div class="block">JDK的java.io.StringWriter使用StringBuffer，移植Commons IO使用StringBuilder的版本.
 
 https://github.com/apache/commons-io/blob/master/src/main/java/org/apache/commons/io/output/StringBuilderWriter.java
 
 <code>Writer</code> implementation that outputs to a <code>StringBuilder</code>.
 <p>
 <strong>NOTE:</strong> This implementation, as an alternative to <code>java.io.StringWriter</code>, provides an
 <i>un-synchronized</i> (i.e. for use in a single thread) implementation for better performance. For safe usage with
 multiple <code>Thread</code>s then <code>java.io.StringWriter</code> should be used.</div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>2.0</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../../serialized-form.html#com.huazheng.tunny.tools.io.type.StringBuilderWriter">序列化表格</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.io.Writer">
<!--   -->
</a>
<h3>从类继承的字段&nbsp;java.io.Writer</h3>
<code>lock</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html#StringBuilderWriter--">StringBuilderWriter</a></span>()</code>
<div class="block">Constructs a new <code>StringBuilder</code> instance with default capacity.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html#StringBuilderWriter-int-">StringBuilderWriter</a></span>(int&nbsp;capacity)</code>
<div class="block">Constructs a new <code>StringBuilder</code> instance with the specified capacity.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html#StringBuilderWriter-java.lang.StringBuilder-">StringBuilderWriter</a></span>(java.lang.StringBuilder&nbsp;builder)</code>
<div class="block">Constructs a new instance with the specified <code>StringBuilder</code>.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.io.Writer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html#append-char-">append</a></span>(char&nbsp;value)</code>
<div class="block">Appends a single character to this Writer.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.io.Writer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html#append-java.lang.CharSequence-">append</a></span>(java.lang.CharSequence&nbsp;value)</code>
<div class="block">Appends a character sequence to this Writer.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.io.Writer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html#append-java.lang.CharSequence-int-int-">append</a></span>(java.lang.CharSequence&nbsp;value,
      int&nbsp;start,
      int&nbsp;end)</code>
<div class="block">Appends a portion of a character sequence to the <code>StringBuilder</code>.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html#close--">close</a></span>()</code>
<div class="block">Closing this writer has no effect.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html#flush--">flush</a></span>()</code>
<div class="block">Flushing this writer has no effect.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.StringBuilder</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html#getBuilder--">getBuilder</a></span>()</code>
<div class="block">Returns the underlying builder.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html#toString--">toString</a></span>()</code>
<div class="block">Returns <code>StringBuilder.toString()</code>.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html#write-char:A-int-int-">write</a></span>(char[]&nbsp;value,
     int&nbsp;offset,
     int&nbsp;length)</code>
<div class="block">Writes a portion of a character array to the <code>StringBuilder</code>.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html#write-java.lang.String-">write</a></span>(java.lang.String&nbsp;value)</code>
<div class="block">Writes a String to the <code>StringBuilder</code>.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.io.Writer">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.io.Writer</h3>
<code>write, write, write</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="StringBuilderWriter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StringBuilderWriter</h4>
<pre>public&nbsp;StringBuilderWriter()</pre>
<div class="block">Constructs a new <code>StringBuilder</code> instance with default capacity.</div>
</li>
</ul>
<a name="StringBuilderWriter-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StringBuilderWriter</h4>
<pre>public&nbsp;StringBuilderWriter(int&nbsp;capacity)</pre>
<div class="block">Constructs a new <code>StringBuilder</code> instance with the specified capacity.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>capacity</code> - The initial capacity of the underlying <code>StringBuilder</code></dd>
</dl>
</li>
</ul>
<a name="StringBuilderWriter-java.lang.StringBuilder-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>StringBuilderWriter</h4>
<pre>public&nbsp;StringBuilderWriter(java.lang.StringBuilder&nbsp;builder)</pre>
<div class="block">Constructs a new instance with the specified <code>StringBuilder</code>.

 <p>
 If <code>builder</code> is null a new instance with default capacity will be created.
 </p></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>builder</code> - The String builder. May be null.</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="append-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>append</h4>
<pre>public&nbsp;java.io.Writer&nbsp;append(char&nbsp;value)</pre>
<div class="block">Appends a single character to this Writer.</div>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>append</code>&nbsp;在接口中&nbsp;<code>java.lang.Appendable</code></dd>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>append</code>&nbsp;在类中&nbsp;<code>java.io.Writer</code></dd>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>value</code> - The character to append</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>This writer instance</dd>
</dl>
</li>
</ul>
<a name="append-java.lang.CharSequence-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>append</h4>
<pre>public&nbsp;java.io.Writer&nbsp;append(java.lang.CharSequence&nbsp;value)</pre>
<div class="block">Appends a character sequence to this Writer.</div>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>append</code>&nbsp;在接口中&nbsp;<code>java.lang.Appendable</code></dd>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>append</code>&nbsp;在类中&nbsp;<code>java.io.Writer</code></dd>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>value</code> - The character to append</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>This writer instance</dd>
</dl>
</li>
</ul>
<a name="append-java.lang.CharSequence-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>append</h4>
<pre>public&nbsp;java.io.Writer&nbsp;append(java.lang.CharSequence&nbsp;value,
                             int&nbsp;start,
                             int&nbsp;end)</pre>
<div class="block">Appends a portion of a character sequence to the <code>StringBuilder</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>append</code>&nbsp;在接口中&nbsp;<code>java.lang.Appendable</code></dd>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>append</code>&nbsp;在类中&nbsp;<code>java.io.Writer</code></dd>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>value</code> - The character to append</dd>
<dd><code>start</code> - The index of the first character</dd>
<dd><code>end</code> - The index of the last character + 1</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>This writer instance</dd>
</dl>
</li>
</ul>
<a name="close--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>close</h4>
<pre>public&nbsp;void&nbsp;close()</pre>
<div class="block">Closing this writer has no effect.</div>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>close</code>&nbsp;在接口中&nbsp;<code>java.io.Closeable</code></dd>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>close</code>&nbsp;在接口中&nbsp;<code>java.lang.AutoCloseable</code></dd>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>close</code>&nbsp;在类中&nbsp;<code>java.io.Writer</code></dd>
</dl>
</li>
</ul>
<a name="flush--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>flush</h4>
<pre>public&nbsp;void&nbsp;flush()</pre>
<div class="block">Flushing this writer has no effect.</div>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>flush</code>&nbsp;在接口中&nbsp;<code>java.io.Flushable</code></dd>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>flush</code>&nbsp;在类中&nbsp;<code>java.io.Writer</code></dd>
</dl>
</li>
</ul>
<a name="write-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>write</h4>
<pre>public&nbsp;void&nbsp;write(java.lang.String&nbsp;value)</pre>
<div class="block">Writes a String to the <code>StringBuilder</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>write</code>&nbsp;在类中&nbsp;<code>java.io.Writer</code></dd>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>value</code> - The value to write</dd>
</dl>
</li>
</ul>
<a name="write-char:A-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>write</h4>
<pre>public&nbsp;void&nbsp;write(char[]&nbsp;value,
                  int&nbsp;offset,
                  int&nbsp;length)</pre>
<div class="block">Writes a portion of a character array to the <code>StringBuilder</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>write</code>&nbsp;在类中&nbsp;<code>java.io.Writer</code></dd>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>value</code> - The value to write</dd>
<dd><code>offset</code> - The index of the first character</dd>
<dd><code>length</code> - The number of characters to write</dd>
</dl>
</li>
</ul>
<a name="getBuilder--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBuilder</h4>
<pre>public&nbsp;java.lang.StringBuilder&nbsp;getBuilder()</pre>
<div class="block">Returns the underlying builder.</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>The underlying builder</dd>
</dl>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;java.lang.String&nbsp;toString()</pre>
<div class="block">Returns <code>StringBuilder.toString()</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>toString</code>&nbsp;在类中&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>The contents of the String builder.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li>下一个类</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/huazheng/tunny/tools/io/type/StringBuilderWriter.html" target="_top">框架</a></li>
<li><a href="StringBuilderWriter.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.java.io.Writer">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>

<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.huazheng</groupId>
		<artifactId>qdydyyq-common</artifactId>
		<version>1.3.2</version>
	</parent>

	<groupId>com.huazheng</groupId>
	<artifactId>qdydyyq-common-starter</artifactId>
	<version>1.3.2</version>
	<packaging>jar</packaging>
	<name>qdydyyq-common-starter</name>
	<description>tunny 配置类</description>

	<dependencies>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<artifactId>qdydyyq-common-core</artifactId>
			<groupId>com.huazheng</groupId>
			<version>1.3.2</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-autoconfigure</artifactId>
			<version>2.0.4.RELEASE</version>
		</dependency>
	</dependencies>
	<build>
		<finalName>${project.artifactId}-${maven.build.timestamp}</finalName>
	</build>
	<properties>
		<!--自定义默认的编码格式-->
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<!--自定义默认的时间格式 年-月-日-时-分-->
		<maven.build.timestamp.format>
			yyyy-MM-dd_HH_mm
		</maven.build.timestamp.format>
	</properties>
</project>

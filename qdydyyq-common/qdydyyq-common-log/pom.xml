<?xml version="1.0" encoding="UTF-8"?>


<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.huazheng</groupId>
		<artifactId>qdydyyq-common</artifactId>
		<version>1.3.2</version>
	</parent>

	<artifactId>qdydyyq-common-log</artifactId>
	<version>1.3.2</version>
	<packaging>jar</packaging>
	<name>qdydyyq-common-log</name>
	<description>tunny 日志服务</description>


	<dependencies>
		<!--工具类核心包-->
		<dependency>
			<groupId>com.huazheng</groupId>
			<artifactId>qdydyyq-common-core</artifactId>
			<version>1.3.2</version>
		</dependency>
		<!--开发工具包-->
		<dependency>
			<groupId>com.huazheng</groupId>
			<artifactId>qdydyyq-common-tools</artifactId>
			<version>1.3.2</version>
			<exclusions>
				<exclusion>
					<groupId>com.google.guava</groupId>
					<artifactId>guava</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-netflix-core</artifactId>
			<version>1.4.3.RELEASE</version>
		</dependency>

		<!--UPMS接口模块-->
		<dependency>
			<groupId>com.huazheng</groupId>
			<artifactId>qdydyyq-admin-client</artifactId>
			<version>1.3.2</version>

		</dependency>

	</dependencies>
	<build>
		<finalName>${project.artifactId}-${maven.build.timestamp}</finalName>
	</build>
	<properties>
		<!--自定义默认的编码格式-->
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<!--自定义默认的时间格式 年-月-日-时-分-->
		<maven.build.timestamp.format>
			yyyy-MM-dd_HH_mm
		</maven.build.timestamp.format>
	</properties>
</project>

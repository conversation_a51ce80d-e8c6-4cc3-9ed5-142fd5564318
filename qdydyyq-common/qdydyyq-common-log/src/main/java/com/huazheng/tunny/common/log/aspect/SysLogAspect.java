package com.huazheng.tunny.common.log.aspect;

import com.huazheng.tunny.admin.api.entity.SysLog;
import com.huazheng.tunny.admin.api.feign.RemoteLogService;
import com.huazheng.tunny.common.log.util.SysLogUtils;
import com.huazheng.tunny.common.core.util.SpringContextHolder;
import com.huazheng.tunny.common.log.event.SysLogEvent;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 操作日志使用spring event异步入库
 *
 */
@Aspect
@Slf4j
public class SysLogAspect {
	@Autowired
	RemoteLogService remoteLogService;
	@Around("@annotation(sysLog)")
	public Object aroundWxApi(ProceedingJoinPoint point, com.huazheng.tunny.common.log.annotation.SysLog sysLog) throws Throwable {
		String strClassName = point.getTarget().getClass().getName();
		String strMethodName = point.getSignature().getName();
		log.debug("[类名]:{},[方法]:{}", strClassName, strMethodName);

		SysLog logVo = SysLogUtils.getSysLog();
		logVo.setTitle(sysLog.value());
		// 发送异步日志事件
		Long startTime = System.currentTimeMillis();
		Object obj = point.proceed();
		Long endTime = System.currentTimeMillis();
		logVo.setTime(endTime - startTime);
		remoteLogService.saveLog(logVo);
		//推送事件
		//SpringContextHolder.publishEvent(new SysLogEvent(logVo));
		return obj;
	}

}

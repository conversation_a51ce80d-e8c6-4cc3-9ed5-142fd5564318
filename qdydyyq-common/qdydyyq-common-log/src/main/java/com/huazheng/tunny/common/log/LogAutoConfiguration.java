
package com.huazheng.tunny.common.log;

import com.huazheng.tunny.admin.api.feign.RemoteLogService;
import com.huazheng.tunny.common.log.aspect.SysLogAspect;
import com.huazheng.tunny.common.log.event.SysLogListener;
import lombok.AllArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.cloud.netflix.feign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 日志自动配置
 */
@Configuration
@AllArgsConstructor
@ConditionalOnWebApplication
@EnableFeignClients({"com.huazheng.tunny.admin.api.feign"})
public class LogAutoConfiguration {
	private final RemoteLogService remoteLogService;

	public SysLogListener sysLogListener() {
		return new SysLogListener(remoteLogService);
	}

	@Bean
	public SysLogAspect sysLogAspect() {
		return new SysLogAspect();
	}

}

package com.huazheng.tunny.common.log.aspect;

import com.huazheng.tunny.admin.api.entity.SysLog;
import com.huazheng.tunny.admin.api.feign.RemoteLogService;
import com.huazheng.tunny.common.log.util.SysLogUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Tunny操作日志切面逻辑
 *
 */
@Aspect
@Slf4j
public class TunnyLogAspect {

	@Around("@annotation(tunnylog)")
	public Object aroundLogApi(ProceedingJoinPoint point, com.huazheng.tunny.common.log.annotation.TunnyLog tunnylog) throws Throwable {
		String strClassName = point.getTarget().getClass().getName();
		String strMethodName = point.getSignature().getName();
		log.debug("[类名]:{},[方法]:{}", strClassName, strMethodName);

		//执行业务逻辑
		Object obj = point.proceed();

		return obj;
	}

}

# 海康威视事件接入服务

## 功能说明

本服务实现了海康威视设备的事件接入，专门处理以下两种事件类型：
1. **区域入侵检测** - 包括目标进入区域、目标离开区域、周界入侵
2. **未戴安全帽检测** - 检测人员是否佩戴安全帽

## 部署准备

### 1. SDK库文件
确保在项目根目录的 `lib` 文件夹下放置海康SDK库文件：

**Windows系统：**
- `HCNetSDK.dll`

**Linux系统：**
- `libhcnetsdk.so`
- `libcrypto.so.1.1`
- `libssl.so.1.1`

### 2. 配置文件
在 `application.yml` 或 `application-hikvision.yml` 中配置设备信息：

```yaml
hikvision:
  device:
    ip: *************      # 海康设备IP
    port: 8000             # 设备端口
    username: admin        # 设备用户名
    password: damin#2025       # 设备密码
```

## 服务特性

### 自动启动
- 服务在Spring Boot启动时自动初始化
- 自动连接海康设备并开始布防
- 支持优雅关闭，自动撤防和清理资源

### 事件处理
- 实时接收海康设备报警事件
- 自动过滤只处理目标事件类型
- 将事件数据解析为JSON格式输出到日志
- 将报警图片转换为Base64格式输出到日志

### 日志输出格式

**区域入侵事件：**
```json
{
  "eventType": "区域入侵",
  "subEventType": "目标进入区域",
  "deviceIp": "*************",
  "deviceName": "Camera01",
  "channelId": 1,
  "timestamp": "2025-08-22 20:30:15",
  "alarmTime": "2025-08-22 20:30:14",
  "detectionTarget": 1
}
```

**未戴安全帽事件：**
```json
{
  "eventType": "未戴安全帽",
  "deviceIp": "*************",
  "deviceName": "Camera01",
  "timestamp": "2025-08-22 20:30:15",
  "dataFormat": 2,
  "pictureCount": 1,
  "alarmData": "..."
}
```

**图片数据：**
```
【区域入侵事件图片】设备IP: *************, 事件类型: 目标进入区域, 图片Base64: /9j/4AAQSkZJRgABAQAAAQ...
```

## API接口

### 1. 获取设备状态
```
GET /hikvision/status
```

返回示例：
```json
{
  "success": true,
  "connected": true,
  "deviceInfo": {
    "deviceIp": "*************",
    "devicePort": 8000,
    "deviceUsername": "admin",
    "connected": true,
    "userID": 1,
    "alarmHandle": 1
  },
  "message": "设备连接正常"
}
```

### 2. 获取支持的事件类型
```
GET /hikvision/events
```

返回示例：
```json
{
  "success": true,
  "supportedEvents": {
    "areaIntrusion": "区域入侵检测",
    "helmetDetection": "未戴安全帽检测"
  },
  "message": "当前支持2种事件类型"
}
```

## 故障排查

### 1. 设备连接失败
- 检查设备IP、端口、用户名、密码是否正确
- 确认设备网络连通性
- 检查设备是否支持ISAPI协议

### 2. SDK加载失败
- 确认lib目录下有对应平台的库文件
- 检查库文件权限
- 确认JNA依赖版本正确

### 3. 事件接收异常
- 检查设备是否启用了对应的智能检测功能
- 确认设备固件版本支持相关事件类型
- 查看海康SDK日志文件

## 注意事项

1. **平台兼容性**：支持Windows和Linux平台
2. **设备要求**：需要海康威视支持ISAPI协议的设备
3. **性能考虑**：图片Base64转换可能占用较多内存，建议监控内存使用情况
4. **安全考虑**：设备密码建议加密存储，不要在配置文件中明文保存

## 扩展说明

如需支持更多事件类型，可在 `hikvisionService.processAlarmEvent()` 方法中添加相应的处理逻辑。

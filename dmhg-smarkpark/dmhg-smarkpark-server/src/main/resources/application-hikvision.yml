# 海康威视设备配置
hikvision:
  # 设备连接配置
  device:
    # 设备IP地址
    ip: *************
    # 设备端口，默认8000
    port: 8000
    # 设备用户名
    username: admin
    # 设备密码
    password: damin#2025
  
  # SDK库文件配置
  library:
    # 库文件存放路径（绝对路径，jar包外部）
    path: /usr/local/tunny/service/lib
    # 备用路径配置说明：
    # Windows: C:/hikvision/lib
    # Linux: /usr/local/tunny/service/lib 或 /opt/hikvision/lib
  
  # 日志配置
  log:
    # 海康SDK日志路径
    path: ./logs/hikvision
    # 是否启用SDK日志
    enabled: true
  
  # 报警配置
  alarm:
    # 布防等级：0-高，1-中，2-低
    level: 0
    # 布防类型：0-客户端布防，1-实时布防
    deploy-type: 0
    # 是否启用JSON和图片分离
    json-picture-separate: true
  
  # 事件过滤配置
  events:
    # 是否启用区域入侵检测
    area-intrusion-enabled: true
    # 是否启用安全帽检测
    helmet-detection-enabled: true
    # 其他事件类型（暂不启用）
    other-events-enabled: false

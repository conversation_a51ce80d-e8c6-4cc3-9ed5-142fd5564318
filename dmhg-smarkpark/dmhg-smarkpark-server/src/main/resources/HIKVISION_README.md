# 海康威视事件接入服务

## 功能说明

本服务实现了海康威视设备的事件接入和解析，专门处理以下两种事件类型：
1. **区域入侵检测** - 包括目标进入区域、目标离开区域、周界入侵
2. **未戴安全帽检测** - 检测人员是否佩戴安全帽

**注意：本服务只负责解析海康事件报文并输出到日志，不保存到数据库。**

## 部署准备

### 1. SDK库文件部署（重要）

**⚠️ 注意：打包成jar后，动态库文件必须放在服务器文件系统上，不能打包在jar内部！**

#### 自动部署脚本
```bash
# 使用提供的部署脚本
chmod +x deploy-hikvision-libs.sh
./deploy-hikvision-libs.sh
```

#### 手动部署步骤

**Linux系统：**
```bash
# 1. 创建库文件目录
sudo mkdir -p /usr/local/tunny/service/lib

# 2. 复制海康SDK库文件
sudo cp /path/to/hikvision-sdk/libhcnetsdk.so /usr/local/tunny/service/lib/
sudo cp /path/to/hikvision-sdk/libcrypto.so.1.1 /usr/local/tunny/service/lib/
sudo cp /path/to/hikvision-sdk/libssl.so.1.1 /usr/local/tunny/service/lib/

# 3. 设置执行权限
sudo chmod +x /usr/local/tunny/service/lib/libhcnetsdk.so
sudo chmod +x /usr/local/tunny/service/lib/libcrypto.so.1.1
sudo chmod +x /usr/local/tunny/service/lib/libssl.so.1.1

# 4. 验证文件
ls -la /usr/local/tunny/service/lib/
```

**Windows系统：**
```cmd
# 1. 创建库文件目录
mkdir C:\hikvision\lib

# 2. 复制海康SDK库文件
copy C:\path\to\hikvision-sdk\HCNetSDK.dll C:\hikvision\lib\

# 3. 验证文件
dir C:\hikvision\lib\
```

### 2. 配置文件
在 `application.yml` 或启动参数中配置：

```yaml
hikvision:
  # 库文件路径配置（必须）
  library:
    path: /usr/local/tunny/service/lib  # Linux
    # path: C:/hikvision/lib            # Windows

  # 设备连接配置
  device:
    ip: *************     # 海康设备IP
    port: 8000             # 设备端口
    username: admin        # 设备用户名
    password: damin#2025        # 设备密码
```

**或者使用启动参数：**
```bash
java -jar dmhg-smarkpark-server.jar --hikvision.library.path=/usr/local/tunny/service/lib
```

## 服务特性

### 自动启动
- 服务在Spring Boot启动时自动初始化
- 自动连接海康设备并开始布防
- 支持优雅关闭，自动撤防和清理资源

### 事件处理
- 实时接收海康设备报警事件
- 自动过滤只处理目标事件类型
- 将事件数据解析为JSON格式输出到日志
- 将报警图片转换为Base64格式输出到日志
- **完整的报文解析** - 解析海康JSON报文中的所有关键信息
- **智能事件识别** - 根据报文内容智能识别事件类型和详情

### 日志输出格式

**区域入侵事件：**
```json
{
  "eventType": "区域入侵",
  "subEventType": "目标进入区域",
  "deviceIp": "*************",
  "deviceName": "Camera01",
  "channelId": 1,
  "timestamp": "2025-08-22 20:30:15",
  "alarmTime": "2025-08-22 20:30:14",
  "detectionTarget": 1
}
```

**未戴安全帽事件（增强版）：**
```json
{
  "errorcode":	0,
  "version":	"2.1.0",
  "width":	"2560",
  "height":	"1440",
  "frameNum":	211066,
  "timeStamp":	8656071,
  "aitype":	0,
  "targets":	[{
    "nodeID":	5,
    "obj":	{
      "modelID":	"00012023082801005toujiansbsdet00",
      "id":	865,
      "type":	2,
      "confidence":	999,
      "valid":	1,
      "visible":	1,
      "rect":	{
        "x":	"0.492969",
        "y":	"0.642663",
        "w":	"0.071875",
        "h":	"0.141304"
      }
    },
    "properties":	[{
      "modelID":	"00022023040601005fanguangyicls00",
      "classify":	{
        "attrType":	0,
        "attrValue":	1,
        "attrConf":	453
      }
    }, {
      "modelID":	"iqav1.3.3_small",
      "scoreInfo":	[{
        "scoreType":	0,
        "scoreConf":	453
      }, {
        "scoreType":	1,
        "scoreConf":	750
      }, {
        "scoreType":	2,
        "scoreConf":	1000
      }, {
        "scoreType":	3,
        "scoreConf":	0
      }, {
        "scoreType":	4,
        "scoreConf":	5
      }, {
        "scoreType":	5,
        "scoreConf":	999
      }]
    }]
  }, {
    "nodeID":	5,
    "obj":	{
      "modelID":	"00012023082801005toujiansbsdet00",
      "id":	866,
      "type":	1,
      "confidence":	998,
      "valid":	1,
      "visible":	1,
      "rect":	{
        "x":	"0.507812",
        "y":	"0.645380",
        "w":	"0.042188",
        "h":	"0.076087"
      }
    },
    "properties":	[{
      "modelID":	"00022023040601005anquanmaomacls0",
      "classify":	{
        "attrType":	0,
        "attrValue":	0,
        "attrConf":	989
      }
    }, {
      "modelID":	"00022023040601005anquanmaomacls0",
      "classify":	{
        "attrType":	1,
        "attrValue":	5,
        "attrConf":	982
      }
    }, {
      "modelID":	"head_iqav1.0.0_small",
      "scoreInfo":	[{
        "scoreType":	0,
        "scoreConf":	378
      }, {
        "scoreType":	1,
        "scoreConf":	999
      }, {
        "scoreType":	2,
        "scoreConf":	750
      }, {
        "scoreType":	3,
        "scoreConf":	0
      }, {
        "scoreType":	4,
        "scoreConf":	3
      }]
    }]
  }],
  "events":	{
    "alertInfo":	[{
      "nodeID":	5,
      "unionTargets":	{
        "id":	0,
        "type":	0,
        "confidence":	989,
        "region":	{
          "rect":	{
            "x":	"0.507812",
            "y":	"0.645380",
            "w":	"0.042188",
            "h":	"0.076087"
          }
        },
        "childTargets":	[{
          "modelID":	"00012023082801005toujiansbsdet00",
          "id":	866
        }]
      },
      "ruleInfo":	{
        "ruleID":	1,
        "triggerType":	1073758209,
        "movDir":	0,
        "region":	{
          "polygon":	[{
            "x":	"0.010000",
            "y":	"0.010000"
          }, {
            "x":	"0.990000",
            "y":	"0.010000"
          }, {
            "x":	"0.990000",
            "y":	"0.990000"
          }, {
            "x":	"0.010000",
            "y":	"0.990000"
          }]
        },
        "ruleName":	"未戴安全帽检测"
      }
    }]
  },
  "algoVerifyInfo":	{
    "firstFrameNum":	1396,
    "alarmFrameNum":	211066,
    "targetInfo":	[{
      "targetID":	866,
      "targetType":	0,
      "confidence":	989,
      "ruleID":	1,
      "targetRect":	{
        "x":	0.507812,
        "y":	0.645380,
        "width":	0.042188,
        "height":	0.076087
      }
    }]
  }
}
```

**图片数据：**
```
【区域入侵事件图片】设备IP: *************, 事件类型: 目标进入区域, 图片Base64: /9j/4AAQSkZJRgABAQAAAQ...
【未戴安全帽事件图片】设备IP: *************, 图片序号: 0, 文件名: snapshot.jpg, 图片Base64: /9j/4AAQSkZJRgABAQAAAQ...
```

## API接口

### 1. 获取设备状态
```
GET /hikvision/status
```

返回示例：
```json
{
  "success": true,
  "connected": true,
  "deviceInfo": {
    "deviceIp": "*************",
    "devicePort": 8000,
    "deviceUsername": "admin",
    "connected": true,
    "userID": 1,
    "alarmHandle": 1
  },
  "message": "设备连接正常"
}
```

### 2. 获取支持的事件类型
```
GET /hikvision/events
```

返回示例：
```json
{
  "success": true,
  "supportedEvents": {
    "areaIntrusion": "区域入侵检测",
    "helmetDetection": "未戴安全帽检测"
  },
  "message": "当前支持2种事件类型，事件将解析并输出到日志"
}
```

## 日志输出格式

### 事件解析日志
所有接收到的海康事件都会解析并输出详细的JSON格式日志，包含：

**基础信息：**
- 设备IP和设备名称
- 事件类型和子类型
- 时间戳信息

**海康报文解析：**
- 错误码、版本号、图像尺寸
- 帧号、时间戳等技术参数
- 规则ID、规则名称、触发类型

**目标检测详情：**
- 检测目标的ID、类型、置信度
- 目标在图像中的位置坐标
- 安全帽检测结果（是否佩戴、置信度）

**图片数据：**
- 完整的Base64编码图片数据
- 图片文件名和序号信息
- 便于后续处理和存储

### 统计信息
服务提供实时的事件处理统计：
- `totalEventsReceived`: 总接收事件数
- `totalEventsProcessed`: 总处理事件数
- `processSuccessRate`: 处理成功率

## 故障排查

### 1. 设备连接失败
- 检查设备IP、端口、用户名、密码是否正确
- 确认设备网络连通性
- 检查设备是否支持ISAPI协议

### 2. SDK加载失败
**常见错误：** `java.lang.UnsatisfiedLinkError: Unable to load library`

**解决方案：**
- 确认库文件路径正确：检查配置的 `hikvision.library.path` 路径
- 确认库文件存在：`ls -la /usr/local/tunny/service/lib/libhcnetsdk.so`
- 检查库文件权限：`chmod +x /usr/local/tunny/service/lib/libhcnetsdk.so`
- 确认架构匹配：库文件必须与JVM架构匹配（32位/64位）
- 检查依赖库：确保系统有必要的依赖库
- 确认JNA依赖版本正确

### 3. 事件接收异常
- 检查设备是否启用了对应的智能检测功能
- 确认设备固件版本支持相关事件类型
- 查看海康SDK日志文件

## 注意事项

1. **平台兼容性**：支持Windows和Linux平台
2. **设备要求**：需要海康威视支持ISAPI协议的设备
3. **性能考虑**：图片Base64转换可能占用较多内存，建议监控内存使用情况
4. **安全考虑**：设备密码建议加密存储，不要在配置文件中明文保存



## 使用建议

1. **监控日志**：实时观察日志输出，确认事件接收和解析正常
2. **性能监控**：注意图片Base64转换的内存使用情况
3. **网络稳定性**：确保与海康设备的网络连接稳定
4. **事件过滤**：根据业务需求调整事件过滤条件
5. **日志管理**：建议配置日志轮转，避免日志文件过大

## 扩展说明

如需支持更多事件类型，可在 `hikvisionService.processAlarmEvent()` 方法中添加相应的处理逻辑。
如需将事件数据保存到数据库或推送到其他系统，可以在事件处理方法中添加相应的业务逻辑。

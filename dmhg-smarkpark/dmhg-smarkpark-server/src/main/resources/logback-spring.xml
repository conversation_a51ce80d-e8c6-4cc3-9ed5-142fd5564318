<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <contextName>dmhg-smarkpark</contextName>
    <!--输出文件路径-->
    <property name="log.path" value="../logs/dmhg-smarkpark.log"/>
    <!--输出到控制台-->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} %contextName [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!--输出到文件-->
    <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}</file>
        <!--日志滚动策略，一天切割一次-->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>../logs/logback.%d{yyyy-MM-dd}.log</fileNamePattern>

            <!--只保留最近15天的日志-->
            <maxHistory>15</maxHistory>
            <!--用来指定日志文件的上限大小，那么到了这个值，就会删除旧的日志-->
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <!-- 日志输出编码格式-->
        <encoder>
            <charset>UTF-8</charset>
            <pattern>%d{HH:mm:ss.SSS} %contextName [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>



    <root level="info">
        <appender-ref ref="console"/>
        <appender-ref ref="file"/>
    </root>

    <!-- 测试环境+开发环境. 多个使用逗号隔开. -->
    <!-- 测试环境+开发环境. 多个使用逗号隔开. -->
    <springProfile name="test,dev">
        <logger name="com.huazheng" level="info" additivity="true" >
            <appender-ref ref="console"/>
        </logger>
    </springProfile>

    <!-- 生产环境可设置成info 或error 输出到Plumelog-->
    <springProfile name="prod,pro">
        <root level="info">
            <appender-ref ref="file"/>
        </root>
    </springProfile>


</configuration>
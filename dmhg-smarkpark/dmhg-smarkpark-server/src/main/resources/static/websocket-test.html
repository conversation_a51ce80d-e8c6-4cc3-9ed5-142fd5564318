<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>告警事件 WebSocket 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .message-area {
            height: 300px;
            border: 1px solid #ddd;
            padding: 10px;
            overflow-y: auto;
            background-color: #f8f9fa;
            margin: 10px 0;
        }
        .message {
            margin: 5px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .message.received {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        .message.sent {
            background-color: #f3e5f5;
            border-left: 4px solid #9c27b0;
        }
        .message.system {
            background-color: #fff3e0;
            border-left: 4px solid #ff9800;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        input[type="text"] {
            width: 200px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
        .alarm-event {
            background-color: #fff8e1;
            border: 2px solid #ffc107;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
        }
        .alarm-event h4 {
            color: #e65100;
            margin: 0 0 10px 0;
        }
        .timestamp {
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>告警事件 WebSocket 测试</h1>
        
        <div id="status" class="status disconnected">未连接</div>
        
        <div>
            <input type="text" id="clientId" placeholder="客户端ID" value="test-client-001">
            <button id="connectBtn" onclick="connect()">连接</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>断开连接</button>
        </div>
        
        <div>
            <input type="text" id="messageInput" placeholder="发送消息">
            <button onclick="sendMessage()" disabled id="sendBtn">发送消息</button>
        </div>
        
        <h3>消息记录</h3>
        <div id="messages" class="message-area"></div>
        
        <button onclick="clearMessages()">清空消息</button>
        <button onclick="getWebSocketStatus()">获取连接状态</button>
    </div>

    <script>
        let websocket = null;
        let clientId = 'test-client-001';
        
        function connect() {
            clientId = document.getElementById('clientId').value || 'test-client-001';
            
            if (websocket != null) {
                addMessage('系统', '已经连接，请先断开', 'system');
                return;
            }
            
            // 构建WebSocket URL
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const host = window.location.host;
            const wsUrl = `${protocol}//${host}/websocket/alarm/${clientId}`;
            
            addMessage('系统', `正在连接到: ${wsUrl}`, 'system');
            
            websocket = new WebSocket(wsUrl);
            
            websocket.onopen = function(event) {
                updateStatus(true);
                addMessage('系统', '连接成功', 'system');
                document.getElementById('connectBtn').disabled = true;
                document.getElementById('disconnectBtn').disabled = false;
                document.getElementById('sendBtn').disabled = false;
            };
            
            websocket.onmessage = function(event) {
                try {
                    // 尝试解析为JSON（告警事件）
                    const data = JSON.parse(event.data);
                    if (data.alertId) {
                        // 这是告警事件
                        addAlarmEvent(data);
                    } else {
                        addMessage('服务器', event.data, 'received');
                    }
                } catch (e) {
                    // 普通文本消息
                    addMessage('服务器', event.data, 'received');
                }
            };
            
            websocket.onclose = function(event) {
                updateStatus(false);
                addMessage('系统', '连接已关闭', 'system');
                document.getElementById('connectBtn').disabled = false;
                document.getElementById('disconnectBtn').disabled = true;
                document.getElementById('sendBtn').disabled = true;
                websocket = null;
            };
            
            websocket.onerror = function(event) {
                addMessage('系统', '连接错误', 'system');
                console.error('WebSocket错误:', event);
            };
        }
        
        function disconnect() {
            if (websocket != null) {
                websocket.close();
            }
        }
        
        function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();
            
            if (message === '') {
                alert('请输入消息内容');
                return;
            }
            
            if (websocket != null && websocket.readyState === WebSocket.OPEN) {
                websocket.send(message);
                addMessage('客户端', message, 'sent');
                messageInput.value = '';
            } else {
                alert('WebSocket未连接');
            }
        }
        
        function updateStatus(connected) {
            const statusDiv = document.getElementById('status');
            if (connected) {
                statusDiv.textContent = `已连接 (客户端ID: ${clientId})`;
                statusDiv.className = 'status connected';
            } else {
                statusDiv.textContent = '未连接';
                statusDiv.className = 'status disconnected';
            }
        }
        
        function addMessage(sender, content, type) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            messageDiv.innerHTML = `
                <strong>${sender}:</strong> ${content}
                <div class="timestamp">${timestamp}</div>
            `;
            
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
        
        function addAlarmEvent(alarmEvent) {
            const messagesDiv = document.getElementById('messages');
            const alarmDiv = document.createElement('div');
            alarmDiv.className = 'alarm-event';
            
            const timestamp = new Date().toLocaleTimeString();
            alarmDiv.innerHTML = `
                <h4>🚨 告警事件</h4>
                <p><strong>告警ID:</strong> ${alarmEvent.alertId || '未知'}</p>
                <p><strong>设备ID:</strong> ${alarmEvent.deviceId || '未知'}</p>
                <p><strong>设备名称:</strong> ${alarmEvent.deviceName || '未知'}</p>
                <p><strong>告警类别:</strong> ${alarmEvent.alertCategory || '未知'}</p>
                <p><strong>事件状态:</strong> ${alarmEvent.eventStatus || '未知'}</p>
                <p><strong>上报时间:</strong> ${alarmEvent.reportTime || '未知'}</p>
                <div class="timestamp">接收时间: ${timestamp}</div>
            `;
            
            messagesDiv.appendChild(alarmDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
        
        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }
        
        function getWebSocketStatus() {
            fetch('/alarmEvent/websocket/status')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        const status = data.data;
                        addMessage('系统', `在线客户端数量: ${status.onlineCount}, 连接的客户端: [${Array.from(status.connectedClients).join(', ')}]`, 'system');
                    } else {
                        addMessage('系统', '获取状态失败: ' + data.msg, 'system');
                    }
                })
                .catch(error => {
                    addMessage('系统', '获取状态异常: ' + error.message, 'system');
                });
        }
        
        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 回车发送消息
            document.getElementById('messageInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
        });
    </script>
</body>
</html>

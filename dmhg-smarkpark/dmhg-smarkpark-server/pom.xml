<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.huazheng</groupId>
        <artifactId>dmhg-smarkpark</artifactId>
        <version>1.3.2</version>
    </parent>

    <artifactId>dmhg-smarkpark-server</artifactId>
    <version>1.3.2</version>
    <packaging>jar</packaging>

    <name>dmhg-smarkpark-server</name>
    <description>dmhg 通用basic业务处理模块</description>

    <dependencies>
        <!--basic api、model 模块-->
        <dependency>
            <groupId>com.huazheng</groupId>
            <artifactId>dmhg-smarkpark-client</artifactId>
            <version>1.3.2</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>

        <!--websocket-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>

        <!--mybatis-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mbp.boot.version}</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <!--common-->
        <dependency>
            <groupId>com.huazheng</groupId>
            <artifactId>qdydyyq-common-core</artifactId>
            <version>1.3.2</version>
        </dependency>
        <!--swagger 内置安全模块-->
        <dependency>
            <groupId>com.huazheng</groupId>
            <artifactId>qdydyyq-common-swagger</artifactId>
            <version>1.3.2</version>
            <exclusions>
                <!--为安全考虑移除actuator监控-->
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-actuator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--日志处理-->
        <dependency>
            <groupId>com.huazheng</groupId>
            <artifactId>qdydyyq-common-log</artifactId>
            <version>1.3.2</version>
        </dependency>

        <!--web 模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <!--排除tomcat依赖-->
                <exclusion>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>


        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.26</version>
        </dependency>

        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20160810</version>
        </dependency>

        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
            <version>3.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
            <version>4.4.5</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore-nio</artifactId>
            <version>4.4.5</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpasyncclient</artifactId>
            <version>4.1.2</version>
        </dependency>


        <!-- Examples JAR -->
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>examples</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- JNA JAR -->
        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna</artifactId>
            <version>5.12.1</version>
        </dependency>
        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna-platform</artifactId>
            <version>5.12.1</version>
        </dependency>

        <!-- JSON JAR -->
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>1.0.0</version>
        </dependency>


    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>

        <resources>

        </resources>
        <!--<finalName>${project.artifactId}-${maven.build.timestamp}</finalName>-->
    </build>
    <properties>
        <!--自定义默认的编码格式-->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <!--自定义默认的时间格式 年-月-日-时-分-->
        <maven.build.timestamp.format>
            yyyy-MM-dd_HH_mm
        </maven.build.timestamp.format>
    </properties>

</project>
